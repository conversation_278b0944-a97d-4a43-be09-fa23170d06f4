package com.ruoyi.cache.utils;

import com.ruoyi.cache.bean.DeptData;
import com.ruoyi.cache.bean.DictData;
import com.ruoyi.cache.bean.RlctConfigData;
import com.ruoyi.cache.loader.CacheLoader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class CacheRlctConfigUtils {
    private static final Logger logger = LoggerFactory.getLogger(CacheRlctConfigUtils.class);


    public static RlctConfigData getRlctConfigById(Long id){
        if(id == null){
            return null;
        }
        return CacheLoader.rlctConfigDataMap.get(id);
    }
}
