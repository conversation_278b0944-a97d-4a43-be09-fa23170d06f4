package com.ruoyi.cache.loader;

import com.ruoyi.cache.bean.DeptData;
import com.ruoyi.cache.bean.DictData;
import com.ruoyi.cache.bean.RlctConfigData;
import com.ruoyi.cache.mapper.CacheManageMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: zhaojf
 * @Description TODO
 * @date 2021/6/25
 */
@Service
public class CacheLoader {
    private static final Logger logger = LoggerFactory.getLogger(CacheLoader.class);

    //字典
    public static final Map<String, List<DictData>> dictDataMap = new HashMap<>();


    //部门
    public static final List<DeptData> deptDataList = new ArrayList<>();
    public static final Map<Long, DeptData> deptDataMap = new HashMap<>();

    public static final Map<Long, RlctConfigData> rlctConfigDataMap = new HashMap<>();

    @Autowired
    private CacheManageMapper cacheDictMapper;


    @Scheduled(cron = "0 0/30 * * * ?")
    @PostConstruct
    public void init() {
        logger.info("-CacheLoader init--start--");
        loadAllDictData();
        loadAllDeptData();
        loadAllRlctConfigData();
        logger.info("-CacheLoader init--end--");
    }

    public void loadAllDictData() {
        List<DictData> list = cacheDictMapper.selectAllDictData();
        Map<String, List<DictData>> dictDataMapTmp = list.stream().collect(Collectors.groupingBy(DictData::getDictType));
        synchronized (dictDataMap) {
            dictDataMap.clear();
            dictDataMap.putAll(dictDataMapTmp);
        }
    }

    public void loadAllDeptData() {
        List<DeptData> list = cacheDictMapper.selectAllDeptData();
        Map<Long, DeptData> deptDataMapTmp = list.stream().collect(Collectors.toMap(DeptData::getDeptId, Function.identity()));
        synchronized (deptDataList) {
            deptDataList.clear();
            deptDataList.addAll(list);
            deptDataMap.clear();
            deptDataMap.putAll(deptDataMapTmp);
        }
    }

    public void loadAllRlctConfigData() {
        List<RlctConfigData> list = cacheDictMapper.selectAllRlctConfigData();
        Map<Long, RlctConfigData> deptDataMapTmp = list.stream().collect(Collectors.toMap(RlctConfigData::getId, Function.identity()));
        synchronized (deptDataList) {
            rlctConfigDataMap.clear();
            rlctConfigDataMap.putAll(deptDataMapTmp);
        }
    }
}
