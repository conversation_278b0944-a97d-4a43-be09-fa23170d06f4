package com.ruoyi.web.controller.vo;

import com.ruoyi.common.core.domain.entity.SysUser;

import java.io.Serializable;

public class LoginResUser implements Serializable {
    private static final long serialVersionUID = 1L;
    private String token;
    private SysUser sysUser;
    private String loginType;

    public LoginResUser(){}

    public LoginResUser(String token, SysUser sysUser) {
        this.token = token;
        this.sysUser = sysUser;

        //用户类型（00系统用户 11医师 22患者 33导诊）
//        if(sysUser.getUserType().equals("22")){
//            loginType = "patient";
//        }else if (sysUser.getUserType().equals("11")){
//            loginType = "physician";
//        }

        if(sysUser.getUserType().equals("22")){
            loginType = "patient";
        }else{
            loginType = "physician";
        }
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public SysUser getSysUser() {
        return sysUser;
    }

    public void setSysUser(SysUser sysUser) {
        this.sysUser = sysUser;
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }
}
