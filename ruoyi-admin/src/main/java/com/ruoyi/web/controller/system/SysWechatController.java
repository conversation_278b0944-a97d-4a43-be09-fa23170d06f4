package com.ruoyi.web.controller.system;


import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.model.PhoneParam;
import com.ruoyi.common.core.domain.model.PhysicianRegisterBody;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysWechatService;
import com.ruoyi.teen.utils.ValidateUtils;
import com.ruoyi.web.controller.vo.LoginResUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


/**
 * 微信小程序登录接口
 *
 * @Author: Qiuweihuang
 * @Date: 2023/11/20
 **/
@RestController
@RequestMapping("/weixin")
public class SysWechatController extends BaseController {
    @Autowired
    private ISysWechatService wechatService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private ISysUserService userService;

    /**
     * 微信登录接口
     *
     * @param code 用户授权码
     * @param type 用户类型 patient家长   physician医师
     * @return 操作结果
     * @throws IOException 发送请求或解析响应发生异常时抛出
     */
    @Anonymous
    @PostMapping("/wxLogin")
    public AjaxResult getWechatLoginInfo(String code) throws Exception {
        //参数校验
        ValidateUtils.checkString(code, "用户授权码");

        //获取返回信息
        SysUser sysUser = wechatService.handleWechatLogin(code);

        //获取微信登录后的token
        LoginUser loginUser = new LoginUser();

        //增加微信登录后的token时间  从登录开始时间 23小时59分 内不需要重新登录
//        tokenService.originalExpireTime(30 * 24 * 60);
        loginUser.setUser(sysUser);
        String token = tokenService.createToken(loginUser);
        loginUser.setToken(token);

        // 添加这行日志↓↓↓
        logger.info("生成的Token: " + token + ", 用户ID: " + sysUser.getUserId());
        // 返回操作成功
        return AjaxResult.success("登录成功",new LoginResUser(token,sysUser));
    }

    /**
     * 根据微信登录code返回绑定的手机号-医师端
     *
     * @param param
     * @return
     */
    @PostMapping("/physician/getPhone")
    @Anonymous
    public AjaxResult getPhoneByCodeForPhysician(@RequestBody PhoneParam param) {
        String phone = wechatService.getPhoneByCodeForPhysician(param.getWxCode());
        Map<String, String> resMap = new HashMap<>();
        if (StringUtils.isNotBlank(phone)) {
            resMap.put("phone", phone);
        }
        return success(resMap);
    }

    /**
     * 根据微信登录code返回绑定的手机号-家长端
     *
     * @param param
     * @return
     */
    @PostMapping("/patient/getPhone")
    @Anonymous
    public AjaxResult getPhoneByCodeForPatient(@RequestBody PhoneParam param) {
        String phone = wechatService.getPhoneByCodeForPatient(param.getWxCode());
        Map<String, String> resMap = new HashMap<>();
        if (StringUtils.isNotBlank(phone)) {
            resMap.put("phone", phone);
        }
        return success(resMap);
    }

}



































