package com.ruoyi.web.controller.system;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.sms.client.SmsClient;
import com.ruoyi.sms.service.MessageService;
import com.ruoyi.system.service.ISysLoginService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysWechatService;
import com.ruoyi.teen.utils.ValidateUtils;
import com.ruoyi.web.controller.tool.ValidateCodeUtils;
import com.ruoyi.web.controller.vo.LoginResUser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 手机号获取验证码登录
 * <AUTHOR>
 */
@RestController
public class LoginController
{
    @Autowired
    private TokenService tokenService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysWechatService wechatService;
    @Autowired
    private ISysLoginService loginService;

    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);


    /**
     * 获取验证码
     * @param phone
     * @return
     */
    @PostMapping("/codeInfo")
    @Anonymous
    public AjaxResult codeInfo(String phone){
        try{
            ValidateUtils.checkString(phone,"手机号");
            String code= String.valueOf(ValidateCodeUtils.generateValidateCode(4));
            deleteCode(phone);
            checkPhone(phone);
            redisCache.setCacheObject("code_"+phone,code,10,TimeUnit.MINUTES);
            redisCache.setCacheObject("time_"+phone,"1",1,TimeUnit.MINUTES);

            String resCode = redisCache.getCacheObject("code_"+phone).toString();
            System.out.println("resCode="+resCode);
            SmsClient.sendSmsCode(phone,code);
            logger.info("验证码:{}",code);
        }catch (Exception e){
            return AjaxResult.error(e);
        }
        return AjaxResult.success();
    }

    /**
     * 删除上一次手机号获取的验证码
     * @param phone
     */
    public void deleteCode(String phone){
        // 删除上一次验证码
        String lastCode = redisCache.getCacheObject("code_" + phone);
        if (lastCode != null) {
            redisCache.deleteObject("code_" + phone);
        }
    }

    /**
     * 校验手机号获取验证码
     * @param phone
     * @return
     * @throws RuntimeException
     */
    public void checkPhone(String phone) throws RuntimeException{
        String Error_msg;
        String timePhone = redisCache.getCacheObject("time_"+phone);
        if(timePhone!=null){
            Error_msg="60s内不能获取验证码";
            throw new RuntimeException(Error_msg);
        }
    }

    /**
     * 校验验证码
     * @param phone
     * @return
     * @throws RuntimeException
     */
    public void checkCode(String phone,String code) throws RuntimeException{
        ValidateUtils.checkString(code,"验证码");
        if(code.equals("8899")){//用于调试
            return;
        }
        String Error_msg;
        String resdisCode = redisCache.getCacheObject("code_"+phone).toString();
        System.out.println("resdisCode2="+resdisCode);
        System.out.println("code="+code);
        if(!resdisCode.equals(code)){
            Error_msg="验证码错误";
            throw new RuntimeException(Error_msg);
        }
    }

//    /**
//     * 手机号验证码登录验证（待删除）
//     * @param phoneNumber
//     * @param code
//     * @return
//     */
//    @Anonymous
//    @PostMapping("/phoneLogin")
//    public AjaxResult LoginInfo(String phoneNumber, String code,String type){
//        try{
//            String Token=null;
//            // 检查用户是否已存在，如果不存在，则创建新用户
//            SysUser sysUser = new SysUser();
//            sysUser.setPhonenumber(phoneNumber);
//            boolean phoneInfo=false;
//            checkCode(phoneNumber,code);//校验验证码
//            if ("patient".equals(type)){
//                // 如果是家长
//                phoneInfo= userService.checkPhoneUnique(sysUser);
//                if (phoneInfo) {
//                    createUserIfNotExist(phoneNumber,type);
//                }
//                sysUser = userService.selectOne(phoneNumber,type);
//                if(sysUser == null || !sysUser.getUserType().equals("22")){
//                    throw new RuntimeException("该用户非家长");
//                }
//            }
//            // 如果是医师
//            if ("physician".equals(type)){
//                // 如果是医师
//                sysUser = userService.selectOne(phoneNumber,type);
//                if(sysUser == null){
//                    throw new RuntimeException("用户未注册");
//                }
//                if(!sysUser.getStatus().equals("0")){
//                    throw new RuntimeException("账号审核中或已停用");
//                }
//            }
//            LoginUser loginUser = new LoginUser();
//            loginUser.setUser(sysUser);
//            Token =  tokenService.createToken(loginUser);
//            // 删除验证码缓存
//            redisCache.deleteObject("code_" + phoneNumber);
//            redisCache.deleteObject("time_" + phoneNumber);
//            // 返回操作成功
//            return AjaxResult.success(Token,sysUser);
//        }catch (Exception e){
//            return AjaxResult.error(e);
//        }
//    }


    /**
     * 手机号验证码登录验证,带短信验证码的
     * @param phoneNumber
     * @param code
     * @return
     */
    @Anonymous
    @PostMapping("/phoneLogin/patientOrPhysician")
    public AjaxResult LoginInfoPatientOrPhysician(String phoneNumber, String wxCode,String phoneCode,String type){
        try{
            ValidateUtils.checkString(phoneNumber,"phoneNumber");
            ValidateUtils.checkString(wxCode,"wxCode");
            ValidateUtils.checkString(phoneCode,"phoneCode");

            checkCode(phoneNumber,phoneCode);

            // 检查用户是否已存在，如果不存在，则创建新用户

            String openId = wechatService.getOpenIdByCodeForPatient(wxCode);

            // 家长
            SysUser sysUser = loginService.getLoginUser(phoneNumber,openId);

            LoginUser loginUser = new LoginUser();
            loginUser.setUser(sysUser);
            String token =  tokenService.createToken(loginUser);

            // 返回操作成功
            return AjaxResult.success("登录成功",new LoginResUser(token,sysUser));
        }catch (Exception e){
            return AjaxResult.error(e);
        }
    }

//    /**
//     * 手机号验证码登录验证,带短信验证码的
//     * @param phoneNumber
//     * @param code
//     * @return
//     */
//    @Anonymous
//    @PostMapping("/phoneLogin/patient")
//    public AjaxResult LoginInfoPatient(String phoneNumber, String wxCode,String phoneCode,String type){
//        try{
//            ValidateUtils.checkString(phoneNumber,"phoneNumber");
//            ValidateUtils.checkString(wxCode,"wxCode");
//            ValidateUtils.checkString(phoneCode,"phoneCode");
//
//            checkCode(phoneNumber,phoneCode);
//
//            String Token=null;
//            // 检查用户是否已存在，如果不存在，则创建新用户
//
//            String openId = wechatService.getOpenIdByCodeForPatient(wxCode);
//
//            // 家长
//            SysUser sysUser = userService.selectOne(phoneNumber,null);
//
//            if(sysUser == null){
//                sysUser = createUserIfNotExist(phoneNumber,"patient");
//            } else if (!sysUser.getUserType().equals("22")) {
//                throw new RuntimeException("该用户非家长");
//            }
//
//            if(!sysUser.getStatus().equals("0")){
//                throw new RuntimeException("该账号已停用");
//            }
//
//
//            if(StringUtils.isNotBlank(sysUser.getOpenid())){
//                if(!openId.equalsIgnoreCase(sysUser.getOpenid())){
//                    throw new RuntimeException("该手机号非当前绑定微信号");
//                }
//            }else{
//                //当前用户没有opnId,更新openId
//                userService.updateOpenId(sysUser.getUserId(),openId);
//            }
//
//            LoginUser loginUser = new LoginUser();
//            loginUser.setUser(sysUser);
//            Token =  tokenService.createToken(loginUser);
//            // 返回操作成功
//            return AjaxResult.success(Token,sysUser);
//        }catch (Exception e){
//            return AjaxResult.error(e);
//        }
//    }


    /**
     * 手机号验证码登录验证
     * @param phoneNumber
     * @param code
     * @return
     */
//    @Anonymous
//    @PostMapping("/phoneLogin/patientOld")
//    public AjaxResult LoginInfoPatientOld(String phoneNumber, String code,String type){
//        try{
//            ValidateUtils.checkString(phoneNumber,"phoneNumber");
//            ValidateUtils.checkString(code,"code");
//
//            String Token=null;
//            // 检查用户是否已存在，如果不存在，则创建新用户
//
//            String openId = wechatService.getOpenIdByCodeForPatient(code);
//
//            // 家长
//            SysUser sysUser = userService.selectOne(phoneNumber,null);
//
//            if(sysUser == null){
//                sysUser = createUserIfNotExist(phoneNumber,"patient");
//            } else if (!sysUser.getUserType().equals("22")) {
//                throw new RuntimeException("该用户非家长");
//            }
//
//            if(!sysUser.getStatus().equals("0")){
//                throw new RuntimeException("该账号已停用");
//            }
//
//
//            if(StringUtils.isNotBlank(sysUser.getOpenid())){
//                if(!openId.equalsIgnoreCase(sysUser.getOpenid())){
//                    throw new RuntimeException("该手机号非当前绑定微信号");
//                }
//            }else{
//                //当前用户没有opnId,更新openId
//                userService.updateOpenId(sysUser.getUserId(),openId);
//            }
//
//            LoginUser loginUser = new LoginUser();
//            loginUser.setUser(sysUser);
//            Token =  tokenService.createToken(loginUser);
//            // 返回操作成功
//            return AjaxResult.success(Token,sysUser);
//        }catch (Exception e){
//            return AjaxResult.error(e);
//        }
//    }

    @Anonymous
    @PostMapping("/phoneLogin/physician")
    public AjaxResult LoginInfoPhysician(String phoneNumber, String code,String type){
        try{
            ValidateUtils.checkString(phoneNumber,"phoneNumber");
            ValidateUtils.checkString(code,"code");
            String Token=null;
            // 检查用户是否已存在，如果不存在，则创建新用户

            String openId = wechatService.getOpenIdByCodeForPhysician(code);

            // 如果是医师
            SysUser sysUser = userService.selectOne(phoneNumber,"physician");

            if(sysUser == null){
                throw new RuntimeException("用户未注册");
            }
            if(!sysUser.getStatus().equals("0")){
                throw new RuntimeException("账号审核中或已停用");
            }

            if(StringUtils.isNotBlank(sysUser.getOpenid())){
                if(!openId.equalsIgnoreCase(sysUser.getOpenid())){
                    throw new RuntimeException("该手机号非当前绑定微信号");
                }
            }else{
                //当前用户没有opnId,更新openId
                userService.updateOpenId(sysUser.getUserId(),openId);
            }

            LoginUser loginUser = new LoginUser();
            loginUser.setUser(sysUser);
            Token =  tokenService.createToken(loginUser);

            // 返回操作成功
            return AjaxResult.success(Token,sysUser);
        }catch (Exception e){
            return AjaxResult.error(e);
        }
    }

}
