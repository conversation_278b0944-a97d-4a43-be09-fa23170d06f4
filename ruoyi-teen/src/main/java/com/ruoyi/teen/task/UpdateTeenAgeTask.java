package com.ruoyi.teen.task;

import com.ruoyi.cache.loader.CacheLoader;
import com.ruoyi.teen.service.IRlctConfigService;
import com.ruoyi.teen.service.IRlctTeenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
public class UpdateTeenAgeTask {
    private static final Logger logger = LoggerFactory.getLogger(UpdateTeenAgeTask.class);
    @Autowired
    private IRlctTeenService rlctTeenService;

//    @Scheduled(cron = "0 0/1 * * * ?")
    //每天2:10分运行
    @Scheduled(cron="0 10 2 * * ?")
    @PostConstruct
    public void run() {
        logger.info("-UpdateTeenAgeTask run--start--");
        rlctTeenService.updateTeenAge();
        logger.info("-UpdateTeenAgeTask run--end--");
    }
}
