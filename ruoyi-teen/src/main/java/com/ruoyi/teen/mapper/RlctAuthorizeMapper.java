package com.ruoyi.teen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.teen.domain.RlctAuthorize;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 授权Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface RlctAuthorizeMapper extends BaseMapper<RlctAuthorize>
{
    /**
     * 查询授权
     *
     * @param id 授权主键
     * @return 授权
     */
    public RlctAuthorize selectRlctAuthorizeById(Long id);
    public RlctAuthorize selectByUserId(@Param("userId") Long userId);
    public RlctAuthorize selectByCode(@Param("code") String code);

    /**
     * 查询授权列表
     *
     * @param rlctAuthorize 授权
     * @return 授权集合
     */
    public List<RlctAuthorize> selectRlctAuthorizeList(RlctAuthorize rlctAuthorize);

    /**
     * 新增授权
     *
     * @param rlctAuthorize 授权
     * @return 结果
     */
    public int insertRlctAuthorize(RlctAuthorize rlctAuthorize);

    /**
     * 修改授权
     *
     * @param rlctAuthorize 授权
     * @return 结果
     */
    public int updateRlctAuthorize(RlctAuthorize rlctAuthorize);
    public int updateByUserId(RlctAuthorize rlctAuthorize);

    /**
     * 删除授权
     *
     * @param id 授权主键
     * @return 结果
     */
    public int deleteRlctAuthorizeById(Long id);

    /**
     * 批量删除授权
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctAuthorizeByIds(Long[] ids);
}
