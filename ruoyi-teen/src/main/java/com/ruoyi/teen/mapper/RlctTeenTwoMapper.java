package com.ruoyi.teen.mapper;

import java.util.List;

import com.ruoyi.teen.domain.*;
import com.ruoyi.teen.vo.ipad.RecordListVo;
import org.apache.ibatis.annotations.Param;

/**
 * 孩子Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface RlctTeenTwoMapper
{
    /**
     * 查询孩子
     *
     * @param parent_teen_id 家长于孩子关系主键
     * @return 孩子
     */
    public RlctTeenTwo selectRlctTeenTwoByTeenId(Long parent_teen_id);


    /**
     * 新增孩子
     *
     * @param rlctTeenDTO 孩子
     * @return 结果
     */
    public Long insertRlctTeenTwo(RlctTeenDTO rlctTeenDTO);




    List<ProductUse> selectProductUseList(Long parent_teen_id);

    /*查询剩余套餐次数*/
    RlctUserPackageVTwo selectRlctUserPackage(Long parent_teen_id);

    List<RlctBoneAgeRecord> selectBoneAgeRecordByParentTeenId(@Param("parentTeenId") Long parentTeenId);

    RlctTeenTwo selectRlctTeenTwoInitByTeenId(Long parentTeenId);

    int insertRlctParentTeen(RlctParentTeen rlctParentTeen);

    List<RecordListVo> selectDistinctTeenIds(@Param("phonenumber") String phonenumber, @Param("nameCh") String nameCh, @Param("keyword") String keyword);


    RecordListVo selectLatestVisitByTeenId(@Param("parentTeenId")Long parentTeenId);
}
