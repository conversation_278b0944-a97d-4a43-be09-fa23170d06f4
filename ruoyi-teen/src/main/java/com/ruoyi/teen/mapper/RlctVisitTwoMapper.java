package com.ruoyi.teen.mapper;

import java.util.List;

import com.ruoyi.teen.domain.*;
import com.ruoyi.teen.vo.ipad.RlctThisTimeVisitVo;
import com.ruoyi.teen.vo.ipad.RlctVisitVo;
import com.ruoyi.teen.vo.ipad.TreatmentVo;
import org.apache.ibatis.annotations.Param;

/**
 * 就诊记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface RlctVisitTwoMapper
{



    RlctVisitTwo selectRlctVisitTwoLastTime(Long teenId);


    List<RlctVisitVo> selectVisitsByParentTeenId(Long teenId);


    /*查询数据库已有的治疗工作项目*/
    List<TreatmentDTO> selectTreatmentsByVisitId(Long visitId);

//    Long selectRlctVisitTwoByStatus(@Param("parent_teen_id") Long parent_teen_id, @Param("number") Long number);
//    List<RlctThisTimeVisitVo> selectRlctVisitTwoCurrent(@Param("parent_teen_id") Long parent_teen_id);

    /*根据就诊id查询信息*/
    RlctThisTimeVisitVo selectRlctThisTimeVisitTwoByVisitId(Long visitId);

    List<TreatmentVo> selectTreatmentsByVisitIdAndUserId(@Param("visitId") Long visitId,@Param("userId") Long userId);

    RlctVisit selectRlctVisitByStatus(@Param("parent_teen_id") Long parent_teen_id, @Param("number") Long number);

    /*
        查询指定医师和类型的就诊记录ID
     * @param visitId 就诊ID
     * @param doctorId 医师ID
     * @param treatmentType 治疗类型
     * @return 存在的就诊记录ID（未删除的），不存在返回null
    * */
    Long selectVisitIdByDoctorAndType( @Param("visitId") Long visitId,
                                @Param("doctorId") Long doctorId,
                                @Param("treatmentType") String treatmentType);

    /*
    查询指定医师和类型的就诊记录ID  返回一个对象接收用于填写治疗时间
 * @param visitId 就诊ID
 * @param doctorId 医师ID
 * @param treatmentType 治疗类型
 * @return 存在的就诊记录ID（未删除的），不存在返回null
* */
    RlctDoctorVisic selectVisitIdByDoctorAndTypes(@Param("visitId") Long visitId,
                                                  @Param("doctorId") Long doctorId,
                                                  @Param("treatmentType") String treatmentType);

    // 查询当前就诊记录的已使用的产品
    RlctProductUse selectProductUseByVisitId(Long visitId);
}
