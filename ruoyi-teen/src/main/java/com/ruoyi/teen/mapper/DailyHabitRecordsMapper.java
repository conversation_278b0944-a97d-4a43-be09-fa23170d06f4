package com.ruoyi.teen.mapper;

import java.util.List;
import com.ruoyi.teen.domain.DailyHabitRecords;
import com.ruoyi.teen.vo.ipad.DailyHabitRecordsVo;
import org.apache.ibatis.annotations.Param;

/**
 * 日常行为习惯登记Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface DailyHabitRecordsMapper
{

    /*
    * 新建就诊获取的日常习惯登记列表
    *
    * */
    public DailyHabitRecordsVo selectDailyHabitRecordsParentTeenIdList(@Param("parent_teen_id") Long parent_teen_id);

    /**
     * 新增日常行为习惯登记
     *
     * @param dailyHabitRecords 日常行为习惯登记
     * @return 结果
     */
    public int insertDailyHabitRecords(DailyHabitRecords dailyHabitRecords);

    DailyHabitRecordsVo selectDailyHabitRecordsByVisitId(@Param("visitId") Long visitId);

    /*更新日常行为习惯登记*/
    int updateDailyHabitRecordsByhabitId(DailyHabitRecords record);

    /*根据就诊id搜索日常行为主键*/
    Long selectDailyHabitRecordsHabitIdByVisitId(@Param("visitId") Long visitId);

}
