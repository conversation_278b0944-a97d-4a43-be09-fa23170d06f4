package com.ruoyi.teen.mapper;

import java.util.List;
import com.ruoyi.teen.domain.RlctPackages;
import com.ruoyi.teen.vo.ipad.PackageBuyTimesVo;
import com.ruoyi.teen.vo.ipad.RlctPackagesVo;
import org.apache.ibatis.annotations.Param;

/**
 * 套餐Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctPackagesMapper
{
    /**
     * 查询套餐
     *
     * @param packageId 套餐主键
     * @return 套餐
     */
    public RlctPackages selectRlctPackagesByPackageId(@Param("packageId") Long packageId);

    /**
     * 查询套餐列表
     *
     * @param rlctPackages 套餐
     * @return 套餐集合
     */
    public List<RlctPackages> selectRlctPackagesList(RlctPackages rlctPackages);

    /**
     * 新增套餐
     *
     * @param rlctPackages 套餐
     * @return 结果
     */
    public int insertRlctPackages(RlctPackages rlctPackages);

    /**
     * 修改套餐
     *
     * @param rlctPackages 套餐
     * @return 结果
     */
    public int updateRlctPackages(RlctPackages rlctPackages);

    /**
     * 删除套餐
     *
     * @param packageId 套餐主键
     * @return 结果
     */
    public int deleteRlctPackagesByPackageId(Long packageId);

    /**
     * 批量删除套餐
     *
     * @param packageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctPackagesByPackageIds(Long[] packageIds);

    List<RlctPackagesVo> selectPackageList();

    List<PackageBuyTimesVo> selectPackageBuyListTimes(Long parentTeenId);

    Long selectRlctParentTeenByParentTeenId(Long parentTeenId);
}
