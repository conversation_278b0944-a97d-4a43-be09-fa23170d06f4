package com.ruoyi.teen.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.teen.domain.RlctDoctorVisic;
import com.ruoyi.teen.domain.RlctProductUse;
import com.ruoyi.teen.domain.RlctVisit;
import com.ruoyi.teen.domain.RlctVisitVTwoDTO;
import com.ruoyi.teen.vo.ipad.ProductUseVo;
import com.ruoyi.teen.vo.ipad.VisitVo;
import com.ruoyi.teen.vo.RlctVisitVo;
import org.apache.ibatis.annotations.Param;

/**
 * 就诊记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctVisitMapper  extends BaseMapper<RlctVisit>
{
    /**
     * 查询就诊记录
     *
     * @param visitId 就诊记录主键
     * @return 就诊记录
     */
    public RlctVisit selectRlctVisitByVisitId(Long visitId);

    /**
     * 查询就诊记录列表
     *
     * @param rlctVisit 就诊记录
     * @return 就诊记录集合
     */
    public List<RlctVisitVo> selectRlctVisitList(RlctVisitVo rlctVisit);

    /**
     * 平板端下单新增就诊记录
     *
     * @param VisitVo
     * @return
     */
//    public int insertIpadVisit(VisitVo VisitVo);

    /**
     * 平板端完成治疗更新就诊记录
     *
     * @param rlctVisitVo
     * @return
     */
    public int upIpadVist(VisitVo rlctVisitVo);


    /**
     * 新增就诊记录
     *
     * @param rlctVisit 就诊记录
     * @return 结果
     */
//    public int insertRlctVisit(RlctVisit rlctVisit);

    /**
     * 修改就诊记录
     *
     * @param rlctVisit 就诊记录
     * @return 结果
     */
    public int updateRlctVisit(RlctVisit rlctVisit);

    /**
     * 删除就诊记录
     *
     * @param visitId 就诊记录主键
     * @return 结果
     */
    public int deleteRlctVisitByVisitId(Long visitId);

    /**
     * 批量删除就诊记录
     *
     * @param visitIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctVisitByVisitIds(Long[] visitIds);

    /**
     * 获取就诊记录列表
     * @param rlctParentTeen 父母与孩子关系的实体
     * @return 结果
     */
    List<RlctVisitVo> getVisitList(@Param("teenId") Long teenId,@Param("status") Integer status);

    public int updateBeforeTreatmentImageUrl(RlctVisit rlctVisit);
    public int updateAfterTreatmentImageUrl(RlctVisit rlctVisit);

    /*新增就诊单*/
    Long insertRlctVisitVTwo(RlctVisitVTwoDTO rlctVisitVTwoDTO);

    void insertRLctProductUse(ProductUseVo productUseVo);

    int insertRlctDoctorVisic(@Param("list") List<RlctDoctorVisic> rlctdoctorvisicList);

    void updateRLctProductUse(ProductUseVo productUseVo);

    void updateRlctDoctorVisic(RlctDoctorVisic rlctdoctorvisicList);


    int insertsubmitProduct(RlctProductUse rlctProductUse);

    /*单条伪删除医师与就诊关系表*/
    int updateRlctDoctorVisicByVisitId(@Param("record") RlctDoctorVisic updateDoctorVisic);

    Long selectRLctProductUseByVisitId(Long visitId);

    // 获取数据库中现有的治疗记录 （当前的医师id）
    List<RlctDoctorVisic> selectByVisitAndDoctor(@Param("visitId") Long visitId, @Param("doctorId") Long doctorId);

    /*批量伪删除医师与就诊关系表 并且还能填入更新者更新时间*/
    int batchSoftDelete(@Param("ids")List<Long> ids,@Param("currentUserId") Long currentUserId,@Param("now") Date now);

    // 获取该就诊单下所有未审核完成的医生数量
    int countPendingDoctorsByVisit(Long visitId);

    //  获取该就诊单下所有未审核完成的医生单
    List<RlctDoctorVisic> selectByVisit(Long visitId);



    /*更新产品记录表*/
    void updateProductUseByuserProductId(RlctProductUse rlctProductUse);

    /*更新医生与就诊单的治疗工作*/
    void updateRlctDoctorVisicByDoctorVisicId(RlctDoctorVisic toUpdate);
}
