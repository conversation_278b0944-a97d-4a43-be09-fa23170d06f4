package com.ruoyi.teen.mapper;

import java.util.List;
import com.ruoyi.teen.domain.RlctParentTeen;
import org.apache.ibatis.annotations.Param;

/**
 * 家长和孩子的关系Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctParentTeenMapper
{
    /**
     * 查询家长和孩子的关系
     *
     * @param id 家长和孩子的关系主键
     * @return 家长和孩子的关系
     */
    public RlctParentTeen selectRlctParentTeenById(Long id);

    /**
     * 查询家长和孩子的关系列表
     *
     * @param rlctParentTeen 家长和孩子的关系
     * @return 家长和孩子的关系集合
     */
    public List<RlctParentTeen> selectRlctParentTeenList(RlctParentTeen rlctParentTeen);


    public RlctParentTeen selectByParentIdAndTeenId(@Param("parentId") Long parentId, @Param("teenId") Long teenId);

    /**
     * 新增家长和孩子的关系
     *
     * @param rlctParentTeen 家长和孩子的关系
     * @return 结果
     */
    public int insertRlctParentTeen(RlctParentTeen rlctParentTeen);

    /**
     * 修改家长和孩子的关系
     *
     * @param rlctParentTeen 家长和孩子的关系
     * @return 结果
     */
    public int updateRlctParentTeen(RlctParentTeen rlctParentTeen);

    /**
     * 删除家长和孩子的关系
     *
     * @param id 家长和孩子的关系主键
     * @return 结果
     */
    public int deleteRlctParentTeenById(Long id);

    /**
     * 批量删除家长和孩子的关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctParentTeenByIds(Long[] ids);

    /*根据关系id查询家长id*/
    Long selectByparentTeenId(Long parentTeenId);
}
