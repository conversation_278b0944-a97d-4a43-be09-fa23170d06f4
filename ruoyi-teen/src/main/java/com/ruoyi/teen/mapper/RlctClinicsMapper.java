package com.ruoyi.teen.mapper;

import java.util.List;
import com.ruoyi.teen.domain.RlctClinics;

/**
 * 医馆信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctClinicsMapper 
{
    /**
     * 查询医馆信息
     * 
     * @param clinicId 医馆信息主键
     * @return 医馆信息
     */
    public RlctClinics selectRlctClinicsByClinicId(Long clinicId);

    /**
     * 查询医馆信息列表
     * 
     * @param rlctClinics 医馆信息
     * @return 医馆信息集合
     */
    public List<RlctClinics> selectRlctClinicsList(RlctClinics rlctClinics);

    /**
     * 新增医馆信息
     * 
     * @param rlctClinics 医馆信息
     * @return 结果
     */
    public int insertRlctClinics(RlctClinics rlctClinics);

    /**
     * 修改医馆信息
     * 
     * @param rlctClinics 医馆信息
     * @return 结果
     */
    public int updateRlctClinics(RlctClinics rlctClinics);

    /**
     * 删除医馆信息
     * 
     * @param clinicId 医馆信息主键
     * @return 结果
     */
    public int deleteRlctClinicsByClinicId(Long clinicId);

    /**
     * 批量删除医馆信息
     * 
     * @param clinicIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctClinicsByClinicIds(Long[] clinicIds);
}
