package com.ruoyi.teen.mapper;

import java.util.List;

import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.domain.RlctTeenWightHeight;
import org.apache.ibatis.annotations.Param;

/**
 * 身高体重Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctTeenWightHeightMapper
{
    /**
     * 查询身高体重
     *
     * @param id 身高体重主键
     * @return 身高体重
     */
    public RlctTeenWightHeight selectRlctTeenWightHeightById(Long id);

    /**
     * 获取小孩最新的身高体重
     * @param teenId
     * @return
     */
    public RlctTeenWightHeight getLastRlctTeenWightHeight(Long parentTeenId);

    /**
     * 查询身高体重列表
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 身高体重集合
     */
    public List<RlctTeenWightHeight> selectRlctTeenWightHeightList(RlctTeenWightHeight rlctTeenWightHeight);

    /**
     * 新增身高体重
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 结果
     */
    public int insertRlctTeenWightHeight(RlctTeenWightHeight rlctTeenWightHeight);

    /**
     * 修改身高体重
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 结果
     */
    public int updateRlctTeenWightHeight(RlctTeenWightHeight rlctTeenWightHeight);

    /**
     * 删除身高体重
     *
     * @param id 身高体重主键
     * @return 结果
     */
    public int deleteRlctTeenWightHeightById(Long id);

    /**
     * 批量删除身高体重
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctTeenWightHeightByIds(Long[] ids);

    /**
     * 获取孩子的身高体重
     * @param parentTeen 父母孩子关系实体
     * @return 结果
     */
    List<RlctTeenWightHeight> getBodyInfo(RlctParentTeen parentTeen);

//    /**
//     * 获取孩子的身高体重(平板端)
//     * @param parentTeen 父母孩子关系实体
//     * @return
//     */
//    List<RlctTeenWightHeight> BodyInfo(RlctParentTeen parentTeen);

    /**
     * 获取孩子的前n次身高
     * @param parentTeenId
     * @param n
     * @return
     */
    List<RlctTeenWightHeight> getHeight(@Param("parentTeenId")Long parentTeenId,@Param("n")Long n);
    int countByParentTeenId(@Param("parentTeenId")Long parentTeenId);

}
