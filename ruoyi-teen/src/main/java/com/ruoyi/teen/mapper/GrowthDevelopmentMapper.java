package com.ruoyi.teen.mapper;

import java.util.List;
import com.ruoyi.teen.domain.GrowthDevelopment;
import org.apache.ibatis.annotations.Param;

/**
 * 成长发育情况Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface GrowthDevelopmentMapper
{
    /**
     * 新增成长发育情况
     *
     * @param growthDevelopment 成长发育情况
     * @return 结果
     */
    public int insertGrowthDevelopment(GrowthDevelopment growthDevelopment);

}
