package com.ruoyi.teen.mapper;

import java.util.List;
import com.ruoyi.teen.domain.RlctVisitHistory;

/**
 * 就诊历史记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctVisitHistoryMapper
{
    /**
     * 查询就诊历史记录
     *
     * @param id 就诊历史记录主键
     * @return 就诊历史记录
     */
    public RlctVisitHistory selectRlctVisitHistoryById(Long id);

    /**
     * 查询就诊历史记录列表
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 就诊历史记录集合
     */
    public List<RlctVisitHistory> selectRlctVisitHistoryList(RlctVisitHistory rlctVisitHistory);

    /**
     * 新增就诊历史记录
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 结果
     */
    public int insertRlctVisitHistory(RlctVisitHistory rlctVisitHistory);

    /**
     * 完成治疗时插入就诊历史表
     * @param id
     * @return
     */
//    public int finishVist(Long id);

    /**
     * 修改就诊历史记录
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 结果
     */
    public int updateRlctVisitHistory(RlctVisitHistory rlctVisitHistory);

    /**
     * 删除就诊历史记录
     *
     * @param id 就诊历史记录主键
     * @return 结果
     */
    public int deleteRlctVisitHistoryById(Long id);

    /**
     * 批量删除就诊历史记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctVisitHistoryByIds(Long[] ids);
}
