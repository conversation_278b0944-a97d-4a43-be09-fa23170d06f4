package com.ruoyi.teen.mapper;


import com.ruoyi.teen.domain.RlctBoneageDTO;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.vo.pc.RlctTeenBoneAgeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PCRlctTeenVTWOMapper {

    /*查询孩子列表*/
    List<RlctTeenBoneAgeVo> selectRlctTeenList(RlctTeen rlctTeen);

    /*更新孩子信息*/
    int updateRlctTeen(RlctTeenBoneAgeVo rlctTeenBoneAgeVo);

    /*更新骨龄报告*/
    void updateRlctBoneAgeRecord(RlctBoneageDTO rlctBoneageDTO);

    /*骨龄列表*/
    List<RlctBoneageDTO> selectRlctBoneAgeRecordList(@Param("teenId") Long teenId);

    /*新增骨龄*/
    int insertRlctBoneAgeRecord(RlctBoneageDTO rlctBoneageDTO);

    /*查询骨龄详情*/
    RlctBoneageDTO selectRlctBoneAgeRecordByboneAgeRecordId(Long boneAgeRecordId);

    int deleteRlctBoneAgeRecordByboneAgeRecordId(Long boneAgeRecordId);
}
