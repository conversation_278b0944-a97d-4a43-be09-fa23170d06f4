package com.ruoyi.teen.mapper;

import java.util.List;
import com.ruoyi.teen.domain.RlctConfig;

/**
 * 配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctConfigMapper 
{
    /**
     * 查询配置
     * 
     * @param id 配置主键
     * @return 配置
     */
    public RlctConfig selectRlctConfigById(Long id);

    /**
     * 查询配置列表
     * 
     * @param rlctConfig 配置
     * @return 配置集合
     */
    public List<RlctConfig> selectRlctConfigList(RlctConfig rlctConfig);

    /**
     * 新增配置
     * 
     * @param rlctConfig 配置
     * @return 结果
     */
    public int insertRlctConfig(RlctConfig rlctConfig);

    /**
     * 修改配置
     * 
     * @param rlctConfig 配置
     * @return 结果
     */
    public int updateRlctConfig(RlctConfig rlctConfig);

    /**
     * 删除配置
     * 
     * @param id 配置主键
     * @return 结果
     */
    public int deleteRlctConfigById(Long id);

    /**
     * 批量删除配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctConfigByIds(Long[] ids);
}
