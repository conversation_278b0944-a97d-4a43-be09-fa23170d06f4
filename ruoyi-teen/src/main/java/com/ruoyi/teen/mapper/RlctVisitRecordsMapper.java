package com.ruoyi.teen.mapper;

import com.ruoyi.teen.vo.ipad.VisitRecordsVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 就诊记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface RlctVisitRecordsMapper {

    List<Long> selectRlctVisitRecordsList(@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    List<VisitRecordsVo> selectByVisitId(@Param("visitIdList") List<Long> visitIdList);
}
