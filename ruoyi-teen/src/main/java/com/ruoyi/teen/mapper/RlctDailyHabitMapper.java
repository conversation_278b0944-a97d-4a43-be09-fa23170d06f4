package com.ruoyi.teen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.teen.domain.RlctDailyHabit;

/**
 * 患者记录日常习惯Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface RlctDailyHabitMapper extends BaseMapper<RlctDailyHabit>
{
    /**
     * 查询患者记录日常习惯
     *
     * @param id 患者记录日常习惯主键
     * @return 患者记录日常习惯
     */
    public RlctDailyHabit selectRlctDailyHabitById(Long id);

    /**
     * 查询患者记录日常习惯列表
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 患者记录日常习惯集合
     */
    public List<RlctDailyHabit> selectRlctDailyHabitList(RlctDailyHabit rlctDailyHabit);

    /**
     * 新增患者记录日常习惯
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 结果
     */
    public int insertRlctDailyHabit(RlctDailyHabit rlctDailyHabit);

    /**
     * 修改患者记录日常习惯
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 结果
     */
    public int updateRlctDailyHabit(RlctDailyHabit rlctDailyHabit);

    /**
     * 删除患者记录日常习惯
     *
     * @param id 患者记录日常习惯主键
     * @return 结果
     */
    public int deleteRlctDailyHabitById(Long id);

    /**
     * 批量删除患者记录日常习惯
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctDailyHabitByIds(Long[] ids);
    public List<RlctDailyHabit> selectLastDailyHabit(Long parentTeenId);
}
