package com.ruoyi.teen.mapper;

import java.util.List;

import com.ruoyi.teen.domain.RlctPackages;
import com.ruoyi.teen.domain.RlctUserPackage;
import com.ruoyi.teen.domain.RlctUserPackageVTwo;
import com.ruoyi.teen.vo.ipad.*;
import com.ruoyi.teen.vo.patient.RlctUserPackageVo;
import com.ruoyi.teen.vo.pc.RlctUserPackagePCVo;
import com.ruoyi.teen.vo.pc.RlctReturnRecordVo;
import org.apache.ibatis.annotations.Param;

/**
 * 用户和套餐关系Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctUserPackageMapper
{
    /**
     * 查询用户和套餐关系
     *
     * @param id 用户和套餐关系主键
     * @return 用户和套餐关系
     */
    public RlctUserPackage selectRlctUserPackageById(Long id);

    /**
     * 平板端查询用户的套餐列表
     * @param userId 用户的id
     * @return 用户和套餐的关系集合
     */
    public List<PadUserPackageVo> selectUserPackageList(@Param("patientId") Long patientId);
    public List<UserPackageVo> selectByTel(@Param("tel") String tel);

    /**
     * 平板端查询用户的套餐列表
     * @param userId 用户的id
     * @param id  用户和套餐的关系主键
     * @return 用户和套餐的关系集合
     */
    public List<UserPackageVo> selectUserPackage(@Param("deductParentId") Long userId, @Param("id") Long id);

    /**
     * 平板端下单时更新这个用户的套餐的套餐消费中次数+1
     *
     * @param id
     * @return
     */
    public int upIpadUserPackage(Long id);

    /**
     * 治疗完成时更新这个用户的套餐的 套餐消费中次数-1 套餐消费次数+1
     * @return
     */
    public int finishUserPackage(Long id);

    /**
     * 查询用户和套餐关系列表
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 用户和套餐关系集合
     */
    public List<RlctUserPackage> selectRlctUserPackageList(RlctUserPackage rlctUserPackage);

    /**
     * 查询用户和套餐关系列表(用户名关联用户id)
     *
     * @param rlctUserPackagePCVo 用户和套餐关系
     * @return 用户和套餐关系集合
     */
    public List<RlctUserPackagePCVo> selectRlctUserPackagePCList(RlctUserPackagePCVo rlctUserPackagePCVo);


    /**
     * 新增用户和套餐关系
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 结果
     */
    public int insertRlctUserPackage(RlctUserPackage rlctUserPackage);

    /**
     * 修改用户和套餐关系
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 结果
     */
    public int updateRlctUserPackage(RlctUserPackage rlctUserPackage);
    public int userPackageReturn(RlctUserPackage rlctUserPackage);

    /**
     * 删除用户和套餐关系
     *
     * @param id 用户和套餐关系主键
     * @return 结果
     */
    public int deleteRlctUserPackageById(Long id);

    /**
     * 批量删除用户和套餐关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctUserPackageByIds(Long[] ids);

    /**
     * 查询用户套餐使用情况
     * @param tel 用户手机号码
     * @return 结果
     */
    List<RlctUserPackageVo> selectUsePackage(String tel);


    /**
     * 查询套餐退费列表
     * @return 结果
     */
    List<RlctReturnRecordVo> selectReturnRecordList(RlctReturnRecordVo rlctUserPackage);


    /**
     * 统计家长的套餐使用次数
     * @param tel
     * @return
     */
    Integer countNumberTimes(@Param("tel") String tel);

    /**
     * 统计家长的剩余套餐次数
     * @param tel
     * @return
     */
    Integer countLeaveTimes(@Param("tel") String tel);

    List<RecordPackagesListVo> selectByPhonenumberNameCh(@Param("phonenumber") String phonenumber,@Param("nameCh") String nameCh,@Param("keyword") String keyword);


    int insertRlctUserPackageList(@Param("rlctUserPackageVTwoList") List<RlctUserPackageVTwo> rlctUserPackageVTwo);

    List<PackageBuyTimesVo> selectTreatmentTypeSelection(Long parentTeenId);

    List<DoctorUserVo> selectDoctorUserList();



    /*
     * 通过套餐关系id查询套餐剩余次数与家长信息
     * */
    RlctUserPackageVTwo selectCheckRemainingTimes(Long userPackageId);

    int upIpadRollbackUserPackage(Long userPackageId);

}
