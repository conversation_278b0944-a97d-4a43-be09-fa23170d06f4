package com.ruoyi.teen.mapper;

import java.util.List;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.vo.ipad.PadPatientDetailVo;
import com.ruoyi.teen.vo.ipad.PadPatientTeenVo;
import com.ruoyi.teen.vo.ipad.PadPatientVo;
import com.ruoyi.teen.vo.physician.RlctTeenVo;
import org.apache.ibatis.annotations.Param;

/**
 * 孩子Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface RlctTeenMapper
{
    /**
     * 查询孩子
     *
     * @param teenId 孩子主键
     * @return 孩子
     */
    public RlctTeen selectRlctTeenByTeenId(Long teenId);

    /**
     * 获取孩子姓名拼音首字母为空的孩子信息
     * @return
     */
    public List<RlctTeen> selectTeenByUnTeenNameCh();

    /**
     * 查询孩子列表
     *
     * @param rlctTeen 孩子
     * @return 孩子集合
     */
    public List<RlctTeen> selectRlctTeenList(RlctTeen rlctTeen);

    /**
     * 新增孩子
     *
     * @param rlctTeen 孩子
     * @return 结果
     */
    public int insertRlctTeen(RlctTeen rlctTeen);

    /**
     * 修改孩子
     *
     * @param rlctTeen 孩子
     * @return 结果
     */
    public int updateRlctTeen(RlctTeen rlctTeen);

    /**
     * 删除孩子
     *
     * @param teenId 孩子主键
     * @return 结果
     */
    public int deleteRlctTeenByTeenId(Long teenId);

    /**
     * 批量删除孩子
     *
     * @param teenIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRlctTeenByTeenIds(Long[] teenIds);

    /**
     * 获取用户绑定的孩子(平板端)
     * @param tel 用户的电话号码
     * @return 结果  含父母与孩子关系id
     */
    List<RlctTeen> getTeenListByTel(String tel);

    List<PadPatientVo> patientList(String tel);
    List<PadPatientTeenVo> patientTeenList(@Param("teenNameCh") String teenNameCh);
    PadPatientDetailVo patientByTel(String tel);

    /**
     * 获取用户绑定的孩子(PC端)
     * @param userId 家长id
     * @return 结果  含父母与孩子关系id
     */
    List<RlctTeen> getTeenList(Long userId);

    /**
     * 获取用户绑定的孩子
     * @param tel 用户的电话号码
     * @return 结果
     */
    List<RlctTeen> getListByTel(String tel);

    RlctTeenVo selectTeen(Long teenId);
}
