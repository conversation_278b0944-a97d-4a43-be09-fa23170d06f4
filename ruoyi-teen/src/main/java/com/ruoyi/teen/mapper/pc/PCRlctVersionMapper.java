package com.ruoyi.teen.mapper.pc;

import com.ruoyi.teen.domain.RlctPadVersion;

import java.util.List;

public interface PCRlctVersionMapper {

    /*查询版本列表*/
    List<RlctPadVersion> selectRlctVersionList(RlctPadVersion rlctPadVersion);


    /*新增版本*/
    int insertRlctVersion(RlctPadVersion rlctPadVersion);


    /*获取版本详情*/
    RlctPadVersion selectRlctVersionByVersionId(Long versionId);


    /*修改版本详情*/
    int updateRlctVersion(RlctPadVersion rlctPadVersion);

    /*批量删除版本信息*/
    int deleteRlctVersionByVersionIds(Long[] versionIds);
}
