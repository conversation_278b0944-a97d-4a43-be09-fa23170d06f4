package com.ruoyi.teen.mapper;


import com.ruoyi.teen.domain.RlctDoctorVisic;
import com.ruoyi.teen.domain.RlctDoctorVisicDTO;
import com.ruoyi.teen.domain.RlcttreatmentTypeListDTO;
import com.ruoyi.teen.vo.ipad.PadConfirmCheckVo;
import com.ruoyi.teen.vo.ipad.RlctDoctorVisicVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface RlctDoctorVisicMapper {


    List<RlctDoctorVisicVo> selectRlctDoctorVisicNoCheckList(@Param("auditStatus")Integer auditStatus);



    /*
    * 根据ID审核通过
    *
    * */
    int updateRlctDoctorVisicStatus(RlctDoctorVisic data);

    // 获取项目治疗类型
    List<RlcttreatmentTypeListDTO> selectRlctDoctorVisicTreatmentType(@Param("visitId") Long visitId, @Param("doctorId") Long doctorId);

    List<RlcttreatmentTypeListDTO> selectAllTreatmentsForVisits(@Param("visitIds")List<Long> visitIds,@Param("doctorIds") List<Long> doctorIds);


    /**
     * 根据ID伪删除治疗记录
     * @param doctorVisicId 记录ID
     * @param currentUserId 操作人ID
     * @param nowDate 操作时间
     * @return 影响的行数
     * */
    int updateRlctDoctorVisic(@Param("doctorVisicId") Long doctorVisicId,@Param("currentUserId") Long currentUserId,@Param("now") Date nowDate);

    /*
    * 根据id批量审核 通过 或审核不通过
    * */
    int batchUpdateRlctDoctorVisicStatus(@Param("ids") List<Long> ids,@Param("reviewStatus") String reviewStatus,@Param("currentUserId") Long currentUserId,@Param("reviewTime") Date reviewTime,@Param("cancelReviewTime") Date cancelReviewTime);
}
