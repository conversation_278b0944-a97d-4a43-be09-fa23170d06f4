package com.ruoyi.teen.mapper;

import com.ruoyi.teen.domain.statistic.HeightWightBean;
import com.ruoyi.teen.domain.statistic.TeenStatisticBean;
import com.ruoyi.teen.domain.statistic.TeenStatisticHeightBean;
import com.ruoyi.teen.domain.statistic.TeenStatisticPriceBean;
import com.ruoyi.teen.vo.ipad.UserServiceRecordVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 医馆信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface TeenStatisticMapper
{

    //统计家长下各小孩的就诊次数
    List<TeenStatisticBean> statisticVisitTimes(@Param("tel") String tel);

    //统计家长下各小孩的身高
    List<TeenStatisticHeightBean> statisticGroupUp(@Param("tel") String tel);


    //根据teenId查询身高体重数据
    List<HeightWightBean> statisticHeightWightByTeenId(@Param("teenId") Long teenId);

    //根据teenId查询就诊次数
    List<TeenStatisticBean> statisticVisitTimesByTeenId(@Param("teenId") Long teenId);
    List<TeenStatisticPriceBean> statisticPrice();

    int countTotalService();//总服务人数
    int countTodayService();//今日服务人数
    int countTotalArchives();//总建档人数
    int countTodayArchives();//今日建档人数

    // 根据医生id或范围可写查询充值金额
    Long sumPackagePrice(@Param("doctorId")Long doctorId, @Param("startTime") LocalDateTime startTime,  @Param("endTime")LocalDateTime endTime);

    // 根据医生id或范围可写查询建档人数
    Integer countTotalArchivesByDoctorId(@Param("doctorId")Long doctorId,@Param("startTime") LocalDateTime startTime,  @Param("endTime")LocalDateTime endTime);

    //  * 查询医生每日服务记录
    //     * @param doctorId 医生ID
    //     * @param startTime 开始时间（字符串格式）
    //     * @param endTime 结束时间（Date类型）
    //     * @return 每日服务记录列表
    List<UserServiceRecordVo> selectDailyServiceRecords(@Param("doctorId")Long doctorId,@Param("startTime") String firstDayOfStartMonth,@Param("endTime") Date tomorrow);

    //  * 统计医生每日服务金额
    List<UserServiceRecordVo> selectDailyServicePrice(@Param("doctorId")Long doctorId,@Param("startTime") String firstDayOfStartMonth,@Param("endTime") Date tomorrow);
}
