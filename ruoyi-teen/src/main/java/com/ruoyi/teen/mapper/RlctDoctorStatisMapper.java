package com.ruoyi.teen.mapper;

import com.ruoyi.teen.domain.RlctTodayPerformanceDTO;
import com.ruoyi.teen.vo.patient.RlctPersonalInfoVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface RlctDoctorStatisMapper {

    /*个人信息*/
    RlctPersonalInfoVo selectDoctorPersonalInfo(Long userId);


    /*根据idAnd就诊时间范围查询业绩*/
    List<RlctTodayPerformanceDTO> selectPerformance(@Param("userId")Long userId, @Param("startDate") String startDate, @Param("endDate") String endDate);


    /*根据idAnd时间范围查询完成治疗不同接诊单数量业绩*/
    BigDecimal selectPeoplePerformance(@Param("userId")Long userId, @Param("startDate") String startDate, @Param("endDate") String endDate);
}
