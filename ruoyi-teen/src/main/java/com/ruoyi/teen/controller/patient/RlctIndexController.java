package com.ruoyi.teen.controller.patient;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.model.PhoneParam;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysLoginService;
import com.ruoyi.system.service.ISysWechatService;
import com.ruoyi.teen.service.IRlctDoctorStatisticsService;
import com.ruoyi.teen.vo.ipad.RlctMonthPerformanceVo;
import com.ruoyi.teen.vo.patient.RlctPersonalInfoVo;
import com.ruoyi.teen.vo.patient.RlctTodayPerformanceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

//@Api("小程序汇总")
@RestController
@RequestMapping("/applet/index")
public class RlctIndexController extends BaseController {
    @Autowired
    private ISysWechatService wechatService; // 添加这行

    @Autowired
    private ISysLoginService loginService;

    @Autowired
    private IRlctDoctorStatisticsService doctorStatisticsService;

    @Autowired
    private TokenService tokenService;
    /**
     * 个人信息
     * */
//    @ApiOperation("个人信息")
    @GetMapping("/userInfo")
    public AjaxResult userInfo(HttpServletRequest request){
        try {
            LoginUser loginUser = getLoginUser();
            Long userId = loginUser.getUser().getUserId();  //  从缓存得到 当前用户ID
            RlctPersonalInfoVo seriesData  = doctorStatisticsService.personalInfo(userId);
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("personal information!",e);
            return AjaxResult.error(e);
        }
    }

    /**
     * 今日业绩
     * */
//    @ApiOperation("今日业绩")
    @GetMapping("/todayPerformance")
    public AjaxResult todayPerformance(){
        try {
            LoginUser loginUser = getLoginUser();
            Long userId = loginUser.getUser().getUserId();  //  从缓存得到 当前用户ID
            List<RlctTodayPerformanceVo> result  = doctorStatisticsService.todayPerformance(userId);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("Today's performance!",e);
            return AjaxResult.error(e);
        }
    }

    /**
     * 本月业绩对比
     * */
//    @ApiOperation("本月业绩对比")
    @GetMapping("/monthPerformance")
    public AjaxResult monthPerformance(){
        try {
            LoginUser loginUser = getLoginUser();
            Long userId = loginUser.getUser().getUserId();  //  从缓存得到 当前用户ID
            List<RlctMonthPerformanceVo> result  = doctorStatisticsService.monthPerformance(userId);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("This month's performance!",e);
            return AjaxResult.error(e);
        }
    }
}
