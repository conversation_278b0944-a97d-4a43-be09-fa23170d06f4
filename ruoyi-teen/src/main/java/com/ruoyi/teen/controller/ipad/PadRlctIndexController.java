package com.ruoyi.teen.controller.ipad;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.domain.statistic.EchartSeriesData;
import com.ruoyi.teen.service.ITeenStatisticService;
import com.ruoyi.teen.vo.ipad.PadUserInfo;
import com.ruoyi.teen.vo.ipad.PadUserInfoVTwo;
import com.ruoyi.teen.vo.ipad.UserPackageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/23
 */
@Api("平板端汇总")
@RestController
@RequestMapping("/ipad/index")
public class PadRlctIndexController extends BaseController {


    @Autowired
    private ITeenStatisticService teenStatisticService;

    //本月充值记录
    @ApiOperation("本月充值记录")
    @GetMapping("/statisticPrice")
    public AjaxResult statisticPrice(){
        try {
            EchartSeriesData seriesData = teenStatisticService.statisticPrice();
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("statisticPrice!",e);
            return AjaxResult.error(e);
        }
    }

    /**
     * 根据员工查询其每天接待人数的汇总（总服务人数、今日服务人数、总建档人数、今日建档人数）员工信息（姓名、性别、年龄、联系方式）
     * @return
     */
    @ApiOperation("根据员工查询其每天接待人数的汇总")
    @GetMapping("/userInfo")
    public AjaxResult userInfo(){
        try {
            PadUserInfo data = teenStatisticService.padUserInfo();
            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("statisticPrice!",e);
            return AjaxResult.error(e);
        }
    }

    /**
     * 根据员工查询其每天接待人数的汇总（总服务人数、今日服务人数、总建档人数、今日建档人数）员工信息（姓名、性别、年龄、联系方式）
     * @return
     */
    @ApiOperation("根据员工查询其每天接待人数的汇总")
    @GetMapping("/userInfoTwo")
    public AjaxResult userInfoTwo(){
        try {
            PadUserInfoVTwo data = teenStatisticService.padUserInfoTwo();
            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("statisticPrice!",e);
            return AjaxResult.error(e);
        }
    }

}
