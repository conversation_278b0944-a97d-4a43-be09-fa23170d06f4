package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctConfig;
import com.ruoyi.teen.service.IRlctConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 配置Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("配置管理")
@RestController
@RequestMapping("/teen/config")
public class RlctConfigController extends BaseController
{
    @Autowired
    private IRlctConfigService rlctConfigService;

    /**
     * 查询配置列表
     */
    @ApiOperation("查询配置列表")
    @PreAuthorize("@ss.hasPermi('teen:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctConfig rlctConfig)
    {
        startPage();
        List<RlctConfig> list = rlctConfigService.selectRlctConfigList(rlctConfig);
        return getDataTable(list);
    }

    /**
     * 导出配置列表
     */
    @ApiOperation("导出配置列表")
    @PreAuthorize("@ss.hasPermi('teen:config:export')")
    @Log(title = "配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctConfig rlctConfig)
    {
        List<RlctConfig> list = rlctConfigService.selectRlctConfigList(rlctConfig);
        ExcelUtil<RlctConfig> util = new ExcelUtil<RlctConfig>(RlctConfig.class);
        util.exportExcel(response, list, "配置数据");
    }

    /**
     * 获取配置详细信息
     */
    @ApiOperation("获取配置详细信息")
    @PreAuthorize("@ss.hasPermi('teen:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rlctConfigService.selectRlctConfigById(id));
    }

    /**
     * 新增配置
     */
    @ApiOperation("新增配置")
    @PreAuthorize("@ss.hasPermi('teen:config:add')")
    @Log(title = "配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctConfig rlctConfig)
    {
        return toAjax(rlctConfigService.insertRlctConfig(rlctConfig));
    }

    /**
     * 修改配置
     */
    @ApiOperation("修改配置")
    @PreAuthorize("@ss.hasPermi('teen:config:edit')")
    @Log(title = "配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctConfig rlctConfig)
    {
        return toAjax(rlctConfigService.updateRlctConfig(rlctConfig));
    }

    /**
     * 删除配置
     */
    @ApiOperation("删除配置")
    @PreAuthorize("@ss.hasPermi('teen:config:remove')")
    @Log(title = "配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rlctConfigService.deleteRlctConfigByIds(ids));
    }
}
