package com.ruoyi.teen.controller.ipad;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.teen.domain.RlctVisit;
import com.ruoyi.teen.domain.config.AttributeListBean;
import com.ruoyi.teen.domain.config.Diagnose;
import com.ruoyi.teen.service.IRlctConfigService;
import com.ruoyi.teen.utils.DownloadUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;

/**
 * 配置Controller
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@Api("配置管理")
@RestController
@RequestMapping("/ipad/config")
public class PadRlctConfigController extends BaseController
{
    @Autowired
    private IRlctConfigService rlctConfigService;


//    /**
//     * 平板端封装获取config类的数据
//     * @param rlctConfig
//     * @return
//     */
//    @GetMapping("/listInfo")
//    public List<configVo> clist(RlctConfig rlctConfig)
//    {
//        return rlctConfigService.getList(rlctConfig);
//    }

    /**
     * 平板端封装获取config类的数据
     * @return
     */
    @ApiOperation("平板端封装获取config类的数据")
    @GetMapping("/listInfo")
    public AjaxResult listInfo()
    {
        try{
            AttributeListBean bean = rlctConfigService.listInfo();
            return AjaxResult.success(bean);
        }catch (Exception e){
            return AjaxResult.error(e);
        }
    }


    /**
     * 平板端套餐下单及保存信息配置表
     * @param diagnose
     * @return
     */
    @ApiOperation("平板端套餐下单及保存信息配置表")
    @PostMapping("/saveConfig")
    public AjaxResult saveConfig(@RequestBody @Valid Diagnose diagnose){
        try{
            RlctVisit visit = rlctConfigService.saveConfig(diagnose);
            return AjaxResult.success(visit);
        }catch (Exception e){
            return AjaxResult.error(e);
        }
    }

    /**
     * 根据teenId获取小孩最近的信息
     * @param teenId
     * @return
     */
    @ApiOperation("获取小孩最近的信息")
    @GetMapping("/getLastConfig")
    @Anonymous
    public AjaxResult getLastConfig(@RequestParam Long teenId,@RequestParam Long patientId){
        try{
            Diagnose diagnose = rlctConfigService.getLastConfig(teenId,patientId);
            return AjaxResult.success(diagnose);
        }catch (Exception e){
            return AjaxResult.error(e);
        }
    }

    /**
     * 平板端套餐下单及保存信息配置表,并下载pdf的诊疗单
     * @param diagnose
     * @return
     */
    @ApiOperation("平板端套餐下单及保存信息配置表,并下载pdf的诊疗单")
    @PostMapping("/saveConfigAndPdf")
    public void saveConfigAndPdf(@RequestBody @Valid Diagnose diagnose, HttpServletResponse response) throws Exception {
        String pdfPath = rlctConfigService.saveConfigAndPdf(diagnose);
        DownloadUtils.downloadPdf(pdfPath,response);
    }

    /**
     * 下载pdf的诊疗单
     * @param visitId
     * @return
     */
    @ApiOperation("下载pdf的诊疗单")
    @GetMapping("/downloadPdf")
    @Anonymous
    public void downloadPdf(Long visitId,HttpServletResponse response) throws Exception {
        String pdfPath = rlctConfigService.downloadPdf(visitId);
        DownloadUtils.downloadPdf(pdfPath,response);
    }

    @GetMapping("/saveConfigAndPdfTest")
    @Anonymous
    public void saveConfigAndPdfTest( HttpServletResponse response) throws Exception {
        Diagnose diagnose = new Diagnose();
        diagnose.setUserPackageId(5L);
        diagnose.setPatientId(137L);
        diagnose.setTeenId(8L);
        diagnose.setHeight(new BigDecimal(12.5));
        diagnose.setWeight(new BigDecimal(11));
        diagnose.setSleep(11L);
        diagnose.setDuration(11L);
        diagnose.setJump(11L);
        diagnose.setHeightJump(11L);
        diagnose.setBar(11L);
        diagnose.setMilk(11L);
        diagnose.setEgg(11L);
        diagnose.setVegetable(11L);
        diagnose.setRaw(11L);
        diagnose.setGrain(11L);
        diagnose.setSnacks(11L);
        diagnose.setEatSnack(11L);
        diagnose.setvAt(11L);
        diagnose.setvDt(11L);
        diagnose.setCa(11L);
        diagnose.setSynthesis(11L);
        diagnose.setAcid(11L);


        String pdfPath = rlctConfigService.saveConfigAndPdf(diagnose);
        DownloadUtils.downloadPdf(pdfPath,response);
    }

//    /**
//     * 平板端套餐下单及保存信息配置表
//     * @param visitVo
//     * @return
//     */
//    @PostMapping("/orderInfo")
//    public AjaxResult finish(@RequestBody VisitVo visitVo){
//            try{
//                ValidateUtils.checkLong(visitVo.getDeductParentId(),"用户userid");
//                ValidateUtils.checkLong(visitVo.getPackageId(),"套餐id");
//                ValidateUtils.checkObject(visitVo.getGetIds(),"记录表ids");
//                ValidateUtils.checkBigDecimal(visitVo.getHeight(),"身高");
//                ValidateUtils.checkBigDecimal(visitVo.getWeight(),"体重");
//                ValidateUtils.checkLong(visitVo.getParentTeenId(),"父母与孩子关系Id");
//                return toAjax(rlctConfigService.finish(visitVo));
//            }catch (RuntimeException r){
//                return AjaxResult.error("参数校验失败",r.getMessage());
//            }
//    }
}
