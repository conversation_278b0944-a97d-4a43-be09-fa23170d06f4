package com.ruoyi.teen.controller.patient;

import com.ruoyi.teen.domain.RlctProduct;
import com.ruoyi.teen.service.IRlctProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/12/5
 */
@RestController
@Component("patientRlctProductController")
@RequestMapping("/patient/product")
public class RlctProductController {

    @Autowired
    private IRlctProductService rlctProductService;
    /**
     * 查询产品广告列表
     * @return
     */
//    @PreAuthorize("@ss.hasPermi('patient:product:list')")
    @PostMapping("/list")
    public List<RlctProduct> list(@RequestBody RlctProduct rlctProduct)
    {
         return rlctProductService.selectRlctProductList(rlctProduct);
    }
}
