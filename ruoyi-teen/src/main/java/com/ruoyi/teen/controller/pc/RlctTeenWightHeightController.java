package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctTeenWightHeight;
import com.ruoyi.teen.service.IRlctTeenWightHeightService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 身高体重Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("身高体重管理")
@RestController
@RequestMapping("/teen/wight_height")
public class RlctTeenWightHeightController extends BaseController
{
    @Autowired
    private IRlctTeenWightHeightService rlctTeenWightHeightService;

    /**
     * 查询身高体重列表
     */
    @ApiOperation("查询身高体重列表")
    @PreAuthorize("@ss.hasPermi('teen:wight_height:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctTeenWightHeight rlctTeenWightHeight)
    {
        startPage();
        List<RlctTeenWightHeight> list = rlctTeenWightHeightService.selectRlctTeenWightHeightList(rlctTeenWightHeight);
        return getDataTable(list);
    }

    /**
     * 导出身高体重列表
     */
    @ApiOperation("导出身高体重列表")
    @PreAuthorize("@ss.hasPermi('teen:wight_height:export')")
    @Log(title = "身高体重", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctTeenWightHeight rlctTeenWightHeight)
    {
        List<RlctTeenWightHeight> list = rlctTeenWightHeightService.selectRlctTeenWightHeightList(rlctTeenWightHeight);
        ExcelUtil<RlctTeenWightHeight> util = new ExcelUtil<RlctTeenWightHeight>(RlctTeenWightHeight.class);
        util.exportExcel(response, list, "身高体重数据");
    }

    /**
     * 获取身高体重详细信息
     */
    @ApiOperation("获取身高体重详细信息")
    @PreAuthorize("@ss.hasPermi('teen:wight_height:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rlctTeenWightHeightService.selectRlctTeenWightHeightById(id));
    }

    /**
     * 新增身高体重
     */
    @ApiOperation("新增身高体重")
    @PreAuthorize("@ss.hasPermi('teen:wight_height:add')")
    @Log(title = "身高体重", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctTeenWightHeight rlctTeenWightHeight)
    {
        return toAjax(rlctTeenWightHeightService.insertRlctTeenWightHeight(rlctTeenWightHeight));
    }

    /**
     * 修改身高体重
     */
    @ApiOperation("修改身高体重")
    @PreAuthorize("@ss.hasPermi('teen:wight_height:edit')")
    @Log(title = "身高体重", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctTeenWightHeight rlctTeenWightHeight)
    {
        return toAjax(rlctTeenWightHeightService.updateRlctTeenWightHeight(rlctTeenWightHeight));
    }

    /**
     * 删除身高体重
     */
    @ApiOperation("删除身高体重")
    @PreAuthorize("@ss.hasPermi('teen:wight_height:remove')")
    @Log(title = "身高体重", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rlctTeenWightHeightService.deleteRlctTeenWightHeightByIds(ids));
    }
}
