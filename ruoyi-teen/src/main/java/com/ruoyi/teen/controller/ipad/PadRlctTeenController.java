package com.ruoyi.teen.controller.ipad;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.domain.statistic.EchartSeriesData;
import com.ruoyi.teen.service.IRlctTeenService;
import com.ruoyi.teen.service.IRlctUserPackageService;
import com.ruoyi.teen.service.ITeenStatisticService;
import com.ruoyi.teen.vo.ipad.PadPatientDetailVo;
import com.ruoyi.teen.vo.ipad.PadPatientTeenVo;
import com.ruoyi.teen.vo.ipad.PadPatientVo;
import com.ruoyi.teen.vo.ipad.UserPackageVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/23
 */
@ApiOperation("平板端家长管理")
@RestController
@Component("ipadRlctTeenController")
@RequestMapping("/ipad/teen")
public class PadRlctTeenController extends BaseController {

    @Autowired
    private IRlctTeenService rlctTeenService;

    @Autowired
    private ITeenStatisticService teenStatisticService;

    @Autowired
    private IRlctUserPackageService rlctUserPackageService;

    /**
     * 根据手机号查询家长列表
     */
    @ApiOperation("根据手机号查询家长列表")
    @GetMapping("/patientList")
    public TableDataInfo patientList(@RequestParam String tel) {
        List<PadPatientVo> list = rlctTeenService.patientList(tel);
        return getDataTable(list);
    }

    @ApiOperation("根据孩子首字母缩写查询孩子信息列表（模糊查询）")
    @GetMapping("/patientTeenList")
//    @Anonymous
    public TableDataInfo patientTeenList(@RequestParam String teenNameCh) {
        List<PadPatientTeenVo> list = rlctTeenService.patientTeenList(teenNameCh);
        return getDataTable(list);
    }



    //根据手机号码查询详细信息（建档时间、就诊次数、未使用次数 、患者列表）
    @ApiOperation("根据手机号码查询详细信息（建档时间、就诊次数、未使用次数 、患者列表）")
    @GetMapping("/patientDetailByTel")
    public AjaxResult patientDetailByTel(@RequestParam String tel){
        try {
            PadPatientDetailVo vo =  rlctTeenService.patientDetailByTel(tel);
            return AjaxResult.success(vo);
        } catch (Exception e) {
            logger.error("patientDetailByTel!tel="+tel,e);
            return AjaxResult.error(e);
        }
    }

    //图表数据：就诊次数对比数据接口（多小孩）
    @ApiOperation("图表数据：就诊次数对比数据接口（多小孩）")
    @GetMapping("/statisticVisitTimes")
    public AjaxResult statisticVisitTimes(@RequestParam String tel){
        try {
            EchartSeriesData seriesData = teenStatisticService.statisticVisitTimes(tel);
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("statisticVisitTimes!tel="+tel,e);
            return AjaxResult.error(e);
        }
    }

    //图表数据：成长曲线对比数据接口（多小孩）
    @ApiOperation("图表数据：成长曲线对比数据接口（多小孩）")
    @GetMapping("/statisticGroupUp")
    public AjaxResult statisticGroupUp(@RequestParam String tel){
        try {
            EchartSeriesData seriesData = teenStatisticService.statisticGroupUp(tel);
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("statisticGroupUp!tel="+tel,e);
            return AjaxResult.error(e);
        }
    }

    //根据手机号码查询套餐内容（套餐名称、使用情况、已使用次数、总次数）
    @ApiOperation("根据手机号码查询套餐内容（套餐名称、使用情况、已使用次数、总次数）")
    @GetMapping("/selectByTel")
    public TableDataInfo selectByTel(@RequestParam String tel){
        List<UserPackageVo> list = rlctUserPackageService.selectByTel(tel);
        return getDataTable(list);
    }



    //根据该手机号码下的孩子查询详情--图表数据：身高体重数据接口
    @ApiOperation("根据该手机号码下的孩子查询详情--图表数据：身高体重数据接口")
    @GetMapping("/statisticHeightWightByTeenId")
    public AjaxResult statisticHeightWightByTeenId(@RequestParam Long teenId){
        try {
            EchartSeriesData seriesData = teenStatisticService.statisticHeightWightByTeenId(teenId);
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("statisticHeightWightByTeenId!teenId="+teenId,e);
            return AjaxResult.error(e);
        }
    }

    //根据该手机号码下的孩子查询详情--图表数据：每月就诊次数数据接口
    @ApiOperation("根据该手机号码下的孩子查询详情--图表数据：每月就诊次数数据接口")
    @GetMapping("/statisticVisitTimesByTeenId")
    public AjaxResult statisticVisitTimesByTeenId(@RequestParam Long teenId){
        try {
            EchartSeriesData seriesData = teenStatisticService.statisticVisitTimesByTeenId(teenId);
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("statisticVisitTimesByTeenId!teenId="+teenId,e);
            return AjaxResult.error(e);
        }
    }


    //根据该手机号码下的孩子查询详情--图表数据：健康管理五维图数据接口
    @ApiOperation("根据该手机号码下的孩子查询详情--图表数据：健康管理五维图数据接口")
    @GetMapping("/statisticRadarByTeenId")
    public AjaxResult statisticRadarByTeenId(@RequestParam Long teenId){
        try {
            EchartSeriesData seriesData = teenStatisticService.statisticRadarByTeenId(teenId);
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("statisticRadarByTeenId!teenId="+teenId,e);
            return AjaxResult.error(e);
        }
    }

    /**
     * 平板端查询孩子列表
     */
    @ApiOperation("板端查询孩子列表")
    @GetMapping("/teenList/{tel}")
    public List<RlctTeen> getTeenListByTel(@PathVariable("tel") String tel)
    {
        return rlctTeenService.getTeenListByTel(tel);
    }

    /**
     * 获取孩子健康管理五维图信息
     */
    @ApiOperation("获取孩子健康管理五维图信息")
    @GetMapping("/teenInfo/{teenId}")
    public RlctTeen getTeenInfo(@PathVariable("teenId") Long teenId)
    {
        return rlctTeenService.selectRlctTeenByTeenId(teenId);
    }

}
