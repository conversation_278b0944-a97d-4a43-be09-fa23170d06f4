package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.teen.vo.pc.RlctConsumptionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctConsumption;
import com.ruoyi.teen.service.IRlctConsumptionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 消费记录Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("消费记录")
@RestController
@RequestMapping("/teen/consumption")
public class RlctConsumptionController extends BaseController
{
    @Autowired
    private IRlctConsumptionService rlctConsumptionService;

    /**
     * 查询消费记录列表
     */
    @ApiOperation("查询消费记录列表")
    @PreAuthorize("@ss.hasPermi('teen:consumption:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctConsumption rlctConsumption)
    {
        startPage();
        List<RlctConsumption> list = rlctConsumptionService.selectRlctConsumptionList(rlctConsumption);
        return getDataTable(list);
    }

    /**
     * 查询消费记录名字列表
     */
    @ApiOperation("查询消费记录名字列表")
    @PreAuthorize("@ss.hasPermi('teen:consumption:list')")
    @GetMapping("/listName")
    public TableDataInfo listName(RlctConsumptionVo rlctConsumptionVo)
    {
        startPage();
        List<RlctConsumptionVo> list = rlctConsumptionService.selectRlctConsumptionVoList(rlctConsumptionVo);
        return getDataTable(list);
    }

    /**
     * 导出消费记录列表
     */
    @ApiOperation("导出消费记录列表")
    @PreAuthorize("@ss.hasPermi('teen:consumption:export')")
    @Log(title = "消费记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctConsumption rlctConsumption)
    {
        List<RlctConsumption> list = rlctConsumptionService.selectRlctConsumptionList(rlctConsumption);
        ExcelUtil<RlctConsumption> util = new ExcelUtil<RlctConsumption>(RlctConsumption.class);
        util.exportExcel(response, list, "消费记录数据");
    }

    /**
     * 获取消费记录详细信息
     */
    @ApiOperation("获取消费记录详细信息")
    @PreAuthorize("@ss.hasPermi('teen:consumption:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rlctConsumptionService.selectRlctConsumptionById(id));
    }

    /**
     * 新增消费记录
     */
    @ApiOperation("新增消费记录")
    @PreAuthorize("@ss.hasPermi('teen:consumption:add')")
    @Log(title = "消费记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctConsumption rlctConsumption)
    {
        return toAjax(rlctConsumptionService.insertRlctConsumption(rlctConsumption));
    }

    /**
     * 修改消费记录
     */
    @PreAuthorize("@ss.hasPermi('teen:consumption:edit')")
    @Log(title = "消费记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctConsumption rlctConsumption)
    {
        return toAjax(rlctConsumptionService.updateRlctConsumption(rlctConsumption));
    }

    /**
     * 删除消费记录
     */
    @PreAuthorize("@ss.hasPermi('teen:consumption:remove')")
    @Log(title = "消费记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rlctConsumptionService.deleteRlctConsumptionByIds(ids));
    }
}
