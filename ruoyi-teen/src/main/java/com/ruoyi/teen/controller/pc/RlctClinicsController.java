package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.teen.utils.ValidateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctClinics;
import com.ruoyi.teen.service.IRlctClinicsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 医馆信息Controller
 *  医馆管理2
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("医馆管理")
@RestController
@RequestMapping("/teen/clinics")
public class RlctClinicsController extends BaseController
{
    @Autowired
    private IRlctClinicsService rlctClinicsService;

    /**
     * 查询医馆信息列表
     */
    @ApiOperation("查询医馆信息列表")
    @PreAuthorize("@ss.hasPermi('teen:clinics:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctClinics rlctClinics)
    {
        startPage();
        List<RlctClinics> list = rlctClinicsService.selectRlctClinicsList(rlctClinics);
        return getDataTable(list);
    }

    /**
     * 导出医馆信息列表
     */
    @ApiOperation("导出医馆信息列表")
    @PreAuthorize("@ss.hasPermi('teen:clinics:export')")
    @Log(title = "医馆信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctClinics rlctClinics)
    {
        List<RlctClinics> list = rlctClinicsService.selectRlctClinicsList(rlctClinics);
        ExcelUtil<RlctClinics> util = new ExcelUtil<RlctClinics>(RlctClinics.class);
        util.exportExcel(response, list, "医馆信息数据");
    }

    /**
     * 获取医馆信息详细信息
     */
    @ApiOperation("获取医馆信息详细信息")
    @PreAuthorize("@ss.hasPermi('teen:clinics:query')")
    @GetMapping(value = "/{clinicId}")
    public AjaxResult getInfo(@PathVariable("clinicId") Long clinicId)
    {
        return success(rlctClinicsService.selectRlctClinicsByClinicId(clinicId));
    }

    /**
     * 新增医馆信息
     */
    @ApiOperation("新增医馆信息")
    @PreAuthorize("@ss.hasPermi('teen:clinics:add')")
    @Log(title = "医馆信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody RlctClinics rlctClinics)
    {
        return toAjax(rlctClinicsService.insertRlctClinics(rlctClinics));
    }

    /**
     * 修改医馆信息
     */
    @ApiOperation("修改医馆信息")
    @PreAuthorize("@ss.hasPermi('teen:clinics:edit')")
    @Log(title = "医馆信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody RlctClinics rlctClinics)
    {
        return toAjax(rlctClinicsService.updateRlctClinics(rlctClinics));
    }

    /**
     * 删除医馆信息
     */
    @ApiOperation("删除医馆信息")
    @PreAuthorize("@ss.hasPermi('teen:clinics:remove')")
    @Log(title = "医馆信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{clinicIds}")
    public AjaxResult remove(@PathVariable Long[] clinicIds)
    {
        return toAjax(rlctClinicsService.deleteRlctClinicsByClinicIds(clinicIds));
    }
}
