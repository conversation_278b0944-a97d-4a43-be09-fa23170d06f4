package com.ruoyi.teen.controller.physician;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.service.IRlctVisitService;
import com.ruoyi.teen.utils.ValidateUtils;
import com.ruoyi.teen.vo.ipad.VisitVo;
import com.ruoyi.teen.vo.RlctVisitVo;
import com.ruoyi.teen.vo.physician.TreatmentImageListBean;
import com.ruoyi.teen.vo.physician.TreatmentImageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27
 */
@Api("就诊记录")
@RestController
@Component("physicianRlctVisitController")
@RequestMapping("/physician/visit")
public class RlctVisitController extends BaseController {

    @Autowired
    private IRlctVisitService rlctVisitService;

    /**
     * 获取就诊记录列表
     * @param teenId 孩子ID
     * @return 结果
     */
    @ApiOperation("获取就诊记录列表")
//    @PreAuthorize("@ss.hasPermi('patient:visit:list')")
    @GetMapping("/list")
    public TableDataInfo List(Long teenId,Integer status)
    {
        startPage();
        List<RlctVisitVo> list = rlctVisitService.getVisitList(teenId,status);
        return getDataTable(list);
    }


//    /**
//     * 完成治疗更新就诊记录和插入就诊记录表
//     *
//     * @param rlctVisitVo
//     * @return
//     */
//    @PostMapping("/finshVisit")
//    public AjaxResult finshVisit(@RequestBody VisitVo rlctVisitVo){
////        return toAjax(rlctVisitService.upIpadVist(rlctVisitVo));
//        return null;
//    }

    @ApiOperation("更新就诊前图片")
    @PostMapping("/updateBeforeTreatmentImageUrl")
    public AjaxResult updateBeforeTreatmentImageUrl(@RequestBody TreatmentImageListBean images){
        rlctVisitService.updateBeforeTreatmentImageUrl(images);
        return AjaxResult.success();
    }

    @ApiOperation("更新就诊后图片")
    @PostMapping("/updateAfterTreatmentImageUrl")
    public AjaxResult updateAfterTreatmentImageUrl(@RequestBody TreatmentImageListBean images){
        rlctVisitService.updateAfterTreatmentImageUrl(images);
        return AjaxResult.success();
    }

    /**
     * 完成治疗
     * @param rlctVisitVo 本次就诊记录实体
     * @return
     */
    @ApiOperation("完成治疗")
    @PostMapping("/finishTreat")
    public AjaxResult finishTreat(@RequestBody RlctVisitVo rlctVisitVo){
        ValidateUtils.checkLong(rlctVisitVo.getVisitId(),"visitId");
        return toAjax(rlctVisitService.finishTreat(rlctVisitVo.getVisitId()));
    }
}
