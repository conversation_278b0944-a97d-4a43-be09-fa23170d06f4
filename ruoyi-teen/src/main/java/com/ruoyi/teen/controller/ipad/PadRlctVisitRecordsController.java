package com.ruoyi.teen.controller.ipad;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.service.IRlctVisitRecordsService;
import com.ruoyi.teen.vo.ipad.VisitRecordsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 就诊记录Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/ipad/VisitRecords")
public class PadRlctVisitRecordsController extends BaseController {

    @Autowired
    private IRlctVisitRecordsService rlctVisitRecordsService;


    /**
     * 就诊记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                              @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        List<VisitRecordsVo> list =rlctVisitRecordsService.selectRlctVisitRecordsList(startDate,endDate);
        // 添加这行代码启动分页
        startPage();
        return getDataTable(list);
    }

}
