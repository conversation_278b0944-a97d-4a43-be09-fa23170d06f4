package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.teen.domain.RlctAuthorize;
import com.ruoyi.teen.service.IRlctAuthorizeService;
import com.ruoyi.teen.utils.ValidateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;

/**
 * 授权Controller
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
@Api("授权")
@RestController
@RequestMapping("/system/authorize")
public class RlctAuthorizeController extends BaseController
{
    @Autowired
    private IRlctAuthorizeService rlctAuthorizeService;

//    /**
//     * 查询授权列表
//     */
//    @PreAuthorize("@ss.hasPermi('system:authorize:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(RlctAuthorize rlctAuthorize)
//    {
//        startPage();
//        List<RlctAuthorize> list = rlctAuthorizeService.selectRlctAuthorizeList(rlctAuthorize);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出授权列表
//     */
//    @PreAuthorize("@ss.hasPermi('system:authorize:export')")
//    @Log(title = "授权", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, RlctAuthorize rlctAuthorize)
//    {
//        List<RlctAuthorize> list = rlctAuthorizeService.selectRlctAuthorizeList(rlctAuthorize);
//        ExcelUtil<RlctAuthorize> util = new ExcelUtil<RlctAuthorize>(RlctAuthorize.class);
//        util.exportExcel(response, list, "授权数据");
//    }
//
//    /**
//     * 获取授权详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('system:authorize:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(rlctAuthorizeService.selectRlctAuthorizeById(id));
//    }
//
//    /**
//     * 新增授权
//     */
//    @PreAuthorize("@ss.hasPermi('system:authorize:add')")
//    @Log(title = "授权", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody RlctAuthorize rlctAuthorize)
//    {
//        return toAjax(rlctAuthorizeService.insertRlctAuthorize(rlctAuthorize));
//    }
//
//    /**
//     * 修改授权
//     */
//    @PreAuthorize("@ss.hasPermi('system:authorize:edit')")
//    @Log(title = "授权", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody RlctAuthorize rlctAuthorize)
//    {
//        return toAjax(rlctAuthorizeService.updateRlctAuthorize(rlctAuthorize));
//    }
//
//    /**
//     * 删除授权
//     */
//    @PreAuthorize("@ss.hasPermi('system:authorize:remove')")
//    @Log(title = "授权", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(rlctAuthorizeService.deleteRlctAuthorizeByIds(ids));
//    }
    @ApiOperation("授权")
        @PreAuthorize("@ss.hasPermi('teen:authorize:add')")
    @Log(title = "授权", businessType = BusinessType.UPDATE)
    @PostMapping("/saveAuthorize")
    public AjaxResult saveAuthorize(@RequestBody RlctAuthorize rlctAuthorize)
    {
        ValidateUtils.checkString(rlctAuthorize.getAuthorizeCode(),"authorizeCode");
        return toAjax(rlctAuthorizeService.saveAuthorize(rlctAuthorize));
    }

    @ApiOperation("获取授权")
    @Log(title = "授权", businessType = BusinessType.UPDATE)
    @GetMapping("/getAuthorize")
    public AjaxResult getAuthorize()
    {
        return AjaxResult.success(rlctAuthorizeService.getAuthorize());
    }

}
