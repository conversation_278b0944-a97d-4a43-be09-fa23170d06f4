package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctProduct;
import com.ruoyi.teen.service.IRlctProductService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产品广告Controller
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@RestController
@RequestMapping("/teen/product")
public class RlctProductController extends BaseController
{
    @Autowired
    private IRlctProductService rlctProductService;

    /**
     * 查询产品广告列表
     */
    @PreAuthorize("@ss.hasPermi('teen:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctProduct rlctProduct)
    {
        startPage();
        List<RlctProduct> list = rlctProductService.selectRlctProductList(rlctProduct);
        return getDataTable(list);
    }

    /**
     * 导出产品广告列表
     */
    @PreAuthorize("@ss.hasPermi('teen:product:export')")
    @Log(title = "产品广告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctProduct rlctProduct)
    {
        List<RlctProduct> list = rlctProductService.selectRlctProductList(rlctProduct);
        ExcelUtil<RlctProduct> util = new ExcelUtil<RlctProduct>(RlctProduct.class);
        util.exportExcel(response, list, "产品广告数据");
    }

    /**
     * 获取产品广告详细信息
     */
    @PreAuthorize("@ss.hasPermi('teen:product:query')")
    @GetMapping(value = "/{productId}")
    public AjaxResult getInfo(@PathVariable("productId") Long productId)
    {
        return success(rlctProductService.selectRlctProductByProductId(productId));
    }

    /**
     * 新增产品广告
     */
    @PreAuthorize("@ss.hasPermi('teen:product:add')")
    @Log(title = "产品广告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctProduct rlctProduct)
    {
        return toAjax(rlctProductService.insertRlctProduct(rlctProduct));
    }

    /**
     * 修改产品广告
     */
    @PreAuthorize("@ss.hasPermi('teen:product:edit')")
    @Log(title = "产品广告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctProduct rlctProduct)
    {
        return toAjax(rlctProductService.updateRlctProduct(rlctProduct));
    }

    /**
     * 删除产品广告
     */
    @PreAuthorize("@ss.hasPermi('teen:product:remove')")
    @Log(title = "产品广告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{productIds}")
    public AjaxResult remove(@PathVariable Long[] productIds)
    {
        return toAjax(rlctProductService.deleteRlctProductByProductIds(productIds));
    }
}
