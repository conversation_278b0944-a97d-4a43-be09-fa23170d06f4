package com.ruoyi.teen.controller.pc;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctPadVersion;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.service.pc.PCRlctVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api("版本管理")
@RestController
@RequestMapping("/system/version")
public class RlctVersionController extends BaseController {

    @Autowired
    private PCRlctVersionService rlctVersionService;


    /**
     * 查询版本列表
     * */
    @ApiOperation("查询孩子列表")
    @PreAuthorize("@ss.hasPermi('system:version:list')")  // 关联菜单权限控制
    @GetMapping("/list")
    public TableDataInfo list(RlctPadVersion rlctPadVersion) {
        startPage(); // 启动分页
        List<RlctPadVersion> list = rlctVersionService.selectRlctVersionList(rlctPadVersion);
        return getDataTable(list);
    }


    /**
     * 新增孩子
     */
    @ApiOperation("新增版本")
    @PreAuthorize("@ss.hasPermi('system:version:add')")
    @Log(title = "版本", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctPadVersion rlctPadVersion)
    {
        return toAjax(rlctVersionService.insertRlctVersion(rlctPadVersion));
    }

    // 获取单个版本详情
    @ApiOperation("获取版本详情")
    @PreAuthorize("@ss.hasPermi('system:version:query')")
    @GetMapping("/{versionId}")
    public AjaxResult getInfo(@PathVariable("versionId") Long versionId)
    {
        return AjaxResult.success(rlctVersionService.selectRlctVersionByVersionId(versionId));
    }


    //修改版本
    @ApiOperation("修改版本")
    @PreAuthorize("@ss.hasPermi('system:version:edit')")
    @Log(title = "版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctPadVersion rlctPadVersion)
    {
        return toAjax(rlctVersionService.updateRlctVersion(rlctPadVersion));
    }

    // 删除版本
    @ApiOperation("删除版本")
    @PreAuthorize("@ss.hasPermi('system:version:remove')")
    @Log(title = "版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{versionIds}")
    public AjaxResult remove(@PathVariable Long[] versionIds){
        return toAjax(rlctVersionService.deleteRlctVersionByVersionIds(versionIds));
    }
}
