package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctTreatments;
import com.ruoyi.teen.service.IRlctTreatmentsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目Controller
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@RestController
@RequestMapping("/teen/treatments")
public class RlctTreatmentsController extends BaseController
{
    @Autowired
    private IRlctTreatmentsService rlctTreatmentsService;

    /**
     * 查询项目列表
     */
    @PreAuthorize("@ss.hasPermi('teen:treatments:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctTreatments rlctTreatments)
    {
        startPage();
        List<RlctTreatments> list = rlctTreatmentsService.selectRlctTreatmentsList(rlctTreatments);
        return getDataTable(list);
    }

    /**
     * 导出项目列表
     */
    @PreAuthorize("@ss.hasPermi('teen:treatments:export')")
    @Log(title = "项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctTreatments rlctTreatments)
    {
        List<RlctTreatments> list = rlctTreatmentsService.selectRlctTreatmentsList(rlctTreatments);
        ExcelUtil<RlctTreatments> util = new ExcelUtil<RlctTreatments>(RlctTreatments.class);
        util.exportExcel(response, list, "项目数据");
    }

    /**
     * 获取项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('teen:treatments:query')")
    @GetMapping(value = "/{treatmentId}")
    public AjaxResult getInfo(@PathVariable("treatmentId") Long treatmentId)
    {
        return success(rlctTreatmentsService.selectRlctTreatmentsByTreatmentId(treatmentId));
    }

    /**
     * 新增项目
     */
    @PreAuthorize("@ss.hasPermi('teen:treatments:add')")
    @Log(title = "项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctTreatments rlctTreatments)
    {
        return toAjax(rlctTreatmentsService.insertRlctTreatments(rlctTreatments));
    }

    /**
     * 修改项目
     */
    @PreAuthorize("@ss.hasPermi('teen:treatments:edit')")
    @Log(title = "项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctTreatments rlctTreatments)
    {
        return toAjax(rlctTreatmentsService.updateRlctTreatments(rlctTreatments));
    }

    /**
     * 删除项目
     */
    @PreAuthorize("@ss.hasPermi('teen:treatments:remove')")
    @Log(title = "项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{treatmentIds}")
    public AjaxResult remove(@PathVariable Long[] treatmentIds)
    {
        return toAjax(rlctTreatmentsService.deleteRlctTreatmentsByTreatmentIds(treatmentIds));
    }
}
