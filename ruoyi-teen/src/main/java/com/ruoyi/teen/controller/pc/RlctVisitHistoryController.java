package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctVisitHistory;
import com.ruoyi.teen.service.IRlctVisitHistoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 就诊历史记录Controller
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@RestController
@RequestMapping("/teen/visit_history")
public class RlctVisitHistoryController extends BaseController
{
    @Autowired
    private IRlctVisitHistoryService rlctVisitHistoryService;

    /**
     * 查询就诊历史记录列表
     */
    @PreAuthorize("@ss.hasPermi('teen:visit_history:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctVisitHistory rlctVisitHistory)
    {
        startPage();
        List<RlctVisitHistory> list = rlctVisitHistoryService.selectRlctVisitHistoryList(rlctVisitHistory);
        return getDataTable(list);
    }

    /**
     * 导出就诊历史记录列表
     */
    @PreAuthorize("@ss.hasPermi('teen:visit_history:export')")
    @Log(title = "就诊历史记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctVisitHistory rlctVisitHistory)
    {
        List<RlctVisitHistory> list = rlctVisitHistoryService.selectRlctVisitHistoryList(rlctVisitHistory);
        ExcelUtil<RlctVisitHistory> util = new ExcelUtil<RlctVisitHistory>(RlctVisitHistory.class);
        util.exportExcel(response, list, "就诊历史记录数据");
    }

    /**
     * 获取就诊历史记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('teen:visit_history:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rlctVisitHistoryService.selectRlctVisitHistoryById(id));
    }

    /**
     * 新增就诊历史记录
     */
    @PreAuthorize("@ss.hasPermi('teen:visit_history:add')")
    @Log(title = "就诊历史记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctVisitHistory rlctVisitHistory)
    {
        return toAjax(rlctVisitHistoryService.insertRlctVisitHistory(rlctVisitHistory));
    }

    /**
     * 修改就诊历史记录
     */
    @PreAuthorize("@ss.hasPermi('teen:visit_history:edit')")
    @Log(title = "就诊历史记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctVisitHistory rlctVisitHistory)
    {
        return toAjax(rlctVisitHistoryService.updateRlctVisitHistory(rlctVisitHistory));
    }

    /**
     * 删除就诊历史记录
     */
    @PreAuthorize("@ss.hasPermi('teen:visit_history:remove')")
    @Log(title = "就诊历史记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rlctVisitHistoryService.deleteRlctVisitHistoryByIds(ids));
    }
}
