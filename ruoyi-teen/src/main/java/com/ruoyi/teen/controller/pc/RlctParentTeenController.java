package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.service.IRlctParentTeenService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 家长和孩子的关系Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("家长和孩子的关系")
@RestController
@RequestMapping("/teen/parent_teen")
public class RlctParentTeenController extends BaseController
{
    @Autowired
    private IRlctParentTeenService rlctParentTeenService;

    /**
     * 查询家长和孩子的关系列表
     */
    @ApiOperation("查询家长和孩子的关系列表")
    @PreAuthorize("@ss.hasPermi('teen:parent_teen:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctParentTeen rlctParentTeen)
    {
        startPage();
        List<RlctParentTeen> list = rlctParentTeenService.selectRlctParentTeenList(rlctParentTeen);
        return getDataTable(list);
    }

    /**
     * 导出家长和孩子的关系列表
     */
    @ApiOperation("导出家长和孩子的关系列表")
    @PreAuthorize("@ss.hasPermi('teen:parent_teen:export')")
    @Log(title = "家长和孩子的关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctParentTeen rlctParentTeen)
    {
        List<RlctParentTeen> list = rlctParentTeenService.selectRlctParentTeenList(rlctParentTeen);
        ExcelUtil<RlctParentTeen> util = new ExcelUtil<RlctParentTeen>(RlctParentTeen.class);
        util.exportExcel(response, list, "家长和孩子的关系数据");
    }

    /**
     * 获取家长和孩子的关系详细信息
     */
    @ApiOperation("获取家长和孩子的关系详细信息")
    @PreAuthorize("@ss.hasPermi('teen:parent_teen:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rlctParentTeenService.selectRlctParentTeenById(id));
    }

    /**
     * 新增家长和孩子的关系
     */
    @ApiOperation("新增家长和孩子的关系")
    @PreAuthorize("@ss.hasPermi('teen:parent_teen:add')")
    @Log(title = "家长和孩子的关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctParentTeen rlctParentTeen)
    {
        return toAjax(rlctParentTeenService.insertRlctParentTeen(rlctParentTeen));
    }

    /**
     * 修改家长和孩子的关系
     */
    @ApiOperation("修改家长和孩子的关系")
    @PreAuthorize("@ss.hasPermi('teen:parent_teen:edit')")
    @Log(title = "家长和孩子的关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctParentTeen rlctParentTeen)
    {
        return toAjax(rlctParentTeenService.updateRlctParentTeen(rlctParentTeen));
    }

    /**
     * 删除家长和孩子的关系
     */
    @ApiOperation("删除家长和孩子的关系")
    @PreAuthorize("@ss.hasPermi('teen:parent_teen:remove')")
    @Log(title = "家长和孩子的关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rlctParentTeenService.deleteRlctParentTeenByIds(ids));
    }
}
