package com.ruoyi.teen.controller.patient;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.domain.statistic.EchartSeriesData;
import com.ruoyi.teen.service.IRlctTeenService;
import com.ruoyi.teen.service.IRlctTeenTwoService;
import com.ruoyi.teen.service.ITeenStatisticService;
import com.ruoyi.teen.utils.ValidateUtils;
import com.ruoyi.teen.vo.ipad.RecordListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27
 */
@Api("家长端家长管理")
@RestController
@Component("patientRlctTeenController")
@RequestMapping("/patient/teen")
public class RlctTeenController extends BaseController {

    @Autowired
    private IRlctTeenService rlctTeenService;
    @Autowired
    private ITeenStatisticService teenStatisticService;
    @Autowired
    private IRlctTeenTwoService rlctTeenTwoService;

    /**
     * 查询孩子列表
     */
//    @PreAuthorize("@ss.hasPermi('patient:teen:teenList')")
    @ApiOperation("查询孩子列表")
    @GetMapping("/querylists/{tel}")
    public List<RlctTeen> getTeenListByTel(@PathVariable("tel") String tel)
    {
        return rlctTeenService.getListByTel(tel);
    }

    /**
    * 二期医生查询患者列表
    * */
    @ApiOperation("医生查询患者列表")
    @GetMapping("/querylist")
    public TableDataInfo getTeenListByTel(@RequestParam(required = false) String phonenumber,
                                          @RequestParam(required = false) String nameCh,
                                          @RequestParam(required = false) String keyword
    )
    {
        // 添加这行代码启动分页
        startPage();
        List<RecordListVo> list = rlctTeenTwoService.selectRlctTeenTwoqueryList(phonenumber,nameCh,keyword);
        return getDataTable(list);
    }

    /**
     * 家长新增（绑定）孩子
     */
    @ApiOperation("家长新增（绑定）孩子")
//    @PreAuthorize("@ss.hasPermi('patient:teen:add')")
    @PostMapping
    public AjaxResult add(@RequestBody RlctTeen rlctTeen) {
        try {
            // 校验孩子的名字和性别不能为空
            ValidateUtils.checkString(rlctTeen.getTeenName(), "孩子姓名");
            ValidateUtils.checkString(rlctTeen.getTeenSex(), "孩子性别");

            // 执行插入孩子操作
            return toAjax(rlctTeenService.insertTeen(rlctTeen));
        } catch (RuntimeException e) {
            // 参数校验失败，返回错误信息
            return AjaxResult.error("参数校验失败: " + e.getMessage());
        }
    }



    @Anonymous
    @GetMapping("/initTeenNameJob")
    public AjaxResult initTeenNameJob() {
        try {
            // 执行插入孩子操作
            int result = rlctTeenService.initTeenNameJob();
            return AjaxResult.success("更新记录数："+result);
        } catch (RuntimeException e) {
            // 参数校验失败，返回错误信息
            return AjaxResult.error("参数校验失败: " + e.getMessage());
        }
    }

    /**
     * 获取孩子信息
     */
//    @PreAuthorize("@ss.hasPermi('patient:teen:teenInfo')")
    @ApiOperation("获取孩子信息")
    @GetMapping("/teenInfo/{teenId}")
    public AjaxResult getTeenInfo(@PathVariable("teenId") Long teenId)
    {
        return AjaxResult.success(rlctTeenService.selectRlctTeenByTeenId(teenId));
    }


    //根据该手机号码下的孩子查询详情--图表数据：身高体重数据接口
    @ApiOperation("根据该手机号码下的孩子查询详情--图表数据：身高体重数据接口")
    @GetMapping("/statisticHeightWightByTeenId")
    public AjaxResult statisticHeightWightByTeenId(@RequestParam Long teenId){
        try {
            EchartSeriesData seriesData = teenStatisticService.statisticHeightWightByTeenId(teenId);
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("statisticHeightWightByTeenId!teenId="+teenId,e);
            return AjaxResult.error(e);
        }
    }


    //孩子五维图数据接口
    @ApiOperation("孩子五维图数据接口")
    @GetMapping("/statisticRadarByTeenId")
    @Anonymous
    public AjaxResult statisticRadarByTeenId(@RequestParam Long teenId){
        try {
            EchartSeriesData seriesData = teenStatisticService.statisticRadarByTeenId(teenId);
            return AjaxResult.success(seriesData);
        } catch (Exception e) {
            logger.error("statisticRadarByTeenId!teenId="+teenId,e);
            return AjaxResult.error(e);
        }
    }
}
