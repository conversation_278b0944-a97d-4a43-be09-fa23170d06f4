package com.ruoyi.teen.controller.ipad;

import java.util.List;


import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.vo.ipad.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.teen.service.IRlctTeenTwoService;

/**
 * 孩子Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/ipad/teen")
public class PadRlctTeenTwoController extends BaseController {
    @Autowired
    private IRlctTeenTwoService rlctTeenTwoService;

    /**
     * 带条件查询孩子列表 http://localhost:38812/teenapi/ipad/teen/querylist?phonenumber=18850171696&nameCh=dyh
     */
    @GetMapping("/querylist")
    public TableDataInfo alist(@RequestParam(required = false) String phonenumber,
                               @RequestParam(required = false) String nameCh,
                               @RequestParam(required = false) String keyword
    ) {
        // 添加这行代码启动分页
        startPage();
        List<RecordListVo> list = rlctTeenTwoService.selectRlctTeenTwoqueryList(phonenumber, nameCh, keyword);
        return getDataTable(list);
    }

    /**
     * 就诊（档案）详情表
     */
    @GetMapping("/recorddetail")
    public AjaxResult recordDetail(@RequestParam Long parent_teen_id) {
        try {
            RecorddDetailVo recordddetailvo = rlctTeenTwoService.selectRlctTeenTwoByTeenId(parent_teen_id);
            AjaxResult success = success(recordddetailvo);
            return success;
        } catch (Exception e) {
            return error("parent_teen_id错误: " + e.getMessage());
        }
    }

    /**
     * 就诊档案详情（按下导诊《接诊》按钮 (查询用户已购套餐列表和类型是医师的列表
     */
    @GetMapping("/treatmentType/selection")
    public AjaxResult treatmentTypeSelection(@RequestParam Long parentTeenId) {
        try {
            PadTreatmentTypeSelectionVo padTreatmentTypeSelectionVo = rlctTeenTwoService.treatmentTypeSelection(parentTeenId);
            AjaxResult success = success(padTreatmentTypeSelectionVo);
            return success;
        } catch (Exception e) {
            return error("parent_teen_id错误: " + e.getMessage());
        }
    }


    /**
     * 获取孩子详细信息
     */
    @PreAuthorize("@ss.hasPermi('teen:teen:query')")
    @GetMapping(value = "/{teenId}")
    public AjaxResult getInfo(@PathVariable("teenId") Long teenId) {
        return success(rlctTeenTwoService.selectRlctTeenTwoByTeenId(teenId));
    }


    /**
     * 新增孩子
     */
    @PostMapping
    public AjaxResult add(@RequestBody RecordVo recordVo) {
        try {
            return success(rlctTeenTwoService.insertRlctTeenTwo(recordVo));
        } catch (Exception e) {
            return error("新增孩子信息失败: " + e.getMessage());
        }
    }


}
