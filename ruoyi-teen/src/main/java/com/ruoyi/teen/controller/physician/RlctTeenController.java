package com.ruoyi.teen.controller.physician;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.service.IRlctTeenService;
import com.ruoyi.teen.utils.ValidateUtils;
import com.ruoyi.teen.vo.ipad.PadPatientDetailVo;
import com.ruoyi.teen.vo.ipad.PadPatientVo;
import com.ruoyi.teen.vo.physician.RlctTeenVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Api("家长管理")
@RestController
@Component("physicianRlctTeenController")
@RequestMapping("/physician/teen")
public class RlctTeenController extends BaseController {

    @Autowired
    private IRlctTeenService rlctTeenService;

    @ApiOperation("获取家长列表")
    @GetMapping("/patientList")
    public TableDataInfo patientList(@RequestParam String tel) {
        List<PadPatientVo> list = rlctTeenService.patientList(tel);
        return getDataTable(list);
    }

    //根据手机号码查询详细信息（建档时间、就诊次数、未使用次数 、患者列表）
    @ApiOperation("根据手机号码查询详细信息（建档时间、就诊次数、未使用次数 、患者列表）")
    @GetMapping("/patientDetailByTel")
    public AjaxResult patientDetailByTel(@RequestParam String tel){
        try {
            PadPatientDetailVo vo =  rlctTeenService.patientDetailByTel(tel);
            return AjaxResult.success(vo);
        } catch (Exception e) {
            logger.error("patientDetailByTel!tel="+tel,e);
            return AjaxResult.error(e);
        }
    }
//
//    /**
//     * 查询孩子列表
//     */
////    @PreAuthorize("@ss.hasPermi('physician:teen:teenList')")
//    @GetMapping("/teenList/{tel}")
//    public List<RlctTeen> getTeenListByTel(@PathVariable("tel") String tel)
//    {
//        return rlctTeenService.getListByTel(tel);
//    }

    @ApiOperation("查询孩子列表")
    @GetMapping("/teenInfo/{teenId}")
    public AjaxResult getTeenInfo(@PathVariable("teenId") Long teenId)
    {
        return AjaxResult.success(rlctTeenService.selectRlctTeenByTeenId(teenId));
    }
}
