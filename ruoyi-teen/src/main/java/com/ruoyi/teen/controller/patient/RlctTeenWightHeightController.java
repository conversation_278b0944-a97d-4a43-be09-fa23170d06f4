package com.ruoyi.teen.controller.patient;

import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.domain.RlctTeenWightHeight;
import com.ruoyi.teen.service.IRlctTeenWightHeightService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5
 */
@Api("孩子的身高体重")
@RestController
@Component("patientRlctTeenWightHeightController")
@RequestMapping("/patient/body")
public class RlctTeenWightHeightController {

    @Autowired
    private IRlctTeenWightHeightService rlctTeenWightHeightService;

    /**
     * 获取孩子的身高体重
     * @param parentTeen 父母孩子关系实体
     * @return 结果
     */
//    @PreAuthorize("@ss.hasPermi('patient:body:bodyInfo')")
    @ApiOperation("获取孩子的身高体重")
    @PostMapping("/bodyInfo")
    public List<RlctTeenWightHeight> bodyInfo(@RequestBody RlctParentTeen parentTeen)
    {
        return rlctTeenWightHeightService.getBodyInfo(parentTeen);
    }

}
