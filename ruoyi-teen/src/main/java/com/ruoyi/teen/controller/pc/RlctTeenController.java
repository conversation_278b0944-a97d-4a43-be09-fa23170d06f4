package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.service.IRlctTeenService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 孩子Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("孩子管理")
@RestController
@RequestMapping("/teen/teen")
public class RlctTeenController extends BaseController
{
    @Autowired
    private IRlctTeenService rlctTeenService;
    
    /**
     * 查询孩子列表
     */
    @ApiOperation("查询孩子列表")
    @PreAuthorize("@ss.hasPermi('teen:teen:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctTeen rlctTeen)
    {
        startPage();
        List<RlctTeen> list = rlctTeenService.selectRlctTeenList(rlctTeen);
        return getDataTable(list);
    }

    /**
     * 导出孩子列表
     */
    @ApiOperation("导出孩子列表")
    @PreAuthorize("@ss.hasPermi('teen:teen:export')")
    @Log(title = "孩子", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctTeen rlctTeen)
    {
        List<RlctTeen> list = rlctTeenService.selectRlctTeenList(rlctTeen);
        ExcelUtil<RlctTeen> util = new ExcelUtil<RlctTeen>(RlctTeen.class);
        util.exportExcel(response, list, "孩子数据");
    }

    /**
     * 获取孩子详细信息
     */
    @ApiOperation("获取孩子详细信息")
    @PreAuthorize("@ss.hasPermi('teen:teen:query')")
    @GetMapping(value = "/{teenId}")
    public AjaxResult getInfo(@PathVariable("teenId") Long teenId)
    {
        return success(rlctTeenService.selectRlctTeenByTeenId(teenId));
    }

    /**
     * 新增孩子
     */
    @ApiOperation("新增孩子")
    @PreAuthorize("@ss.hasPermi('teen:teen:add')")
    @Log(title = "孩子", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctTeen rlctTeen)
    {
        return toAjax(rlctTeenService.insertRlctTeen(rlctTeen));
    }

    /**
     * 修改孩子
     */
    @ApiOperation("修改孩子")
    @PreAuthorize("@ss.hasPermi('teen:teen:edit')")
    @Log(title = "孩子", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctTeen rlctTeen)
    {
        return toAjax(rlctTeenService.updateRlctTeen(rlctTeen));
    }

    /**
     * 删除孩子
     */
    @ApiOperation("删除孩子")
    @PreAuthorize("@ss.hasPermi('teen:teen:remove')")
    @Log(title = "孩子", businessType = BusinessType.DELETE)
	@DeleteMapping("/{teenIds}")
    public AjaxResult remove(@PathVariable Long[] teenIds)
    {
        return toAjax(rlctTeenService.deleteRlctTeenByTeenIds(teenIds));
    }
}
