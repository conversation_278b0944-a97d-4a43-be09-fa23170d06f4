package com.ruoyi.teen.controller.ipad;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.teen.domain.RlctPadVersion;
import com.ruoyi.teen.service.PadVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/versions")
public class PadVersionController {


    @Autowired
    private PadVersionService padVersionService;


    /**
    * 版本校验
    * */
    @PostMapping("/latest")
    public AjaxResult getLatestVersion(@RequestBody RlctPadVersion rlctPadVersion) {
        // 获取最新版本 URL


        return padVersionService.getLatestVersionUrl(rlctPadVersion);
    }

//    @PostMapping
//    public ResponseEntity<Void> createVersion(@RequestBody VersionDto dto) {
//        // 创建新版本
//    }
}
