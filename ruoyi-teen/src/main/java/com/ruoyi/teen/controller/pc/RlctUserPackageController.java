package com.ruoyi.teen.controller.pc;

import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.teen.utils.ValidateUtils;
import com.ruoyi.teen.vo.pc.RlctUserPackagePCVo;

import com.ruoyi.teen.vo.pc.RlctReturnRecordVo;
import com.ruoyi.teen.vo.pc.RlctUserPackageSaveVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctUserPackage;
import com.ruoyi.teen.service.IRlctUserPackageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户和套餐关系Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@RestController
@RequestMapping("/teen/user_package")
public class RlctUserPackageController extends BaseController
{
    @Autowired
    private IRlctUserPackageService rlctUserPackageService;


    /**
     * 查询用户和套餐关系列表
     */
    @PreAuthorize("@ss.hasPermi('teen:user_package:list')")
    @GetMapping("/PClist")
    public TableDataInfo list(RlctUserPackagePCVo RlctUserPackagePCvo)
    {
        startPage();
        List<RlctUserPackagePCVo> list = rlctUserPackageService.selectRlctUserPackagePCVoList(RlctUserPackagePCvo);
        return getDataTable(list);
    }


    /**
     * 查询用户和套餐关系列表
     */
    @PreAuthorize("@ss.hasPermi('teen:user_package:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctUserPackage rlctUserPackage)
    {
        startPage();
        List<RlctUserPackage> list = rlctUserPackageService.selectRlctUserPackageList(rlctUserPackage);
        return getDataTable(list);
    }

    /**
     * 导出用户和套餐关系列表
     */
    @PreAuthorize("@ss.hasPermi('teen:user_package:export')")
    @Log(title = "用户和套餐关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctUserPackage rlctUserPackage)
    {
        List<RlctUserPackage> list = rlctUserPackageService.selectRlctUserPackageList(rlctUserPackage);
        ExcelUtil<RlctUserPackage> util = new ExcelUtil<RlctUserPackage>(RlctUserPackage.class);
        util.exportExcel(response, list, "用户和套餐关系数据");
    }

    /**
     * 获取用户和套餐关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('teen:user_package:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rlctUserPackageService.selectRlctUserPackageById(id));
    }

    /**
     * 新增用户和套餐关系
     */
    @PreAuthorize("@ss.hasPermi('teen:user_package:add')")
    @Log(title = "用户和套餐关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctUserPackageSaveVo rlctUserPackage)
    {
        return toAjax(rlctUserPackageService.insertRlctUserPackage(rlctUserPackage));
    }

    /**
     * 修改用户和套餐关系
     */
    @PreAuthorize("@ss.hasPermi('teen:user_package:edit')")
    @Log(title = "用户和套餐关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctUserPackage rlctUserPackage)
    {
        return toAjax(rlctUserPackageService.updateRlctUserPackage(rlctUserPackage));
    }

    /**
     * 删除用户和套餐关系
     */
    @PreAuthorize("@ss.hasPermi('teen:user_package:remove')")
    @Log(title = "用户和套餐关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rlctUserPackageService.deleteRlctUserPackageByIds(ids));
    }


    /**
     * 查询用户退费列表
     */
//    @PreAuthorize("@ss.hasPermi('teen:return_record:list')")
    @GetMapping("/return/record/list")
    public TableDataInfo getReturnRecordList(RlctReturnRecordVo rlctUserPackage)
    {
        startPage();
        List<RlctReturnRecordVo> list = rlctUserPackageService.selectReturnRecordList(rlctUserPackage);
        return getDataTable(list);
    }



    @Log(title = "退费", businessType = BusinessType.UPDATE)
    @PostMapping("/userPackageReturn")
    public AjaxResult userPackageReturn(@RequestBody RlctUserPackage rlctUserPackage)
    {
        ValidateUtils.checkBigDecimal(rlctUserPackage.getPackageReturnPrice(),"packageReturnPrice");
        ValidateUtils.checkLong(rlctUserPackage.getId(),"id");
        if(rlctUserPackage.getPackageReturnPrice().compareTo(BigDecimal.ZERO) != 1){
            throw new RuntimeException("金额必须大于0");
        }
        return toAjax(rlctUserPackageService.userPackageReturn(rlctUserPackage));
    }
}
