package com.ruoyi.teen.controller.physician;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.teen.utils.ValidateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/12/11
 */
@Api("医师")
@RestController
@Component("physicianRlctUserController")
@RequestMapping("/physician/user")
public class RlctUserController extends BaseController {

    @Autowired
    private ISysUserService userService;
    /**
     * 医师开通申请
     */
    @ApiOperation("医师开通申请")
//    @PreAuthorize("@ss.hasPermi('physician:user:edit')")
    @Log(title = "医师", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody SysUser sysUser) {
        try {
            // 校验字段
            ValidateUtils.checkString(String.valueOf(sysUser.getUserId()), "用户id");
            ValidateUtils.checkString(sysUser.getPhonenumber(), "医师手机号码");
            ValidateUtils.checkString(sysUser.getNickName(), "医师姓名");
            ValidateUtils.checkString(sysUser.getSex(), "医师性别");
            ValidateUtils.checkString(sysUser.getStatus(), "账号状态");

            // 执行更新操作
            return toAjax(userService.updateUserStatus(sysUser));
        } catch (RuntimeException e) {
            // 参数校验失败，返回错误信息
            return AjaxResult.error("参数校验失败: " + e.getMessage());
        }
    }

}
