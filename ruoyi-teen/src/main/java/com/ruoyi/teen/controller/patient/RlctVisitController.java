package com.ruoyi.teen.controller.patient;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.service.IRlctVisitService;
import com.ruoyi.teen.vo.RlctVisitVo;
import com.ruoyi.teen.vo.ipad.SubmitProductVo;
import com.ruoyi.teen.vo.ipad.SubmitRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27
 */
@Api("就诊记录")
@RestController
@Component("patientRlctVisitController")
@RequestMapping("/patient/visit")
public class RlctVisitController extends BaseController {

    @Autowired
    private IRlctVisitService rlctVisitService;

    /**
     * 获取就诊记录列表
     * @param teenId 孩子ID
     * @return 结果
     */
    @ApiOperation("获取就诊记录列表")
//    @PreAuthorize("@ss.hasPermi('patient:visit:list')")
    @GetMapping("/list")
    public TableDataInfo List(Long teenId,Integer status)
    {
        startPage();
        List<RlctVisitVo> list = rlctVisitService.getVisitList(teenId,status);
        for (RlctVisitVo visitVo : list) {
            visitVo.setBeforeTreatmentImageUrl(null);
            visitVo.setAfterTreatmentImageUrl(null);
            visitVo.setBeforeImages(null);
            visitVo.setAfterImages(null);
        }
        return getDataTable(list);
    }

    /**
     *  提交暂存行为记录 或  提交就诊记录  或分配中的提交接诊
     * */
    @PostMapping("/submitstorerecord")
    public AjaxResult submitStoreRecord(@RequestBody SubmitRecordVo submitRecordVo)
    {
        try{
            /*status 2L 暂存行为*/             /*status 0L 治疗中*/    /*1L 分配套餐*/
            return rlctVisitService.insertRlctSubmitStoreRecord(submitRecordVo,submitRecordVo.getStatus());
        }catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
    * 提交只开产品
    * */
    @PostMapping("/submitproduct")
    public AjaxResult submitProduct(@RequestBody SubmitProductVo submitProductVo)
    {
        try{
            int i  = rlctVisitService.insertsubmitProduct(submitProductVo);
            AjaxResult success = success(i);
            return success;
        }catch (Exception e) {
            return error(e.getMessage());
        }
    }


}
