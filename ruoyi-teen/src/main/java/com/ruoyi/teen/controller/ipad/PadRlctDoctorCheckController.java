package com.ruoyi.teen.controller.ipad;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.service.IRlctDoctorCheckService;
import com.ruoyi.teen.vo.ipad.PadConfirmCheckVo;
import com.ruoyi.teen.vo.ipad.RlctDoctorVisicDVO;
import com.ruoyi.teen.vo.ipad.RlctDoctorVisicVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ruoyi.common.utils.PageUtils.startPage;

/**
 * 巡诊审核Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/ipad/doctorvisic")
public class PadRlctDoctorCheckController extends BaseController {

    @Autowired
    private IRlctDoctorCheckService rlctdoctorCheckService;

    /**
     * 巡诊审核列表 0待审核 1审核通过
     */
    @GetMapping("/checklist")
    public TableDataInfo checklist(@RequestParam(required = true) Integer auditStatus) { // 1-通过 2-未通过
        //分页启动
        startPage();
        List<RlctDoctorVisicVo> list = rlctdoctorCheckService.doctornochecklist(auditStatus);
        return getDataTable(list);
    }

    /**
     * 调整项目中确定按钮
     */
    @PostMapping("/confirmcheck")
    public AjaxResult check(@RequestBody PadConfirmCheckVo padConfirmCheckVo) {
        return rlctdoctorCheckService.confirmcheck(padConfirmCheckVo);
    }

    /**
     * 审核通过 或 撤销审核按钮
     */
    @PostMapping("/checkstatus")
    public AjaxResult checkstatus(@RequestBody RlctDoctorVisicDVO rlctDoctorVisicDvo) {
        return rlctdoctorCheckService.checkstatus(rlctDoctorVisicDvo);
    }


    /*
    *  伪删除治疗记录
    * @param rlctDoctorVisicVo 包含记录ID的VO对象
    * @return 操作结果
    * */
    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody RlctDoctorVisicVo rlctDoctorVisicVo) {
        return rlctdoctorCheckService.delete(rlctDoctorVisicVo);
    }
}
