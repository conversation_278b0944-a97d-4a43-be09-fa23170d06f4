package com.ruoyi.teen.controller.pc;

import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.teen.utils.ValidateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctPackages;
import com.ruoyi.teen.service.IRlctPackagesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 套餐Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("套餐管理")
@RestController
@RequestMapping("/teen/packages")
public class RlctPackagesController extends BaseController
{
    @Autowired
    private IRlctPackagesService rlctPackagesService;

    /**
     * 查询套餐列表
     */
    @ApiOperation("查询套餐列表")
    @PreAuthorize("@ss.hasPermi('teen:packages:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctPackages rlctPackages)
    {
        startPage();
        List<RlctPackages> list = rlctPackagesService.selectRlctPackagesList(rlctPackages);
        return getDataTable(list);
    }

    /**
     * 导出套餐列表
     */
    @ApiOperation("导出套餐列表")
    @PreAuthorize("@ss.hasPermi('teen:packages:export')")
    @Log(title = "套餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctPackages rlctPackages)
    {
        List<RlctPackages> list = rlctPackagesService.selectRlctPackagesList(rlctPackages);
        ExcelUtil<RlctPackages> util = new ExcelUtil<RlctPackages>(RlctPackages.class);
        util.exportExcel(response, list, "套餐数据");
    }

    /**
     * 获取套餐详细信息
     */
    @ApiOperation("获取套餐详细信息")
    @PreAuthorize("@ss.hasPermi('teen:packages:query')")
    @GetMapping(value = "/{packageId}")
    public AjaxResult getInfo(@PathVariable("packageId") Long packageId)
    {
        return success(rlctPackagesService.selectRlctPackagesByPackageId(packageId));
    }

    /**
     * 新增套餐
     */
    @ApiOperation("新增套餐")
    @PreAuthorize("@ss.hasPermi('teen:packages:add')")
    @Log(title = "套餐", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody RlctPackages rlctPackages)
    {
        ValidateUtils.checkBigDecimal(rlctPackages.getPackagePrice(),"套餐价格");
        ValidateUtils.checkBigDecimal(rlctPackages.getPackageMemberPrice(),"套餐会员价");
        return toAjax(rlctPackagesService.insertRlctPackages(rlctPackages));
    }

    /**
     * 修改套餐
     */
    @ApiOperation("修改套餐")
    @PreAuthorize("@ss.hasPermi('teen:packages:edit')")
    @Log(title = "套餐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody RlctPackages rlctPackages)
    {
        ValidateUtils.checkBigDecimal(rlctPackages.getPackagePrice(),"套餐价格");
        ValidateUtils.checkBigDecimal(rlctPackages.getPackageMemberPrice(),"套餐会员价");
        return toAjax(rlctPackagesService.updateRlctPackages(rlctPackages));
    }

    /**
     * 删除套餐
     */
    @ApiOperation("删除套餐")
    @PreAuthorize("@ss.hasPermi('teen:packages:remove')")
    @Log(title = "套餐", businessType = BusinessType.DELETE)
	@DeleteMapping("/{packageIds}")
    public AjaxResult remove(@PathVariable Long[] packageIds)
    {
        return toAjax(rlctPackagesService.deleteRlctPackagesByPackageIds(packageIds));
    }
}
