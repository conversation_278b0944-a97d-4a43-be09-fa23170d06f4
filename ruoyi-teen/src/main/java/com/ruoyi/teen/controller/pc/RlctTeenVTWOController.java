package com.ruoyi.teen.controller.pc;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctBoneageDTO;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.service.pc.PCRlctTeenVTWOService;
import com.ruoyi.teen.vo.pc.RlctBoneAgeRecordVo;
import com.ruoyi.teen.vo.pc.RlctTeenBoneAgeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 孩子Controller
 *
 * <AUTHOR>
 * @date 2025-5-21
 */
@Api("孩子管理")
@RestController
@RequestMapping("/teen/teenVTWO")
public class RlctTeenVTWOController extends BaseController {
    @Autowired
    private PCRlctTeenVTWOService pcRlctTeenVTWOService;


    /**
     * 查询孩子列表
     */
    @ApiOperation("查询孩子列表")
    @PreAuthorize("@ss.hasPermi('teen:teen:list')")  // 关联菜单权限控制
    @GetMapping("/listVTWO")
    public TableDataInfo list(RlctTeen rlctTeen) {
        startPage();
        List<RlctTeenBoneAgeVo> list = pcRlctTeenVTWOService.selectRlctTeenList(rlctTeen);
        return getDataTable(list);
    }


//    /**
//     * 修改孩子
//     */
//    @ApiOperation("修改孩子")
//    @PreAuthorize("@ss.hasPermi('teen:teen:edit')")
//    @Log(title = "孩子", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody RlctTeenBoneAgeVo rlctTeenBoneAgeVo) {
//        return toAjax(pcRlctTeenVTWOService.updateRlctTeen(rlctTeenBoneAgeVo));
//    }

    /**
     * 查询骨龄列表
     * */
    @ApiOperation("查询骨龄列表")
    @PreAuthorize("@ss.hasPermi('teen:teen:list')")
    @GetMapping("/boneList/{teenId}")
    public TableDataInfo list(@PathVariable("teenId") Long teenId) {
        // 启动分页
        startPage();
        List<RlctBoneageDTO> list = pcRlctTeenVTWOService.selectRlctBoneAgeRecordList(teenId);
        return getDataTable(list);
    }

    /**
     * 新增骨龄信息
     * */
    @ApiOperation("新增骨龄信息")
    @PreAuthorize("@ss.hasPermi('teen:teen:add')")
    @Log(title = "骨龄信息", businessType = BusinessType.INSERT)
    @PostMapping("/insertBoneAge")
    public AjaxResult add(@RequestBody RlctBoneAgeRecordVo rlctBoneAgeRecordVo){
        return pcRlctTeenVTWOService.addRlctTeenBoneAge(rlctBoneAgeRecordVo);
    }

    /**
    *  查询骨龄详细信息
    * */
    @ApiOperation("查询骨龄详细信息")
    @PreAuthorize("@ss.hasPermi('teen:teen:query')")
    @GetMapping("/boneAgeRecords/{boneAgeRecordId}")
    public AjaxResult getBoneAgeRecordById(@PathVariable Long boneAgeRecordId){
        return pcRlctTeenVTWOService.selectRlctBoneAgeRecordByboneAgeRecordId(boneAgeRecordId);
    }

    /**
    * 修改骨龄
    * */
    @ApiOperation("修改骨龄")
    @PreAuthorize("@ss.hasPermi('teen:teen:edit')")
    @Log(title = "骨龄信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updateBoneAge")
    public AjaxResult edit(@RequestBody RlctBoneageDTO rlctBoneageDTO){
        return pcRlctTeenVTWOService.updateRlctBoneage(rlctBoneageDTO);
    }

    /**
    * 删除骨龄
    * */
    @ApiOperation("删除骨龄")
    @PreAuthorize("@ss.hasPermi('teen:teen:remove')")
    @Log(title = "骨龄信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeBoneAge/{teenIds}")
    public AjaxResult removeBoneAge(@PathVariable Long teenIds){
        return pcRlctTeenVTWOService.deleteRlctBoneAgeRecordByboneAgeRecordId(teenIds);
    }
}
