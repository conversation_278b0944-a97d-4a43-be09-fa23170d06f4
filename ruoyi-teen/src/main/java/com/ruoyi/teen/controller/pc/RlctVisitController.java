package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.teen.vo.RlctVisitVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctVisit;
import com.ruoyi.teen.service.IRlctVisitService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 就诊记录Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("就诊记录管理")
@RestController
@RequestMapping("/teen/visit")
public class RlctVisitController extends BaseController
{
    @Autowired
    private IRlctVisitService rlctVisitService;

    /**
     * 查询就诊记录列表
     */
    @ApiOperation("查询就诊记录列表")
    @PreAuthorize("@ss.hasPermi('teen:visit:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctVisitVo rlctVisit)
    {
        startPage();
        List<RlctVisitVo> list = rlctVisitService.selectRlctVisitList(rlctVisit);
        return getDataTable(list);
    }

    /**
     * 导出就诊记录列表
     */
    @ApiOperation("导出就诊记录列表")
    @PreAuthorize("@ss.hasPermi('teen:visit:export')")
    @Log(title = "就诊记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctVisitVo rlctVisit)
    {
        List<RlctVisitVo> list = rlctVisitService.selectRlctVisitList(rlctVisit);
        ExcelUtil<RlctVisitVo> util = new ExcelUtil<RlctVisitVo>(RlctVisitVo.class);
        util.exportExcel(response, list, "就诊记录数据");
    }

    /**
     * 获取就诊记录详细信息
     */
    @ApiOperation("获取就诊记录详细信息")
    @PreAuthorize("@ss.hasPermi('teen:visit:query')")
    @GetMapping(value = "/{visitId}")
    public AjaxResult getInfo(@PathVariable("visitId") Long visitId)
    {
        return success(rlctVisitService.selectRlctVisitByVisitId(visitId));
    }

//    /**
//     * 新增就诊记录
//     */
//    @PreAuthorize("@ss.hasPermi('teen:visit:add')")
//    @Log(title = "就诊记录", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody RlctVisit rlctVisit)
//    {
//        return toAjax(rlctVisitService.insertRlctVisit(rlctVisit));
//    }

    /**
     * 修改就诊记录
     */
    @ApiOperation("修改就诊记录")
    @PreAuthorize("@ss.hasPermi('teen:visit:edit')")
    @Log(title = "就诊记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctVisit rlctVisit)
    {
        return toAjax(rlctVisitService.updateRlctVisit(rlctVisit));
    }

    /**
     * 删除就诊记录
     */
    @ApiOperation("删除就诊记录")
    @PreAuthorize("@ss.hasPermi('teen:visit:remove')")
    @Log(title = "就诊记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{visitIds}")
    public AjaxResult remove(@PathVariable Long[] visitIds)
    {
        return toAjax(rlctVisitService.deleteRlctVisitByVisitIds(visitIds));
    }
}
