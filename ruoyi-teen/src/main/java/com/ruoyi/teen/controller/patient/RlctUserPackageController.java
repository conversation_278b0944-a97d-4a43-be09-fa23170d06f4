package com.ruoyi.teen.controller.patient;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.teen.service.IRlctUserPackageService;
import com.ruoyi.teen.vo.patient.RlctUserPackageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/24
 */
@Api("用户套餐")
@RestController
@Component("patientRlctUserPackageController")
@RequestMapping("/patient/package")
public class RlctUserPackageController extends BaseController {

    @Autowired
    private IRlctUserPackageService rlctUserPackageService;

    /**
     * 获取用户的套餐使用情况
     */
    @ApiOperation("获取用户的套餐使用情况")
//    @PreAuthorize("@ss.hasPermi('patient:package:package')")
    @GetMapping(value = "/package/{tel}")
    public List<RlctUserPackageVo> getUsePackageInfo(@PathVariable("tel") String tel)
    {
        return rlctUserPackageService.selectUsePackage(tel);
    }

}
