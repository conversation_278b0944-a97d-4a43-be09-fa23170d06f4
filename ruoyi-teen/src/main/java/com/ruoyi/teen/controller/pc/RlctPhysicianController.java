package com.ruoyi.teen.controller.pc;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.service.IRlctTeenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 孩子Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@RestController
@RequestMapping("/teen/physician")
public class RlctPhysicianController extends BaseController
{
    @Autowired
    private ISysUserService sysUserService;

}
