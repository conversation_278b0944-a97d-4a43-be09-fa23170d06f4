package com.ruoyi.teen.controller.ipad;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.teen.service.IRlctUserPackageService;
import com.ruoyi.teen.vo.ipad.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 用户和套餐关系Controller
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Api("平板端用户和套餐关系")
@RestController
@Component("ipadRlctUserPackageController")
@RequestMapping("/ipad/userPackage")
public class PadRlctUserPackageController extends BaseController
{
    @Autowired
    private IRlctUserPackageService rlctUserPackageService;

    /**
     * 根据家长patientId查询可消费的套餐列表
     * @param userId
     */
    @ApiOperation("根据家长patientId查询可消费的套餐列表")
    @GetMapping("/packageList")
    public TableDataInfo list(@RequestParam Long patientId)
    {
        List<PadUserPackageVo> list = rlctUserPackageService.selectUserPackageList(patientId);
        return getDataTable(list);
    }

    /**
     * 根据⼿机号码或姓名简拼查套餐列表
     */
    @GetMapping("/querylistbyphone")
    public TableDataInfo querylistbyphone(@RequestParam(required = false) String phonenumber,
                                          @RequestParam(required = false) String nameCh,
                                          @RequestParam(required = false) String keyword
    )
    {
        // 添加这行代码启动分页
        startPage();
        List<RecordPackagesListVo> list = rlctUserPackageService.selectByPhonenumberNameCh(phonenumber,nameCh,keyword);
        return getDataTable(list);
    }

    /**
     *   套餐详情表
     * */
    @GetMapping("/packagesdetail")
    public AjaxResult packagesdetail(@RequestParam Long parent_teen_id)
    {
        try{
            PackagesDetailVo packagesdetail = rlctUserPackageService.selectpackagesdetailTeenId(parent_teen_id);
            AjaxResult success = success(packagesdetail);
            return success;
        }catch (Exception e) {
            return error("parent_teen_id错误: " + e.getMessage());
        }
    }

    /**
     *      完成套餐购买
     * */
    @PostMapping("/buyUserPackage")
    public AjaxResult buy(@RequestBody RlctUserPackageVo rlctUserPackageVo)
    {
        try{
            int i = rlctUserPackageService.insertIpadRlctUserPackage(rlctUserPackageVo);
            AjaxResult success = success(i);
            return success;
        }catch (Exception e){
            return error("购买失败: " + e.getMessage());
        }
    }

}
