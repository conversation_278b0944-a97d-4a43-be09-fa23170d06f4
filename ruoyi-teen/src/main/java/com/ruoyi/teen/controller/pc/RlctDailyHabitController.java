package com.ruoyi.teen.controller.pc;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.teen.domain.RlctDailyHabit;
import com.ruoyi.teen.service.IRlctDailyHabitService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 患者记录日常习惯Controller
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Api("患者记录日常习惯")
@RestController
@RequestMapping("/teen/daily_habit")
public class RlctDailyHabitController extends BaseController
{
    @Autowired
    private IRlctDailyHabitService rlctDailyHabitService;

    /**
     * 查询患者记录日常习惯列表
     */
    @ApiOperation("查询患者记录日常习惯列表")
    @PreAuthorize("@ss.hasPermi('teen:daily_habit:list')")
    @GetMapping("/list")
    public TableDataInfo list(RlctDailyHabit rlctDailyHabit)
    {
        startPage();
        List<RlctDailyHabit> list = rlctDailyHabitService.selectRlctDailyHabitList(rlctDailyHabit);
        return getDataTable(list);
    }

    /**
     * 导出患者记录日常习惯列表
     */
    @ApiOperation("导出患者记录日常习惯列表")
    @PreAuthorize("@ss.hasPermi('teen:daily_habit:export')")
    @Log(title = "患者记录日常习惯", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RlctDailyHabit rlctDailyHabit)
    {
        List<RlctDailyHabit> list = rlctDailyHabitService.selectRlctDailyHabitList(rlctDailyHabit);
        ExcelUtil<RlctDailyHabit> util = new ExcelUtil<RlctDailyHabit>(RlctDailyHabit.class);
        util.exportExcel(response, list, "患者记录日常习惯数据");
    }

    /**
     * 获取患者记录日常习惯详细信息
     */
    @ApiOperation("获取患者记录日常习惯详细信息")
    @PreAuthorize("@ss.hasPermi('teen:daily_habit:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rlctDailyHabitService.selectRlctDailyHabitById(id));
    }

    /**
     * 新增患者记录日常习惯
     */
    @ApiOperation("新增患者记录日常习惯")
    @PreAuthorize("@ss.hasPermi('teen:daily_habit:add')")
    @Log(title = "患者记录日常习惯", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RlctDailyHabit rlctDailyHabit)
    {
        return toAjax(rlctDailyHabitService.insertRlctDailyHabit(rlctDailyHabit));
    }

    /**
     * 修改患者记录日常习惯
     */
    @ApiOperation("修改患者记录日常习惯")
    @PreAuthorize("@ss.hasPermi('teen:daily_habit:edit')")
    @Log(title = "患者记录日常习惯", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RlctDailyHabit rlctDailyHabit)
    {
        return toAjax(rlctDailyHabitService.updateRlctDailyHabit(rlctDailyHabit));
    }

    /**
     * 删除患者记录日常习惯
     */
    @ApiOperation("删除患者记录日常习惯")
    @PreAuthorize("@ss.hasPermi('teen:daily_habit:remove')")
    @Log(title = "患者记录日常习惯", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rlctDailyHabitService.deleteRlctDailyHabitByIds(ids));
    }
}
