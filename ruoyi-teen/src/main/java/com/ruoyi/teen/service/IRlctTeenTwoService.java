package com.ruoyi.teen.service;

import java.util.List;

import com.ruoyi.teen.vo.ipad.*;

/**
 * 孩子Service接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IRlctTeenTwoService
{
    /**
     * 就诊（档案）详情表
     *
     * @param parent_teen_id 父母与孩子主键
     * @return 孩子
     */
    public RecorddDetailVo selectRlctTeenTwoByTeenId(Long parent_teen_id);


    /**
     * 带条件查询孩子列表
     *
     * @param
     * @return 孩子集合
     */
    public List<RecordListVo> selectRlctTeenTwoqueryList(String phonenumber,String nameCh,String keyword);


    /**
     * 新增孩子
     *
     * @param recordVo 孩子
     * @return 结果
     */
    public int insertRlctTeenTwo(RecordVo recordVo);



    PadTreatmentTypeSelectionVo treatmentTypeSelection(Long parentTeenId);


}
