package com.ruoyi.teen.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctParentTeenMapper;
import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.service.IRlctParentTeenService;

/**
 * 家长和孩子的关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctParentTeenServiceImpl implements IRlctParentTeenService 
{
    @Autowired
    private RlctParentTeenMapper rlctParentTeenMapper;

    /**
     * 查询家长和孩子的关系
     * 
     * @param id 家长和孩子的关系主键
     * @return 家长和孩子的关系
     */
    @Override
    public RlctParentTeen selectRlctParentTeenById(Long id)
    {
        return rlctParentTeenMapper.selectRlctParentTeenById(id);
    }

    /**
     * 查询家长和孩子的关系列表
     * 
     * @param rlctParentTeen 家长和孩子的关系
     * @return 家长和孩子的关系
     */
    @Override
    public List<RlctParentTeen> selectRlctParentTeenList(RlctParentTeen rlctParentTeen)
    {
        return rlctParentTeenMapper.selectRlctParentTeenList(rlctParentTeen);
    }

    /**
     * 新增家长和孩子的关系
     * 
     * @param rlctParentTeen 家长和孩子的关系
     * @return 结果
     */
    @Override
    public int insertRlctParentTeen(RlctParentTeen rlctParentTeen)
    {
        rlctParentTeen.setCreateTime(DateUtils.getNowDate());
        return rlctParentTeenMapper.insertRlctParentTeen(rlctParentTeen);
    }

    /**
     * 修改家长和孩子的关系
     * 
     * @param rlctParentTeen 家长和孩子的关系
     * @return 结果
     */
    @Override
    public int updateRlctParentTeen(RlctParentTeen rlctParentTeen)
    {
        return rlctParentTeenMapper.updateRlctParentTeen(rlctParentTeen);
    }

    /**
     * 批量删除家长和孩子的关系
     * 
     * @param ids 需要删除的家长和孩子的关系主键
     * @return 结果
     */
    @Override
    public int deleteRlctParentTeenByIds(Long[] ids)
    {
        return rlctParentTeenMapper.deleteRlctParentTeenByIds(ids);
    }

    /**
     * 删除家长和孩子的关系信息
     * 
     * @param id 家长和孩子的关系主键
     * @return 结果
     */
    @Override
    public int deleteRlctParentTeenById(Long id)
    {
        return rlctParentTeenMapper.deleteRlctParentTeenById(id);
    }
}
