package com.ruoyi.teen.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ruoyi.cache.utils.CacheDictUtils;
import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.domain.RlctTeenWightHeight;
import com.ruoyi.teen.mapper.RlctParentTeenMapper;
import com.ruoyi.teen.mapper.RlctTeenWightHeightMapper;
import com.ruoyi.teen.mapper.RlctUserPackageMapper;
import com.ruoyi.teen.utils.DateUtils;
import com.ruoyi.teen.utils.PinyinUtils;
import com.ruoyi.teen.vo.ipad.PadPatientDetailVo;
import com.ruoyi.teen.vo.ipad.PadPatientTeenVo;
import com.ruoyi.teen.vo.ipad.PadPatientVo;
import com.ruoyi.teen.vo.physician.RlctTeenVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctTeenMapper;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.service.IRlctTeenService;

/**
 * 孩子Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctTeenServiceImpl implements IRlctTeenService
{
    @Autowired
    private RlctTeenMapper rlctTeenMapper;

    @Autowired
    private RlctParentTeenMapper rlctParentTeenMapper;

    @Autowired
    private RlctUserPackageMapper rlctUserPackageMapper;

    @Autowired
    private RlctTeenWightHeightMapper rlctTeenWightHeightMapper;

    /**
     * 查询孩子
     *
     * @param teenId 孩子主键
     * @return 孩子
     */
    @Override
    public RlctTeen selectRlctTeenByTeenId(Long teenId)
    {
        RlctTeen teen = rlctTeenMapper.selectRlctTeenByTeenId(teenId);
        // 字典值转换
        teen.setTeenSex(CacheDictUtils.toDictLabel("sys_user_sex",teen.getTeenSex()));
        teen.setInitHeight(teen.getInitHeight() == null? BigDecimal.ZERO:teen.getInitHeight());
        teen.setInitWeight(teen.getInitWeight() == null? BigDecimal.ZERO:teen.getInitWeight());
        return teen;
    }

    /**
     * 查询孩子列表
     *
     * @param rlctTeen 孩子
     * @return 孩子
     */
    @Override
    public List<RlctTeen> selectRlctTeenList(RlctTeen rlctTeen)
    {
        return rlctTeenMapper.selectRlctTeenList(rlctTeen);
    }

    /**
     * 获取用户绑定的孩子(PC端)
     * @param userId 家长id
     * @return 结果  含父母与孩子关系id
     */
    @Override
    public List<RlctTeen> getTeenList(Long userId){
        return rlctTeenMapper.getTeenList(userId);
    }

    /**
     * 新增孩子
     *
     * @param rlctTeen 孩子
     * @return 结果
     */
    @Override
    public int insertRlctTeen(RlctTeen rlctTeen)
    {
        rlctTeen.setCreateTime(DateUtils.getNowDate());
        if(rlctTeen.getTeenBirth() != null){
            rlctTeen.setTeenAge(Long.valueOf(DateUtils.getCurrentAge(rlctTeen.getTeenBirth())));
        }
        return rlctTeenMapper.insertRlctTeen(rlctTeen);
    }

    /**
     * 修改孩子
     *
     * @param rlctTeen 孩子
     * @return 结果
     */
    @Override
    public int updateRlctTeen(RlctTeen rlctTeen)
    {
        rlctTeen.setUpdateTime(DateUtils.getNowDate());
        if(rlctTeen.getTeenBirth() != null){
            rlctTeen.setTeenAge(Long.valueOf(DateUtils.getCurrentAge(rlctTeen.getTeenBirth())));
        }
        return rlctTeenMapper.updateRlctTeen(rlctTeen);
    }

    /**
     * 批量删除孩子
     *
     * @param teenIds 需要删除的孩子主键
     * @return 结果
     */
    @Override
    public int deleteRlctTeenByTeenIds(Long[] teenIds)
    {
        return rlctTeenMapper.deleteRlctTeenByTeenIds(teenIds);
    }

    /**
     * 删除孩子信息
     *
     * @param teenId 孩子主键
     * @return 结果
     */
    @Override
    public int deleteRlctTeenByTeenId(Long teenId)
    {
        return rlctTeenMapper.deleteRlctTeenByTeenId(teenId);
    }

    /**
     * 获取用户绑定的孩子(平板端)
     * @param tel 用户的电话号码
     * @return 结果  含父母与孩子关系id
     */
    @Override
    public List<RlctTeen> getTeenListByTel(String tel) {
        return rlctTeenMapper.getTeenListByTel(tel);
    }

    /**
     * 根据手机号查询家长列表(模糊查询)
     * @param tel
     * @return
     */
    @Override
    public List<PadPatientVo> patientList(String tel) {
        if(StringUtils.isBlank(tel)){
            return new ArrayList<>();
        }
        return rlctTeenMapper.patientList(tel);
    }

    @Override
    public List<PadPatientTeenVo> patientTeenList(String teenNameCh) {
        if(StringUtils.isBlank(teenNameCh)){
            return new ArrayList<>();
        }
        List<PadPatientTeenVo> resList = rlctTeenMapper.patientTeenList(teenNameCh);
        for (PadPatientTeenVo teenVo : resList) {
            teenVo.setTeenSexDict(CacheDictUtils.toDictLabel("sys_user_sex",teenVo.getTeenSex()));

            //获取孩子的最新身高，体重
            List<RlctTeenWightHeight> whList = rlctTeenWightHeightMapper.getHeight(teenVo.getParentTeenId(),1L);
            if(whList != null && whList.size() != 0){
                RlctTeenWightHeight rtwh = whList.get(0);//最新的身高体重

                if(rtwh.getHeight() != null){
                    teenVo.setLastHeight(rtwh.getHeight());
                }
                if(rtwh.getWeight() != null){
                    teenVo.setLastWeight(rtwh.getWeight());
                }
            }
        }
        return resList;
    }

    /**
     * 根据手机号码查询详细信息（建档时间、就诊次数、未使用次数 、患者列表）
     * @param tel
     * @return
     */
    @Override
    public PadPatientDetailVo patientDetailByTel(String tel){
        PadPatientDetailVo vo = rlctTeenMapper.patientByTel(tel);
        if(vo == null){
            throw new RuntimeException("用户不存在");
        }

        //套餐使用次数
        Integer numberTimes = rlctUserPackageMapper.countNumberTimes(tel);
        vo.setVisitTimes(numberTimes);

        //统计家长的剩余套餐次数
        Integer leaveTimes = rlctUserPackageMapper.countLeaveTimes(tel);
        vo.setUnUsedTimes(leaveTimes);

        //绑定的孩子列表
        List<RlctTeen> teenList = getListByTel(tel);
        vo.setTeenList(teenList);

        return vo;
    }

    /**
     * 获取用户绑定的孩子
     * @param tel 用户的电话号码
     * @return 结果
     */
    @Override
    public List<RlctTeen> getListByTel(String tel) {
        return rlctTeenMapper.getListByTel(tel);
    }

    /**
     * 家长新增（绑定）孩子
     * @param rlctTeen 孩子实体
     * @return
     */
    @Override
    public int insertTeen(RlctTeen rlctTeen) {

        // 设置字段默认值
        rlctTeen.setCreateTime(DateUtils.getNowDate());
        rlctTeen.setDelFlag("0");
        if(rlctTeen.getTeenBirth() != null){
            rlctTeen.setTeenAge(Long.valueOf(DateUtils.getCurrentAge(rlctTeen.getTeenBirth())));
        }
        rlctTeen.setTeenNameCh(PinyinUtils.getFirstLetter(rlctTeen.getTeenName()));
        // 业务表新增
        rlctTeenMapper.insertRlctTeen(rlctTeen);

        // 关系表新增
        RlctParentTeen teen = new RlctParentTeen();
        teen.setTeenId(rlctTeen.getTeenId());
        teen.setParentId(rlctTeen.getParentId());
        rlctParentTeenMapper.insertRlctParentTeen(teen);

        return 1;
    }

    @Override
    public int initTeenNameJob() {
        List<RlctTeen> list = rlctTeenMapper.selectTeenByUnTeenNameCh();
        for (RlctTeen rlctTeen : list) {
            String teenNameCh = PinyinUtils.getFirstLetter(rlctTeen.getTeenName());
            if(StringUtils.isBlank(teenNameCh)){
                continue;
            }
            RlctTeen update = new RlctTeen();
            update.setTeenId(rlctTeen.getTeenId());
            update.setTeenNameCh(teenNameCh);
            rlctTeenMapper.updateRlctTeen(update);
        }
        return list.size();
    }

    @Override
    public RlctTeenVo selectTeen(Long teenId) {
        RlctTeenVo teen = rlctTeenMapper.selectTeen(teenId);
        // 字典值转换
        teen.setTeenSex(CacheDictUtils.toDictLabel("sys_user_sex",teen.getTeenSex()));
        return teen;
    }

    @Override
    public void updateTeenAge(){
        List<RlctTeen> list = rlctTeenMapper.selectRlctTeenList(new RlctTeen());
        for (RlctTeen rlctTeen : list) {
            updateTeenAge(rlctTeen);
        }
    }

    public void updateTeenAge(RlctTeen teen){
        Long oldAge =teen.getTeenAge() == null?0:teen.getTeenAge();
        if(teen.getTeenBirth() != null){
            long newAge = Long.valueOf(DateUtils.getCurrentAge(teen.getTeenBirth()));
            if(newAge != oldAge){
                RlctTeen upBean = new RlctTeen();
                upBean.setTeenId(teen.getTeenId());
                upBean.setTeenAge(newAge);
                rlctTeenMapper.updateRlctTeen(upBean);
            }
        }
    }
}
