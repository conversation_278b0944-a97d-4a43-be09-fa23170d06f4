package com.ruoyi.teen.service.impl.PC;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.teen.domain.RlctBoneageDTO;
import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.mapper.PCRlctTeenVTWOMapper;
import com.ruoyi.teen.mapper.RlctParentTeenMapper;
import com.ruoyi.teen.service.pc.PCRlctTeenVTWOService;
import com.ruoyi.teen.utils.DateUtils;
import com.ruoyi.teen.vo.pc.RlctBoneAgeRecordVo;
import com.ruoyi.teen.vo.pc.RlctTeenBoneAgeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PCRlctTeenVTWOImpl implements PCRlctTeenVTWOService {
    // logger
    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(PCRlctTeenVTWOImpl.class);

    @Autowired
    private PCRlctTeenVTWOMapper pcRlctTeenVTWOMapper;

    @Autowired
    private RlctParentTeenMapper rlctParentTeenMapper;
    @Override
    public List<RlctTeenBoneAgeVo> selectRlctTeenList(RlctTeen rlctTeen) {
        return pcRlctTeenVTWOMapper.selectRlctTeenList(rlctTeen);
    }

    // 废弃
//    @Override
//    public int updateRlctTeen(RlctTeenBoneAgeVo rlctTeenBoneAgeVo) {
//
//        try{
//            rlctTeenBoneAgeVo.setUpdateTime(DateUtils.getNowDate());
//            if(rlctTeenBoneAgeVo.getTeenBirth() != null){
//                rlctTeenBoneAgeVo.setTeenAge(Long.valueOf(DateUtils.getCurrentAge(rlctTeenBoneAgeVo.getTeenBirth())));
//            }
//
//            pcRlctTeenVTWOMapper.updateRlctTeen(rlctTeenBoneAgeVo);
//
////            rlctTeenBoneAgeVo
//
////            pcRlctTeenVTWOMapper.updateRlctBoneAgeRecord(rlctTeenBoneAgeVo);
//        }catch (Exception e){
//            logger.error("download error!",e);
//            throw new RuntimeException(e);
//        }
//
//
//        return 1;
//    }

    // 查询骨龄
    @Override
    public List<RlctBoneageDTO> selectRlctBoneAgeRecordList(Long teenId) {
        List<RlctBoneageDTO> list = pcRlctTeenVTWOMapper.selectRlctBoneAgeRecordList(teenId);
        return list;
    }

    // 新增骨龄
    @Override
    public AjaxResult addRlctTeenBoneAge(RlctBoneAgeRecordVo rlctBoneAgeRecordVo) {
        Long teenId = rlctBoneAgeRecordVo.getTeenId();
        Long parentId = rlctBoneAgeRecordVo.getParentId();
        // 根据孩子id搜索关系表id
        RlctParentTeen rlctParentTeen = rlctParentTeenMapper.selectByParentIdAndTeenId(parentId,teenId);
        Long parentTeenId = rlctParentTeen.getId();
        RlctBoneageDTO rlctBoneageDTO = new RlctBoneageDTO();
        rlctBoneageDTO.setBoneAge(rlctBoneAgeRecordVo.getBoneAge());
        rlctBoneageDTO.setCaptureTime(rlctBoneAgeRecordVo.getCaptureTime());
        rlctBoneageDTO.setPredictedHeight(rlctBoneAgeRecordVo.getPredictedHeight());
        rlctBoneageDTO.setBoneAgeReport(rlctBoneAgeRecordVo.getBoneAgeReport());
        rlctBoneageDTO.setParentTeenId(parentTeenId);
        rlctBoneageDTO.setTeenAge(rlctBoneAgeRecordVo.getTeenAge());
        rlctBoneageDTO.setCreateTime(DateUtils.getNowDate());
        rlctBoneageDTO.setCreateBy(String.valueOf(SecurityUtils.getUserId()));

        pcRlctTeenVTWOMapper.insertRlctBoneAgeRecord(rlctBoneageDTO);
        return null;
    }

    // 修改骨龄
    @Override
    public AjaxResult updateRlctBoneage(RlctBoneageDTO rlctBoneageDTO) {
        rlctBoneageDTO.setUpdateTime(DateUtils.getNowDate());
        rlctBoneageDTO.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
        pcRlctTeenVTWOMapper.updateRlctBoneAgeRecord(rlctBoneageDTO);
        return null;
    }


    /* 查询骨龄详情*/
    @Override
    public AjaxResult selectRlctBoneAgeRecordByboneAgeRecordId(Long boneAgeRecordId) {
        RlctBoneageDTO rlctBoneageDTO = pcRlctTeenVTWOMapper.selectRlctBoneAgeRecordByboneAgeRecordId(boneAgeRecordId);
        return AjaxResult.success(rlctBoneageDTO);
    }

    /* 删除骨龄*/
    @Override
    public AjaxResult deleteRlctBoneAgeRecordByboneAgeRecordId(Long boneAgeRecordId) {
        return AjaxResult.success(pcRlctTeenVTWOMapper.deleteRlctBoneAgeRecordByboneAgeRecordId(boneAgeRecordId));
    }

}
