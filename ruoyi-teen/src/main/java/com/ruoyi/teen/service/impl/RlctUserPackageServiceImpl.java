package com.ruoyi.teen.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ruoyi.cache.loader.CacheLoader;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.teen.domain.*;
import com.ruoyi.teen.mapper.RlctAuthorizeMapper;
import com.ruoyi.teen.mapper.RlctPackagesMapper;
import com.ruoyi.teen.mapper.RlctTeenTwoMapper;
import com.ruoyi.teen.utils.ValidateUtils;
import com.ruoyi.teen.vo.ipad.*;
import com.ruoyi.teen.vo.patient.RlctUserPackageVo;
import com.ruoyi.teen.vo.pc.RlctUserPackagePCVo;
import com.ruoyi.teen.vo.pc.RlctReturnRecordVo;
import com.ruoyi.teen.vo.pc.RlctUserPackageSaveVo;
import org.apache.poi.xdgf.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctUserPackageMapper;
import com.ruoyi.teen.service.IRlctUserPackageService;

/**
 * 用户和套餐关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctUserPackageServiceImpl implements IRlctUserPackageService {
    private static final Logger logger = LoggerFactory.getLogger(RlctUserPackageServiceImpl.class);
    @Autowired
    private RlctUserPackageMapper rlctUserPackageMapper;

    @Autowired
    private RlctAuthorizeMapper rlctAuthorizeMapper;

    @Autowired
    private RlctTeenTwoMapper rlctTeenTwoMapper;

    @Autowired
    private RlctPackagesMapper rlctPackagesMapper;
    /**
     * 查询用户和套餐关系
     *
     * @param id 用户和套餐关系主键
     * @return 用户和套餐关系
     */
    @Override
    public RlctUserPackage selectRlctUserPackageById(Long id) {
        return rlctUserPackageMapper.selectRlctUserPackageById(id);
    }

    /**
     * 查询用户和套餐关系列表
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 用户和套餐关系
     */
    @Override
    public List<RlctUserPackage> selectRlctUserPackageList(RlctUserPackage rlctUserPackage) {
        return rlctUserPackageMapper.selectRlctUserPackageList(rlctUserPackage);
    }

    /**
     * 平板端查询用户的套餐列表
     *
     * @param userId 用户的id
     * @return 用户的套餐列表
     */
    @Override
    public List<PadUserPackageVo> selectUserPackageList(Long patientId) {
        return rlctUserPackageMapper.selectUserPackageList(patientId);
    }

    @Override
    public List<UserPackageVo> selectByTel(String tel) {
        return rlctUserPackageMapper.selectByTel(tel);
    }

    /**
     * 平板端下单时更新这个用户的套餐的套餐消费中次数+1
     *
     * @param id
     * @return
     */
    @Override
    public int upIpadUserPackage(Long id) {
        return rlctUserPackageMapper.upIpadUserPackage(id);
    }


    /**
     * 查询用户和套餐关系列表（用户名和用户id关联）
     *
     * @param rlctUserPackagePCVo 用户和套餐关系
     * @return 用户和套餐关系
     */
    @Override
    public List<RlctUserPackagePCVo> selectRlctUserPackagePCVoList(RlctUserPackagePCVo rlctUserPackagePCVo) {
        return rlctUserPackageMapper.selectRlctUserPackagePCList(rlctUserPackagePCVo);
    }


    /**
     * 新增用户和套餐关系
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 结果
     */
    @Override
    public int insertRlctUserPackage(RlctUserPackageSaveVo rlctUserPackage) {

        String log = null;
        if(rlctUserPackage.getPackageGiftTimes() != null && rlctUserPackage.getPackageGiftTimes() > 0){
            //有赠送次数，需要授权码
            ValidateUtils.checkString(rlctUserPackage.getAuthorizeCode(),"授权码");
            RlctAuthorize authorize = rlctAuthorizeMapper.selectByCode(rlctUserPackage.getAuthorizeCode());
            if(authorize == null){
                throw new RuntimeException("授权码不存在");
            }
            rlctUserPackage.setPackageAuthorizationer(authorize.getUserId());
            log = "授权码："+rlctUserPackage.getAuthorizeCode()+"，授权人："+authorize.getUserId();
        }

        rlctUserPackage.setPackageReturnStatus(0L);
        rlctUserPackage.setCreateTime(DateUtils.getNowDate());
        rlctUserPackage.setPackageDuringConsumption(0L);
        rlctUserPackage.setPackageNumberTimes(0L);
        int res = rlctUserPackageMapper.insertRlctUserPackage(rlctUserPackage);
        if(log != null){
            logger.info("授权码使用记录,UserPackageId="+rlctUserPackage.getId()+","+log);
        }
        return res;
    }

    /**
     * 修改用户和套餐关系
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 结果
     */
    @Override
    public int updateRlctUserPackage(RlctUserPackage rlctUserPackage) {
        rlctUserPackage.setUpdateTime(DateUtils.getNowDate());
        return rlctUserPackageMapper.updateRlctUserPackage(rlctUserPackage);
    }

    public int userPackageReturn(RlctUserPackage rlctUserPackage) {

        RlctUserPackage rup = rlctUserPackageMapper.selectRlctUserPackageById(rlctUserPackage.getId());
        if (rup == null) {
            throw new RuntimeException("套餐不存在");
        }
        if (rup.getPackageReturnStatus() == 1) {
            throw new RuntimeException("套餐已退费，无需重复退费");
        }

        rlctUserPackage.setUpdateTime(DateUtils.getNowDate());
        rlctUserPackage.setPackageReturnStatus(1L);
        rlctUserPackage.setReturnTime(new Date());
        rlctUserPackage.setPackageReturnUser(SecurityUtils.getLoginUser().getUserId());
        rlctUserPackage.setUpdateTime(new Date());
        rlctUserPackage.setUpdateBy(SecurityUtils.getUsername());
        return rlctUserPackageMapper.userPackageReturn(rlctUserPackage);
    }

    /**
     * 批量删除用户和套餐关系
     *
     * @param ids 需要删除的用户和套餐关系主键
     * @return 结果
     */
    @Override
    public int deleteRlctUserPackageByIds(Long[] ids) {
        return rlctUserPackageMapper.deleteRlctUserPackageByIds(ids);
    }

    /**
     * 删除用户和套餐关系信息
     *
     * @param id 用户和套餐关系主键
     * @return 结果
     */
    @Override
    public int deleteRlctUserPackageById(Long id) {
        return rlctUserPackageMapper.deleteRlctUserPackageById(id);
    }

    /**
     * 查询用户套餐使用情况
     *
     * @param tel 用户手机号码
     * @return 结果
     */
    @Override
    public List<RlctUserPackageVo> selectUsePackage(String tel) {
        List<RlctUserPackageVo> rlctUserPackageVoList = rlctUserPackageMapper.selectUsePackage(tel);

        for (RlctUserPackageVo userPackageVo : rlctUserPackageVoList) {
            calculateRemainingTimes(userPackageVo);
            System.out.println("剩余次数：" + userPackageVo.getRemainingTimes());
        }

        return rlctUserPackageVoList;
    }

    private void calculateRemainingTimes(RlctUserPackageVo userPackageVo) {
        Long totalTimes = userPackageVo.getPackageTotalTimes();
        Long numberTimes = userPackageVo.getPackageNumberTimes();

        if (totalTimes != null && numberTimes != null) {
            userPackageVo.setRemainingTimes(totalTimes - numberTimes);
        } else {
            // 处理空值...
            System.out.println("totalTimes 或 numberTimes 为 null，处理逻辑...");
        }
    }


    @Override
    public List<RlctReturnRecordVo> selectReturnRecordList(RlctReturnRecordVo rlctUserPackage) {
        return rlctUserPackageMapper.selectReturnRecordList(rlctUserPackage);
    }

    /**
     * 根据⼿机号码或姓名简拼查套餐列表
     */
    @Override
    public List<RecordPackagesListVo> selectByPhonenumberNameCh(String phonenumber, String nameCh,String keyword) {
        if ((phonenumber == null && nameCh == null && keyword == null)|| (phonenumber == "" && nameCh == "" &&  keyword =="") ){
            return null;
        }else {
            return rlctUserPackageMapper.selectByPhonenumberNameCh(phonenumber,nameCh,keyword);
        }
    }


    /**
     *   套餐详情表
     * */
    @Override
    public PackagesDetailVo selectpackagesdetailTeenId(Long parent_teen_id){
        /*返回套餐详情*/
        PackagesDetailVo packagesDetailVo = new PackagesDetailVo();

        /*患者信息*/
        /*1、孩子表*/
        RlctTeenTwoVo rlctTeenTwoVo = new RlctTeenTwoVo();
        RlctTeenTwo rlctTeenTwo = rlctTeenTwoMapper.selectRlctTeenTwoByTeenId(parent_teen_id);

        rlctTeenTwoVo.setTeenName(rlctTeenTwo.getTeenName());
        rlctTeenTwoVo.setTeenAge(rlctTeenTwo.getTeenAge());
        rlctTeenTwoVo.setTeenBirth(rlctTeenTwo.getTeenBirth());
        rlctTeenTwoVo.setTeenSex(rlctTeenTwo.getTeenSex());
        rlctTeenTwoVo.setPhonenumber(rlctTeenTwo.getPhonenumber());
        rlctTeenTwoVo.setAllergyHistory(rlctTeenTwo.getAllergyHistory());
        rlctTeenTwoVo.setGeneticHeight(rlctTeenTwo.getGeneticHeight());
        rlctTeenTwoVo.setFatherHeight(rlctTeenTwo.getFatherHeight());
        rlctTeenTwoVo.setMotherHeight(rlctTeenTwo.getMotherHeight());

        /*2、套餐表*/
        RlctUserPackageVTwo rlctUserPackage = rlctTeenTwoMapper.selectRlctUserPackage(parent_teen_id);

        if (rlctUserPackage != null) {
            rlctTeenTwoVo.setRemainingTimes(rlctUserPackage.getRemainingTimes());
        }

        packagesDetailVo.setRlctTeenTwo(rlctTeenTwoVo);

        /*3.已购套餐列表*/
        List<PackageBuyTimesVo> packageBuyTimes = rlctPackagesMapper.selectPackageBuyListTimes(parent_teen_id);
        packagesDetailVo.setPackageBuyTimes(packageBuyTimes);
        /*4. 套餐列表*/
        List<RlctPackagesVo> rlctPackages = rlctPackagesMapper.selectPackageList();
        packagesDetailVo.setRlctPackages(rlctPackages);
        return  packagesDetailVo;
    }


    /*购买套餐*/
    @Override
    public int insertIpadRlctUserPackage(com.ruoyi.teen.vo.ipad.RlctUserPackageVo rlctUserPackageVo) {

        /*通过父母与孩子关系主键找到父母主键*/
        if (rlctUserPackageVo.getParentTeenId() != null) {
            logger.info("开始购买套餐,操作人={},操作时间={}",SecurityUtils.getUserId(),DateUtils.getNowDate());
            Long userId = rlctPackagesMapper.selectRlctParentTeenByParentTeenId(rlctUserPackageVo.getParentTeenId());
            if (userId ==null){
                logger.error("家长与孩子关系不存在,ParentTeenId={},操作人={}",rlctUserPackageVo.getParentTeenId(),SecurityUtils.getUserId());
                throw new RuntimeException("家长与孩子关系不存在");
            }
            List<RlctUserPackageVTwo> rlctUserPackage = new ArrayList<>();
            for (PackageBuyListVo packageBuyListVo : rlctUserPackageVo.getPackageBuyList()){
                RlctUserPackageVTwo rlctUserPackage1 = new RlctUserPackageVTwo();
                rlctUserPackage1.setPackageId(packageBuyListVo.getPackageId());
                /*根据套餐id查询套餐原价跟套餐会员价*/
                RlctPackages rlctPackages = rlctPackagesMapper.selectRlctPackagesByPackageId(packageBuyListVo.getPackageId());
                rlctUserPackage1.setPackageThenPrice(rlctPackages.getPackagePrice());
                if (rlctPackages.getPackageNumber() == null){
                    throw new RuntimeException("后台未设置该套餐次数,数量不能为空");
                }
                rlctUserPackage1.setPackagePrice(rlctPackages.getPackageMemberPrice());
                /*计算该套餐总次数*/
                rlctUserPackage1.setPackageTotalTimes(rlctPackages.getPackageNumber() * packageBuyListVo.getPackageBuyTimes());
                rlctUserPackage1.setPackageBuyTimes(packageBuyListVo.getPackageBuyTimes());
                rlctUserPackage1.setPackageGiftTimes(0L);
                rlctUserPackage1.setPackageNumberTimes(0L);
                rlctUserPackage1.setPackageDuringConsumption(0L);
                rlctUserPackage1.setUserId(userId);
                rlctUserPackage1.setPackageReturnStatus(0L);
                rlctUserPackage1.setCreateTime(DateUtils.getNowDate());
                rlctUserPackage1.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
                rlctUserPackage1.setSalesId(SecurityUtils.getUserId());
                rlctUserPackage.add(rlctUserPackage1);
            }
            int number = rlctUserPackageMapper.insertRlctUserPackageList(rlctUserPackage);
            if (number ==0){
                logger.error("购买套餐失败,购买人={},操作人={}",userId,SecurityUtils.getUserId());
                throw new RuntimeException("购买套餐失败");

            }else {
                logger.info("购买套餐成功,购买人={},操作人={}",userId,SecurityUtils.getUserId());
            }

            return number;

        }

        return 0;
    }
}
