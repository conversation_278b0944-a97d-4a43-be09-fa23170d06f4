package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctProduct;

/**
 * 产品广告Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctProductService 
{
    /**
     * 查询产品广告
     * 
     * @param productId 产品广告主键
     * @return 产品广告
     */
    public RlctProduct selectRlctProductByProductId(Long productId);

    /**
     * 查询产品广告列表
     * 
     * @param rlctProduct 产品广告
     * @return 产品广告集合
     */
    public List<RlctProduct> selectRlctProductList(RlctProduct rlctProduct);

    /**
     * 新增产品广告
     * 
     * @param rlctProduct 产品广告
     * @return 结果
     */
    public int insertRlctProduct(RlctProduct rlctProduct);

    /**
     * 修改产品广告
     * 
     * @param rlctProduct 产品广告
     * @return 结果
     */
    public int updateRlctProduct(RlctProduct rlctProduct);

    /**
     * 批量删除产品广告
     * 
     * @param productIds 需要删除的产品广告主键集合
     * @return 结果
     */
    public int deleteRlctProductByProductIds(Long[] productIds);

    /**
     * 删除产品广告信息
     * 
     * @param productId 产品广告主键
     * @return 结果
     */
    public int deleteRlctProductByProductId(Long productId);
}
