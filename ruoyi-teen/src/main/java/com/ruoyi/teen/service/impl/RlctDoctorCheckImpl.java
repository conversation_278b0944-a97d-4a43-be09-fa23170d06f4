package com.ruoyi.teen.service.impl;


import com.ruoyi.cache.bean.DictData;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.sms.until.QuarzUntil;
import com.ruoyi.teen.domain.*;
import com.ruoyi.teen.mapper.RlctDoctorVisicMapper;
import com.ruoyi.teen.mapper.RlctUserPackageMapper;
import com.ruoyi.teen.mapper.RlctVisitMapper;
import com.ruoyi.teen.mapper.RlctVisitTwoMapper;
import com.ruoyi.teen.service.IRlctDoctorCheckService;
import com.ruoyi.teen.service.IRlctVisitService;
import com.ruoyi.teen.vo.ipad.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class RlctDoctorCheckImpl implements IRlctDoctorCheckService {
    private static final Logger logger = LoggerFactory.getLogger(RlctDoctorCheckImpl.class);


    @Autowired
    private RlctDoctorVisicMapper rlctdoctorVisicMapper;

    @Autowired
    private RlctUserPackageMapper rlctUserPackageMapper;

    @Autowired
    private RlctVisitTwoMapper rlctVisitTwoMapper;
    @Autowired
    private RlctVisitMapper rlctVisitMapper;

    @Autowired
    private IRlctVisitService irlctVisitService;

    /*巡诊审核列表 0待审核 1审核通过*/
    @Override
    public List<RlctDoctorVisicVo> doctornochecklist(Integer auditStatus) {

        // 1. 参数验证
        if (auditStatus != 0 && auditStatus != 1) {
            throw new RuntimeException("审核状态参数错误，只能是0或1");
        }

        // 2. 分页查询基础信息
        List<RlctDoctorVisicVo> result = rlctdoctorVisicMapper.selectRlctDoctorVisicNoCheckList(auditStatus);

        if (result.isEmpty()) {
            logger.warn("没有巡诊记录, auditStatus={}", auditStatus);
            return result;
        }

        // 3. 收集所有visitId和doctorId
        List<Long> visitIds = new ArrayList<>();
        List<Long> doctorIds = new ArrayList<>();

        for (RlctDoctorVisicVo item : result) {
            visitIds.add(item.getVisitId());
            doctorIds.add(item.getDoctorId());
        }

        // 4. 一次性查询所有治疗项目
        List<RlcttreatmentTypeListDTO> allTreatments =
                rlctdoctorVisicMapper.selectAllTreatmentsForVisits(visitIds, doctorIds);

        // 5.
        for (RlctDoctorVisicVo item : result) {
            List<PadtreatmentTypeListVo> treatmentList = new ArrayList<>();

            // 使用简单循环匹配治疗项目
            for (RlcttreatmentTypeListDTO treatment : allTreatments) {
                if (treatment.getVisitId().equals(item.getVisitId()) &&
                        treatment.getDoctorId().equals(item.getDoctorId())) {

                    PadtreatmentTypeListVo vo = new PadtreatmentTypeListVo();
                    vo.setDoctorVisicId(treatment.getDoctorVisicId());
                    vo.setTreatmentType(treatment.getTreatmentType());
                    vo.setTreatmentTypeName(
                            DictUtils.getDictLabel("record_treatment_type",
                                    String.valueOf(treatment.getTreatmentType()), "未知"));
                    vo.setReviewTime(treatment.getReviewTime());
                    treatmentList.add(vo);
                }
            }

            item.setTreatmentTypeList(treatmentList);
        }

        return result;
//        // 1. 验证状态参数
//        if (auditStatus!= 0 && auditStatus!= 1)  {
//            throw new RuntimeException("审核状态参数错误，只能是0或1");
//        }
//        // 获取审核人id
//        Long reviewId = SecurityUtils.getUserId();
//
//        // 审核不成功他不需要查询出审核人id
//        if (auditStatus == 0){
//            reviewId = null ;
//        }
//
//        // 得到基本信息和医生id、就诊id
//        List<RlctDoctorVisicVo>  result = rlctdoctorVisicMapper.selectRlctDoctorVisicNoCheckList(auditStatus);
//
//        if (result.size()==0){
//            logger.warn("没有巡诊记录,doctornochecklist,reviewId={}",reviewId);
//            return  null;
//        }
//
//        //根据列表的就诊id和医生id再去查询出对应的治疗项目类型
//        for (RlctDoctorVisicVo data : result) {
//            RlctDoctorVisicVo rlctdoctorVisicVo = new RlctDoctorVisicVo();
//            Long visitId = data.getVisitId();
//            Long doctorId = data.getDoctorId();
//            // 根据就诊id、医生id、审核状态、审核人id查询出对应的治疗项目类型
//            List<RlcttreatmentTypeListDTO> rlcttreatmentTypeListDTO = rlctdoctorVisicMapper.selectRlctDoctorVisicTreatmentType(visitId,doctorId); // 获取治疗项目类型
//            //System.out.println("治疗名称："+DictUtils.getDictLabel("record_treatment_type",String.valueOf(2), "未知"));
//
//            //将得到的治疗项目类型for循环在传入到PadtreatmentTypeListVo将类型数字转成名称如：0是关节松解
//            List<PadtreatmentTypeListVo> padConfirmCheckVoList = new ArrayList<>();
//            for (RlcttreatmentTypeListDTO typeData  : rlcttreatmentTypeListDTO) {
//                PadtreatmentTypeListVo typeVo = new PadtreatmentTypeListVo();
//                typeVo.setDoctorVisicId(typeData.getDoctorVisicId());
//                typeVo.setTreatmentType(typeData.getTreatmentType());
//                typeVo.setTreatmentTypeName(DictUtils.getDictLabel("record_treatment_type",String.valueOf(typeData.getTreatmentType()), "未知"));
//                typeVo.setReviewTime(typeData.getReviewTime());
//                padConfirmCheckVoList.add(typeVo);
//            }
//
//            //将DTO转VO
//            rlctdoctorVisicVo.setNickName(data.getNickName());
//            rlctdoctorVisicVo.setTeenName(data.getTeenName());
//            rlctdoctorVisicVo.setTreatmentTime(data.getTreatmentTime());
//            rlctdoctorVisicVo.setUserPackageId(data.getUserPackageId());
//            rlctdoctorVisicVo.setTreatmentTypeList(padConfirmCheckVoList);
//            result.add(rlctdoctorVisicVo);
//        }
//
//        return result;
    }

    /*修改治疗项目类型*/
    @Override
    public AjaxResult confirmcheck(PadConfirmCheckVo padConfirmCheckVo) {

        Long userId = SecurityUtils.getUserId();
        Long doctorId = padConfirmCheckVo.getDoctorId();
        Long visitId = padConfirmCheckVo.getVisitId();
        if (doctorId == null || visitId == null) {
            logger.error("doctorId={},visitId={}不能为空,操作人={}", doctorId, visitId, userId);
            return AjaxResult.error(doctorId == null ? "医生id不能为空" : "就诊id不能为空");
        }
        logger.info("开始处理治疗项目调整, doctorId={}, visitId={}, userId={}", doctorId, visitId, userId);
        // 处理提交的治疗项目
        try {
            List<TreatmentVo> treatments = padConfirmCheckVo.getTreatments();
            updateTherapySubmission(treatments, visitId, doctorId);//处理提交治疗工作的方法
            logger.info("治疗项目调整成功, doctorId={}, visitId={}", doctorId, visitId);
        } catch (Exception e) {
            logger.error("治疗项目调整失败, doctorId={}, visitId={}", doctorId, visitId, e);
            throw e;
        }

        return AjaxResult.success("调整项目成功");
    }

    /*
     *
     * 处理提交治疗工作【专用与调整项目】
     * @param treatments 治疗项目列表（不能为空）
     * @param visitId 就诊ID
     * @param doctorId 医生ID
     * @throws IllegalArgumentException 当treatments为null时抛出
     *
     */
    public void updateTherapySubmission(List<TreatmentVo> treatments, Long visitId, Long doctorId) {
        // 1. 参数校验
        if (treatments == null) {
            throw new IllegalArgumentException("治疗项目列表不能为空");
        }
        if (visitId == null || doctorId == null) {
            throw new IllegalArgumentException("就诊ID和医生ID不能为空");
        }

        Long currentUserId = SecurityUtils.getUserId();  // 更改者
        Date now = DateUtils.getNowDate();

        // 2. 获取数据库中现有的治疗记录 （当前医师）
        List<RlctDoctorVisic> dbRecords = rlctVisitMapper.selectByVisitAndDoctor(visitId, doctorId);


        if (CollectionUtils.isEmpty(dbRecords)) {
            logger.info("就诊ID {} 下没有找到医生的治疗记录", visitId);
            return;
        }
        Date treatmentTime = dbRecords.get(0).getTreatmentTime(); // 治疗时间

        // 4. 准备需要新增和删除的记录
        List<RlctDoctorVisic> recordsToAdd = new ArrayList<>();
        List<Long> idsToDelete = new ArrayList<>(); // 伪删除记录列表


        // 5. 检查数据库中的记录是否在前端提供的数据中
        for (RlctDoctorVisic dbRecord : dbRecords) {
            boolean foundInFrontend = false;

            // 检查记录是否在前端提供的ID列表或类型列表中
            for (TreatmentVo treatment : treatments) {
                if (dbRecord.getTreatmentType().equals(treatment.getDictTreatmentType())) {
                    foundInFrontend = true;
                    break; // 过滤已存在的记录
                }
            }

            if (!foundInFrontend) {
                idsToDelete.add(dbRecord.getDoctorVisicId());
            }
        }

        // 6. 检查前端提供的类型是否在数据库中  // 想要自动把其他的也删除也可以在这里add进删除列表
        for (TreatmentVo treatment : treatments) {
            if (treatment.getDictTreatmentType() == null) {
                continue;
            }

            boolean existsInDb = false;
            for (RlctDoctorVisic dbRecord : dbRecords) {
                if (dbRecord.getTreatmentType().equals(treatment.getDictTreatmentType())) {
                    existsInDb = true;
                    break;// 过滤已存在的记录
                }
            }

            if (!existsInDb) {
                RlctDoctorVisic newRecord = new RlctDoctorVisic();
                newRecord.setDoctorId(doctorId);
                newRecord.setTreatmentType(treatment.getDictTreatmentType());
                newRecord.setTreatmentTime(treatmentTime);
                newRecord.setReviewStatus("0");
                newRecord.setDelFlag("0");
                newRecord.setCreateBy((SecurityUtils.getUserId()).toString());
                newRecord.setCreateTime(now);
                newRecord.setVisitId(visitId);
                recordsToAdd.add(newRecord);
            }
        }



        // 7. 执行批量操作
        if (!idsToDelete.isEmpty()) {
            int deleted = rlctVisitMapper.batchSoftDelete(idsToDelete, currentUserId, now);
            logger.info("已伪删除{}条治疗记录", deleted);
        }

        if (!recordsToAdd.isEmpty()) {
            int inserted = rlctVisitMapper.insertRlctDoctorVisic(recordsToAdd);
            logger.info("已新增{}条治疗记录", inserted);
        }

    }

    /**
     * 审核通过 或 撤销审核按钮 审核状态值必须为0(撤销)或1(通过)
     */
    @Override
    public AjaxResult checkstatus(RlctDoctorVisicDVO rlctDoctorVisicDvo) {
        // 1. 参数校验
        if (rlctDoctorVisicDvo.getAuditStatus() == null || (rlctDoctorVisicDvo.getAuditStatus() != 1L && rlctDoctorVisicDvo.getAuditStatus() != 0L)) {
            throw new IllegalArgumentException("请输入正确的审核状态值");
        }
        if (rlctDoctorVisicDvo.getUserPackageId() == null) {
            throw new IllegalArgumentException("用户套餐ID不能为空");
        }
        if (rlctDoctorVisicDvo.getVisitId() == null) {
            throw new IllegalArgumentException("就诊ID不能为空");
        }
        if (rlctDoctorVisicDvo.getDoctorId() == null) {
            throw new IllegalArgumentException("医生ID不能为空");
        }
        logger.info("开始处理审核操作, auditStatus={}, userPackageId={}, visitId={}", rlctDoctorVisicDvo.getAuditStatus(), rlctDoctorVisicDvo.getUserPackageId(), rlctDoctorVisicDvo.getVisitId());

        try {
            Long currentUserId = SecurityUtils.getUserId();
            Date reviewTime = DateUtils.getNowDate();
            Date cancelReviewTime = DateUtils.getNowDate();

            Long visitId = rlctDoctorVisicDvo.getVisitId();
            Long doctorId = rlctDoctorVisicDvo.getDoctorId();
            Long userPackageId = rlctDoctorVisicDvo.getUserPackageId();
            String auditStatus = rlctDoctorVisicDvo.getAuditStatus().toString();
            // 2. 获取数据库中现有的治疗记录
            List<RlctDoctorVisic> dbRecords = rlctVisitMapper.selectByVisitAndDoctor(visitId, doctorId);
            if (dbRecords == null || dbRecords.isEmpty()) {
                throw new IllegalArgumentException("该医生没有待审核的治疗记录");
            }
            logger.info("待审核治疗工作记录数量: {}", dbRecords.size());
            //  先建立一个列表根据id批量审核通过
            List<Long> ids = new ArrayList<>();
            for (RlctDoctorVisic dbRecord : dbRecords) {
                ids.add(dbRecord.getDoctorVisicId());
            }
            if (rlctDoctorVisicDvo.getAuditStatus() == 1L) {
                cancelReviewTime = null;
            } else {
                reviewTime = null;
            }
            int updated = rlctdoctorVisicMapper.batchUpdateRlctDoctorVisicStatus(ids, auditStatus, currentUserId, reviewTime, cancelReviewTime);  // 主键 审核状态 审核人 审核时间
            logger.info("更新{}条治疗记录状态，医生ID：{}", updated, doctorId);
            // 4. 如果是审核通过，检查是否是最后一个医生
            if (rlctDoctorVisicDvo.getAuditStatus() == 1L) {
                // 获取该就诊单下所有未审核完成的医生数量
                int pendingDoctors = rlctVisitMapper.countPendingDoctorsByVisit(visitId);
                logger.info("待审核医生数量: {}", pendingDoctors);
                if (pendingDoctors == 0) {
                    logger.info("所有医生已完成审核，更新就诊状态");
                    // 扣减套餐次数
                    rlctUserPackageMapper.finishUserPackage(userPackageId);
                    // 更新就诊状态为已完成
                    RlctVisitVTwoDTO visitDto = new RlctVisitVTwoDTO();
                    visitDto.setVisitId(visitId);
                    visitDto.setStatus(1L); // 1=已完成
                    visitDto.setHealTime(reviewTime);
                    visitDto.setUpdateBy(currentUserId.toString());
                    visitDto.setUpdateTime(reviewTime);
                    rlctVisitMapper.updateRlctVisit(visitDto);
                }
            }
            return AjaxResult.success(rlctDoctorVisicDvo.getAuditStatus() == 1L ?
                    "审核通过成功" : "撤销审核成功");

        } catch (Exception e) {
            logger.error("审核操作失败", e);
            throw new RuntimeException("审核操作失败: " + e.getMessage(), e);
        }

    }


    /*伪删除医生治疗工作*/
    @Override
    public AjaxResult delete(RlctDoctorVisicVo rlctDoctorVisicVo) {
        if (rlctDoctorVisicVo == null || rlctDoctorVisicVo.getDoctorVisicId() == null) {
            return AjaxResult.error("参数错误：记录ID不能为空");
        }

        Long doctorVisicId = rlctDoctorVisicVo.getDoctorVisicId();
        Long currentUserId = SecurityUtils.getUserId();
        Date nowDate = DateUtils.getNowDate();

        try {
            int affectedRows = rlctdoctorVisicMapper.updateRlctDoctorVisic(
                    doctorVisicId, currentUserId, nowDate);

            if (affectedRows > 0) {
                logger.info("成功伪删除治疗记录ID：{}", doctorVisicId);
                return AjaxResult.success("删除成功");
            } else {
                logger.warn("未找到要删除的治疗记录ID：{}", doctorVisicId);
                return AjaxResult.error("记录不存在或已被删除");
            }
        } catch (Exception e) {
            logger.error("删除治疗记录失败，ID：{}", doctorVisicId, e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }
}
