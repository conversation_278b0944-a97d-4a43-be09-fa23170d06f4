package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctVisitHistory;

/**
 * 就诊历史记录Service接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctVisitHistoryService
{
    /**
     * 查询就诊历史记录
     *
     * @param id 就诊历史记录主键
     * @return 就诊历史记录
     */
    public RlctVisitHistory selectRlctVisitHistoryById(Long id);

    /**
     * 查询就诊历史记录列表
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 就诊历史记录集合
     */
    public List<RlctVisitHistory> selectRlctVisitHistoryList(RlctVisitHistory rlctVisitHistory);

    /**
     * 新增就诊历史记录
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 结果
     */
    public int insertRlctVisitHistory(RlctVisitHistory rlctVisitHistory);


    /**
     * 完成治疗时插入就诊历史表
     * @param visitId
     * @return
     */
//    public int finishVist(Long visitId);

    /**
     * 修改就诊历史记录
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 结果
     */
    public int updateRlctVisitHistory(RlctVisitHistory rlctVisitHistory);

    /**
     * 批量删除就诊历史记录
     *
     * @param ids 需要删除的就诊历史记录主键集合
     * @return 结果
     */
    public int deleteRlctVisitHistoryByIds(Long[] ids);

    /**
     * 删除就诊历史记录信息
     *
     * @param id 就诊历史记录主键
     * @return 结果
     */
    public int deleteRlctVisitHistoryById(Long id);
}
