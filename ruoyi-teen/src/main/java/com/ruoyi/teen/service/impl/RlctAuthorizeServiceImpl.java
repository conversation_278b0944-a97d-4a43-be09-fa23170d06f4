package com.ruoyi.teen.service.impl;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.teen.domain.RlctAuthorize;
import com.ruoyi.teen.domain.RlctDailyHabit;
import com.ruoyi.teen.mapper.RlctAuthorizeMapper;
import com.ruoyi.teen.mapper.RlctDailyHabitMapper;
import com.ruoyi.teen.service.IRlctAuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 授权Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
@Service
public class RlctAuthorizeServiceImpl extends ServiceImpl<RlctAuthorizeMapper, RlctAuthorize> implements IRlctAuthorizeService
{
    @Autowired
    private RlctAuthorizeMapper rlctAuthorizeMapper;

    /**
     * 查询授权
     *
     * @param id 授权主键
     * @return 授权
     */
    @Override
    public RlctAuthorize selectRlctAuthorizeById(Long id)
    {
        return rlctAuthorizeMapper.selectRlctAuthorizeById(id);
    }

    /**
     * 查询授权列表
     *
     * @param rlctAuthorize 授权
     * @return 授权
     */
    @Override
    public List<RlctAuthorize> selectRlctAuthorizeList(RlctAuthorize rlctAuthorize)
    {
        return rlctAuthorizeMapper.selectRlctAuthorizeList(rlctAuthorize);
    }

    /**
     * 新增授权
     *
     * @param rlctAuthorize 授权
     * @return 结果
     */
    @Override
    public int insertRlctAuthorize(RlctAuthorize rlctAuthorize)
    {
        rlctAuthorize.setCreateTime(DateUtils.getNowDate());
        return rlctAuthorizeMapper.insertRlctAuthorize(rlctAuthorize);
    }

    /**
     * 修改授权
     *
     * @param rlctAuthorize 授权
     * @return 结果
     */
    @Override
    public int updateRlctAuthorize(RlctAuthorize rlctAuthorize)
    {
        rlctAuthorize.setUpdateTime(DateUtils.getNowDate());
        return rlctAuthorizeMapper.updateRlctAuthorize(rlctAuthorize);
    }


    /**
     * 批量删除授权
     *
     * @param ids 需要删除的授权主键
     * @return 结果
     */
    @Override
    public int deleteRlctAuthorizeByIds(Long[] ids)
    {
        return rlctAuthorizeMapper.deleteRlctAuthorizeByIds(ids);
    }

    /**
     * 删除授权信息
     *
     * @param id 授权主键
     * @return 结果
     */
    @Override
    public int deleteRlctAuthorizeById(Long id)
    {
        return rlctAuthorizeMapper.deleteRlctAuthorizeById(id);
    }

    @Override
    public RlctAuthorize getAuthorize()
    {
        Long userId = SecurityUtils.getUserId();
        RlctAuthorize authorize = rlctAuthorizeMapper.selectByUserId(userId);
        if(authorize == null){
            authorize = new RlctAuthorize();
        }
        return authorize;
    }

    @Override
    public int saveAuthorize(RlctAuthorize rlctAuthorize)
    {
        Long userId = SecurityUtils.getUserId();

        RlctAuthorize res = rlctAuthorizeMapper.selectByCode(rlctAuthorize.getAuthorizeCode());
        if(res != null && res.getUserId()!= userId){
            throw new RuntimeException("授权码以存在！请更换授权码");
        }

        RlctAuthorize authorize = rlctAuthorizeMapper.selectByUserId(userId);
        rlctAuthorize.setUpdater(SecurityUtils.getUsername());
        rlctAuthorize.setUpdateTime(new Date());
        rlctAuthorize.setUserId(userId);
        if(authorize == null){
            rlctAuthorize.setCreater(SecurityUtils.getUsername());
            rlctAuthorize.setCreateTime(new Date());
            return rlctAuthorizeMapper.insert(rlctAuthorize);
        }else{
            return rlctAuthorizeMapper.updateByUserId(rlctAuthorize);
        }
    }
}
