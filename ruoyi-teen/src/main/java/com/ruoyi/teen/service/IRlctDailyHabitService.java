package com.ruoyi.teen.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.teen.domain.RlctDailyHabit;
import com.ruoyi.teen.domain.config.Diagnose;

/**
 * 患者记录日常习惯Service接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface IRlctDailyHabitService extends IService<RlctDailyHabit>
{
    /**
     * 查询患者记录日常习惯
     *
     * @param id 患者记录日常习惯主键
     * @return 患者记录日常习惯
     */
    public RlctDailyHabit selectRlctDailyHabitById(Long id);

    /**
     * 查询患者记录日常习惯列表
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 患者记录日常习惯集合
     */
    public List<RlctDailyHabit> selectRlctDailyHabitList(RlctDailyHabit rlctDailyHabit);

    /**
     * 新增患者记录日常习惯
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 结果
     */
    public int insertRlctDailyHabit(RlctDailyHabit rlctDailyHabit);

    /**
     * 平板端打印记录表
     * @param getIds
     * @return
     */
    boolean insertHabit(Diagnose diagnose, Long parentTeenId);

    /**
     * 修改患者记录日常习惯
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 结果
     */
    public int updateRlctDailyHabit(RlctDailyHabit rlctDailyHabit);

    /**
     * 批量删除患者记录日常习惯
     *
     * @param ids 需要删除的患者记录日常习惯主键集合
     * @return 结果
     */
    public int deleteRlctDailyHabitByIds(Long[] ids);

    /**
     * 删除患者记录日常习惯信息
     *
     * @param id 患者记录日常习惯主键
     * @return 结果
     */
    public int deleteRlctDailyHabitById(Long id);
}
