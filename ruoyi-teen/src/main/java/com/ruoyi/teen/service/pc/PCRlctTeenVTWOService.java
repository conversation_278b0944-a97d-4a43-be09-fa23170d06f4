package com.ruoyi.teen.service.pc;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.teen.domain.RlctBoneageDTO;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.vo.pc.RlctBoneAgeRecordVo;
import com.ruoyi.teen.vo.pc.RlctTeenBoneAgeVo;

import java.util.List;

public interface PCRlctTeenVTWOService {

    /**
     * 查询孩子列表
     *
     * @param
     * @return 孩子集合
     */
    List<RlctTeenBoneAgeVo> selectRlctTeenList(RlctTeen rlctTeen);

//    /**
//    * 修改孩子
//    * */
//    int updateRlctTeen(RlctTeenBoneAgeVo rlctTeen);

    /*查询骨龄列表*/
    List<RlctBoneageDTO> selectRlctBoneAgeRecordList(Long teenId);

    /*新增骨龄信息*/
    AjaxResult addRlctTeenBoneAge(RlctBoneAgeRecordVo rlctBoneAgeRecordVo);

    /*修改骨龄信息*/
    AjaxResult updateRlctBoneage(RlctBoneageDTO rlctBoneageDTO);

    /*查询骨龄详情*/
    AjaxResult selectRlctBoneAgeRecordByboneAgeRecordId(Long boneAgeRecordId);

    /*删除骨龄*/
    AjaxResult deleteRlctBoneAgeRecordByboneAgeRecordId(Long boneAgeRecordId);
}
