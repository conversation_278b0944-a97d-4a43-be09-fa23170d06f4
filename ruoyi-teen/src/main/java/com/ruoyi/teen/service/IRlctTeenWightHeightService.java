package com.ruoyi.teen.service;

import java.util.List;

import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.domain.RlctTeenWightHeight;

/**
 * 身高体重Service接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctTeenWightHeightService
{
    /**
     * 查询身高体重
     *
     * @param id 身高体重主键
     * @return 身高体重
     */
    public RlctTeenWightHeight selectRlctTeenWightHeightById(Long id);

    /**
     * 查询身高体重列表
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 身高体重集合
     */
    public List<RlctTeenWightHeight> selectRlctTeenWightHeightList(RlctTeenWightHeight rlctTeenWightHeight);

    /**
     * 新增身高体重
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 结果
     */
    public int insertRlctTeenWightHeight(RlctTeenWightHeight rlctTeenWightHeight);

    /**
     * 修改身高体重
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 结果
     */
    public int updateRlctTeenWightHeight(RlctTeenWightHeight rlctTeenWightHeight);

    /**
     * 批量删除身高体重
     *
     * @param ids 需要删除的身高体重主键集合
     * @return 结果
     */
    public int deleteRlctTeenWightHeightByIds(Long[] ids);

    /**
     * 删除身高体重信息
     *
     * @param id 身高体重主键
     * @return 结果
     */
    public int deleteRlctTeenWightHeightById(Long id);

    /**
     * 获取孩子的身高体重
     * @param parentTeen 父母孩子关系实体
     * @return
     */
    List<RlctTeenWightHeight> getBodyInfo(RlctParentTeen parentTeen);

    /**
     * 获取孩子的身高体重(平板端)
     * @param parentTeen 父母孩子关系实体
     * @return
     */
//    List<RlctTeenWightHeight> BodyInfo(RlctParentTeen parentTeen);

    /**
     * 获取孩子的前n次身高,倒序
     * @param id
     * @param n
     * @return
     */
    List<RlctTeenWightHeight> getHeight(Long parentTeenId,Long n);
}
