package com.ruoyi.teen.service;

import com.ruoyi.teen.domain.RlctAuthorize;
import java.util.List;
/**
 * 授权Service接口
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface IRlctAuthorizeService
{
    /**
     * 查询授权
     *
     * @param id 授权主键
     * @return 授权
     */
    public RlctAuthorize selectRlctAuthorizeById(Long id);
    public RlctAuthorize getAuthorize();

    /**
     * 查询授权列表
     *
     * @param rlctAuthorize 授权
     * @return 授权集合
     */
    public List<RlctAuthorize> selectRlctAuthorizeList(RlctAuthorize rlctAuthorize);

    /**
     * 新增授权
     *
     * @param rlctAuthorize 授权
     * @return 结果
     */
    public int insertRlctAuthorize(RlctAuthorize rlctAuthorize);

    /**
     * 修改授权
     *
     * @param rlctAuthorize 授权
     * @return 结果
     */
    public int updateRlctAuthorize(RlctAuthorize rlctAuthorize);
    public int saveAuthorize(RlctAuthorize rlctAuthorize);

    /**
     * 批量删除授权
     *
     * @param ids 需要删除的授权主键集合
     * @return 结果
     */
    public int deleteRlctAuthorizeByIds(Long[] ids);

    /**
     * 删除授权信息
     *
     * @param id 授权主键
     * @return 结果
     */
    public int deleteRlctAuthorizeById(Long id);
}
