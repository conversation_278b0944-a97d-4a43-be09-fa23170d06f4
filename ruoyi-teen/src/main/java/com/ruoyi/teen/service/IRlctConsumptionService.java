package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctConsumption;
import com.ruoyi.teen.vo.pc.RlctConsumptionVo;

/**
 * 消费记录Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctConsumptionService 
{
    /**
     * 查询消费记录
     * 
     * @param id 消费记录主键
     * @return 消费记录
     */
    public RlctConsumption selectRlctConsumptionById(Long id);

    /**
     * 查询消费记录列表
     * 
     * @param rlctConsumption 消费记录
     * @return 消费记录集合
     */
    public List<RlctConsumption> selectRlctConsumptionList(RlctConsumption rlctConsumption);

    /**
     * 根据id查询消费记录列表所有名字
     * @param rlctConsumptionVo 消费记录名字
     * @return 消费记录列表名字集合
     */
    public List<RlctConsumptionVo> selectRlctConsumptionVoList(RlctConsumptionVo rlctConsumptionVo);

    /**
     * 新增消费记录
     * 
     * @param rlctConsumption 消费记录
     * @return 结果
     */
    public int insertRlctConsumption(RlctConsumption rlctConsumption);

    /**
     * 修改消费记录
     * 
     * @param rlctConsumption 消费记录
     * @return 结果
     */
    public int updateRlctConsumption(RlctConsumption rlctConsumption);

    /**
     * 批量删除消费记录
     * 
     * @param ids 需要删除的消费记录主键集合
     * @return 结果
     */
    public int deleteRlctConsumptionByIds(Long[] ids);

    /**
     * 删除消费记录信息
     * 
     * @param id 消费记录主键
     * @return 结果
     */
    public int deleteRlctConsumptionById(Long id);
}
