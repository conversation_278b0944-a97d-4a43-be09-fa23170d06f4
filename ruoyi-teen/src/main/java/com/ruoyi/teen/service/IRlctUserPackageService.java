package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctUserPackage;
import com.ruoyi.teen.vo.ipad.PackagesDetailVo;
import com.ruoyi.teen.vo.ipad.PadUserPackageVo;
import com.ruoyi.teen.vo.ipad.RecordPackagesListVo;
import com.ruoyi.teen.vo.ipad.UserPackageVo;
import com.ruoyi.teen.vo.patient.RlctUserPackageVo;
import com.ruoyi.teen.vo.pc.RlctUserPackagePCVo;
import com.ruoyi.teen.vo.pc.RlctReturnRecordVo;
import com.ruoyi.teen.vo.pc.RlctUserPackageSaveVo;

/**
 * 用户和套餐关系Service接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctUserPackageService
{
    /**
     * 查询用户和套餐关系
     *
     * @param id 用户和套餐关系主键
     * @return 用户和套餐关系
     */
    public RlctUserPackage selectRlctUserPackageById(Long id);

    /**
     * 查询用户和套餐关系列表
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 用户和套餐关系集合
     */
    public List<RlctUserPackage> selectRlctUserPackageList(RlctUserPackage rlctUserPackage);

    /**
     * 平板端查询用户的套餐列表
     *
     * @param userId 用户的id
     * @return 用户的套餐列表
     */
    public List<PadUserPackageVo> selectUserPackageList(Long patientId);
    public List<UserPackageVo> selectByTel(String tel);

    /**
     * 平板端下单时更新这个用户的套餐的套餐消费中次数+1
     *
     * @param id
     * @return
     */
    public int upIpadUserPackage(Long id);


    /**
     * 查询用户和套餐关系列表(带用户id关联用户名)
     *
     * @param rlctUserPackagePCVo 用户和套餐关系
     * @return 用户和套餐关系集合
     */
    public List<RlctUserPackagePCVo> selectRlctUserPackagePCVoList(RlctUserPackagePCVo rlctUserPackagePCVo);


    /**
     * 新增用户和套餐关系
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 结果
     */
    public int insertRlctUserPackage(RlctUserPackageSaveVo rlctUserPackage);

    /**
     * 修改用户和套餐关系
     *
     * @param rlctUserPackage 用户和套餐关系
     * @return 结果
     */
    public int updateRlctUserPackage(RlctUserPackage rlctUserPackage);
    public int userPackageReturn(RlctUserPackage rlctUserPackage);

    /**
     * 批量删除用户和套餐关系
     *
     * @param ids 需要删除的用户和套餐关系主键集合
     * @return 结果
     */
    public int deleteRlctUserPackageByIds(Long[] ids);

    /**
     * 删除用户和套餐关系信息
     *
     * @param id 用户和套餐关系主键
     * @return 结果
     */
    public int deleteRlctUserPackageById(Long id);

    /**
     * 查询用户套餐的使用情况
     * @param tel 用户手机号码
     * @return 结果
     */
    List<RlctUserPackageVo> selectUsePackage(String tel);

    /**
     * 查询套餐退费列表
     * @return 结果
     */
    List<RlctReturnRecordVo> selectReturnRecordList(RlctReturnRecordVo rlctUserPackage);



    /**
     *   套餐详情表
     * */
    PackagesDetailVo selectpackagesdetailTeenId(Long parentTeenId);

    /*
    * 完成套餐购买
    * */
    int insertIpadRlctUserPackage(com.ruoyi.teen.vo.ipad.RlctUserPackageVo rlctUserPackageVo);

    /*套餐列表*/
    List<RecordPackagesListVo> selectByPhonenumberNameCh(String phonenumber, String nameCh, String keyword);
}
