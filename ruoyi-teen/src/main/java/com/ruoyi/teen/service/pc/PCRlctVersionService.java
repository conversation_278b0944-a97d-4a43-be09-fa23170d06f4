package com.ruoyi.teen.service.pc;

import com.ruoyi.teen.domain.RlctPadVersion;

import java.util.List;

public interface PCRlctVersionService {


    /**
     * 查询版本列表
     *
     * @param
     * @return 孩子集合
     */
    List<RlctPadVersion> selectRlctVersionList(RlctPadVersion rlctPadVersion);


    /**
     * 新增版本
     *
     * @param rlctPadVersion 版本信息
     * */
    int insertRlctVersion(RlctPadVersion rlctPadVersion);


    /*获取版本单个详情*/
    RlctPadVersion selectRlctVersionByVersionId(Long versionId);

    /*修改版本信息*/
    int updateRlctVersion(RlctPadVersion rlctPadVersion);

    /*单个或批量删除版本信息*/
    int deleteRlctVersionByVersionIds(Long[] versionIds);
}
