package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctParentTeen;

/**
 * 家长和孩子的关系Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctParentTeenService 
{
    /**
     * 查询家长和孩子的关系
     * 
     * @param id 家长和孩子的关系主键
     * @return 家长和孩子的关系
     */
    public RlctParentTeen selectRlctParentTeenById(Long id);

    /**
     * 查询家长和孩子的关系列表
     * 
     * @param rlctParentTeen 家长和孩子的关系
     * @return 家长和孩子的关系集合
     */
    public List<RlctParentTeen> selectRlctParentTeenList(RlctParentTeen rlctParentTeen);

    /**
     * 新增家长和孩子的关系
     * 
     * @param rlctParentTeen 家长和孩子的关系
     * @return 结果
     */
    public int insertRlctParentTeen(RlctParentTeen rlctParentTeen);

    /**
     * 修改家长和孩子的关系
     * 
     * @param rlctParentTeen 家长和孩子的关系
     * @return 结果
     */
    public int updateRlctParentTeen(RlctParentTeen rlctParentTeen);

    /**
     * 批量删除家长和孩子的关系
     * 
     * @param ids 需要删除的家长和孩子的关系主键集合
     * @return 结果
     */
    public int deleteRlctParentTeenByIds(Long[] ids);

    /**
     * 删除家长和孩子的关系信息
     * 
     * @param id 家长和孩子的关系主键
     * @return 结果
     */
    public int deleteRlctParentTeenById(Long id);
}
