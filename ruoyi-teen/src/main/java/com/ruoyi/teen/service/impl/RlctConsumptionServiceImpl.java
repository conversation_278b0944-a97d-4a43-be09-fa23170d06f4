package com.ruoyi.teen.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.teen.vo.pc.RlctConsumptionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctConsumptionMapper;
import com.ruoyi.teen.domain.RlctConsumption;
import com.ruoyi.teen.service.IRlctConsumptionService;

/**
 * 消费记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctConsumptionServiceImpl implements IRlctConsumptionService 
{
    @Autowired
    private RlctConsumptionMapper rlctConsumptionMapper;

    /**
     * 查询消费记录
     * 
     * @param id 消费记录主键
     * @return 消费记录
     */
    @Override
    public RlctConsumption selectRlctConsumptionById(Long id)
    {
        return rlctConsumptionMapper.selectRlctConsumptionById(id);
    }

    /**
     * 查询消费记录列表
     * 
     * @param rlctConsumption 消费记录
     * @return 消费记录
     */
    @Override
    public List<RlctConsumption> selectRlctConsumptionList(RlctConsumption rlctConsumption)
    {
        return rlctConsumptionMapper.selectRlctConsumptionList(rlctConsumption);
    }


    /**
     * 根据id查询消费记录列表所有名字
     * @param rlctConsumptionVo 消费记录名字
     * @return 消费记录列表名字集合
     */
    @Override
    public List<RlctConsumptionVo> selectRlctConsumptionVoList(RlctConsumptionVo rlctConsumptionVo) {
        return rlctConsumptionMapper.selectRlctConsumptionVoList(rlctConsumptionVo);
    }

    /**
     * 新增消费记录
     * 
     * @param rlctConsumption 消费记录
     * @return 结果
     */
    @Override
    public int insertRlctConsumption(RlctConsumption rlctConsumption)
    {
        rlctConsumption.setCreateTime(DateUtils.getNowDate());
        return rlctConsumptionMapper.insertRlctConsumption(rlctConsumption);
    }

    /**
     * 修改消费记录
     * 
     * @param rlctConsumption 消费记录
     * @return 结果
     */
    @Override
    public int updateRlctConsumption(RlctConsumption rlctConsumption)
    {
        rlctConsumption.setUpdateTime(DateUtils.getNowDate());
        return rlctConsumptionMapper.updateRlctConsumption(rlctConsumption);
    }

    /**
     * 批量删除消费记录
     * 
     * @param ids 需要删除的消费记录主键
     * @return 结果
     */
    @Override
    public int deleteRlctConsumptionByIds(Long[] ids)
    {
        return rlctConsumptionMapper.deleteRlctConsumptionByIds(ids);
    }

    /**
     * 删除消费记录信息
     * 
     * @param id 消费记录主键
     * @return 结果
     */
    @Override
    public int deleteRlctConsumptionById(Long id)
    {
        return rlctConsumptionMapper.deleteRlctConsumptionById(id);
    }
}
