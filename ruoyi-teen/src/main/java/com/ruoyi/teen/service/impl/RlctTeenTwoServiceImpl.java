package com.ruoyi.teen.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.teen.domain.*;
import com.ruoyi.teen.mapper.*;
import com.ruoyi.teen.utils.PinyinUtils;
import com.ruoyi.teen.vo.ipad.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.service.IRlctTeenTwoService;
import org.springframework.util.CollectionUtils;

/**
 * 孩子Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class RlctTeenTwoServiceImpl implements IRlctTeenTwoService
{
    private static final Logger logger = LoggerFactory.getLogger(RlctTeenTwoServiceImpl.class);

    @Autowired
    private RlctTeenTwoMapper rlctTeenTwoMapper;
    @Autowired
    private DailyHabitRecordsMapper dailyHabitRecordsMapper;
    @Autowired
    private GrowthDevelopmentMapper GrowthDevelopmentMapper;
    @Autowired
    private RecordOtherMapper recordOtherMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private RlctParentTeenMapper rlctParentTeenMapper;
    @Autowired
    private RlctVisitTwoMapper rlctVisitTwoMapper;
    @Autowired
    private RlctUserPackageMapper rlctUserPackageMapper;
    @Autowired
    private RlctVisitMapper rlctVisitMapper;


    /**
     * 就诊（档案）详情表
     *
     * @param parentTeenId 家长于孩子关系主键
     * @return 孩子
     */
    @Override
    public RecorddDetailVo selectRlctTeenTwoByTeenId(Long parentTeenId)
    {

        /*返回就诊详情*/
        RecorddDetailVo recorddDetailVo = new RecorddDetailVo();

        // 1. 处理患者基本信息
        recorddDetailVo.setRlctTeenTwo(buildTeenTwoVo(parentTeenId));

        // 2. 处理产品使用记录
        recorddDetailVo.setProductUse(getProductUseList(parentTeenId));

        // 3. 处理骨龄记录
        recorddDetailVo.setBoneAgeRecord(getBoneAgeRecords(parentTeenId));

        // 1. 查询就诊记录基本信息
        RlctTeenTwo rlctTeenTwo = rlctTeenTwoMapper.selectRlctTeenTwoByTeenId(parentTeenId);
        // 4. 处理历史就诊记录
        recorddDetailVo.setRlctHistoryVisitTwoList(getHistoryVisits(rlctTeenTwo.getTeenId()));

        // 先看有没有暂存行为记录的数据，有再去关系表查有没有分配治疗工作 、 如果分配了就根据不同医师查询已经分配不同治疗工作 、如果没有分配数据就返回未分配状态给前端

        // 2. 暂存记录的就诊ID
        RlctVisit twoRlctVisit = rlctVisitTwoMapper.selectRlctVisitByStatus(parentTeenId,2L);
        // 0.  治疗中就诊ID
        RlctVisit oneRlctVisit = rlctVisitTwoMapper.selectRlctVisitByStatus(parentTeenId,0L);
        // 5. 处理本次就诊记录
        recorddDetailVo.setRlctThisTimeVisitTwo(getCurrentVisit(parentTeenId,twoRlctVisit,oneRlctVisit));

        // 6. 处理日常行为习惯
        recorddDetailVo.setDailyHabitRecords(getDailyHabits(parentTeenId));

        // 7.判断是否有治疗中为 0L 他是不显示分配套餐和暂存行为按钮  显示只开产品和提交接诊记录  // 其余全按 2L 暂存行为   他是显示只开产品 显示分配套餐那个按钮和显示暂存行为按钮  不显示提交接诊记录
        // 安全获取当前就诊记录
        if (oneRlctVisit != null) {
            recorddDetailVo.setStatus(0);
        } else {
            recorddDetailVo.setStatus(2);
        }


        return recorddDetailVo;
    }

    // 6. 处理日常行为习惯
    private DailyHabitRecordsVo getDailyHabits(Long parentTeenId) {
        // 2. 暂存记录的就诊ID
        RlctVisit twoRlctVisit = rlctVisitTwoMapper.selectRlctVisitByStatus(parentTeenId,2L);
        // 0.  治疗中就诊ID
        RlctVisit oneRlctVisit = rlctVisitTwoMapper.selectRlctVisitByStatus(parentTeenId,0L);
        // 补充先通过就诊id去查询患者行为记录 如果没有就根据关系表id取最新一条
        if (oneRlctVisit != null){ //  有治疗中的情况
            return dailyHabitRecordsMapper.selectDailyHabitRecordsByVisitId(oneRlctVisit.getVisitId());
        }
        if (twoRlctVisit != null){  // 有暂存记录的情况
            return dailyHabitRecordsMapper.selectDailyHabitRecordsByVisitId(twoRlctVisit.getVisitId());
        }
        return dailyHabitRecordsMapper.selectDailyHabitRecordsParentTeenIdList(parentTeenId);
    }


    // 5. 处理本次就诊记录
    private RlctThisTimeVisitVo getCurrentVisit(Long parentTeenId, RlctVisit twoRlctVisit, RlctVisit oneRlctVisit) {
        if (oneRlctVisit != null){  // 有治疗中的情况
            Long visitId = oneRlctVisit.getVisitId();
            // 0. 有治疗中的情况 然后如果有分配的话在根据医师的id去查对应分配好给他的项目类型  如果是导诊的话就什么都不显示并且按钮没了
            Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
            RlctThisTimeVisitVo visit = rlctVisitTwoMapper.selectRlctThisTimeVisitTwoByVisitId(visitId);
            // 3.根据就诊id获取产品方案
            RlctProductUse rlctProductUse = rlctVisitTwoMapper.selectProductUseByVisitId(visitId);
            if (rlctProductUse != null){
                visit.setProductUseName(rlctProductUse.getProductUseName());
            }
            visit.setTreatments(getTreatmentsByVisitIdAndUserId(visitId, userId));
            return visit;
        }else if (twoRlctVisit != null) {
            Long visitId = twoRlctVisit.getVisitId();
            // 2. 有暂存记录的情况 然后如果有分配的话在根据医师的id去查对应分配好给他的项目类型   这里以下是多写着 可以注释掉他  一般暂存行为是不会出现分配治疗工作这个情况
            Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
            RlctThisTimeVisitVo visit = rlctVisitTwoMapper.selectRlctThisTimeVisitTwoByVisitId(visitId);
            // 3.根据就诊id获取产品方案
            RlctProductUse rlctProductUse = rlctVisitTwoMapper.selectProductUseByVisitId(visitId);
            if (rlctProductUse != null){
                visit.setProductUseName(rlctProductUse.getProductUseName());
            }
            visit.setTreatments(getTreatmentsByVisitIdAndUserId(visitId, userId));
            return visit;
        } else {
            // 3. 无暂存记录和治疗中的情况
            RlctTeenTwo teen = rlctTeenTwoMapper.selectRlctTeenTwoByTeenId(parentTeenId);
            RlctThisTimeVisitVo visit = new RlctThisTimeVisitVo();
            /*设置扣款账户*/
            visit.setPhonenumber(teen.getPhonenumber());
            //设置就诊时间
            visit.setAdmissionTime(DateUtils.getNowDate());
            return visit;
        }
    }

    // 5.1  不同医师治疗工作列表
    private List<TreatmentVo> getTreatmentsByVisitIdAndUserId(Long visitId, Long userId) {
        List<TreatmentVo> treatmentVos = rlctVisitTwoMapper.selectTreatmentsByVisitIdAndUserId(visitId, userId);
        treatmentVos.forEach(treatmentVo -> treatmentVo.setDictTreatmentTypeName(DictUtils.getDictLabel("record_treatment_type",
                String.valueOf(treatmentVo.getDictTreatmentType()), "未知")));
        return treatmentVos;
    }


    // 1. 处理患者基本信息
    private RlctTeenTwoVo buildTeenTwoVo(Long parentTeenId) {
        RlctTeenTwo teen = rlctTeenTwoMapper.selectRlctTeenTwoByTeenId(parentTeenId);
        if (teen == null) {
            throw new ServiceException("患者信息不存在");
        }

        RlctTeenTwoVo vo = new RlctTeenTwoVo();
        // 基础信息设置
        vo.setTeenName(teen.getTeenName());
        vo.setTeenAge(teen.getTeenAge());
        vo.setTeenBirth(teen.getTeenBirth());
        vo.setTeenSex(teen.getTeenSex());
        vo.setPhonenumber(teen.getPhonenumber());
        vo.setAllergyHistory(teen.getAllergyHistory());

        // 遗传身高计算
        // 遗传身高得进行计算  遗传计算公式 男孩 = (父亲身高+母亲身高+13)除以2 女孩 = (父亲身高+母亲身高-13)除以2
        if (teen.getTeenSex().equals("0")) {
            if (teen.getFatherHeight() != null && teen.getMotherHeight() != null) {
            BigDecimal geneticHeight = teen.getFatherHeight()
                    .add(teen.getMotherHeight())
                    .add(new BigDecimal(13))
                    .divide(new BigDecimal(2), 1, RoundingMode.HALF_UP); // 保留1位小数，四舍五入
            vo.setGeneticHeight(geneticHeight);
            }
        } else {
            if (teen.getFatherHeight() != null && teen.getMotherHeight() != null) {
            BigDecimal geneticHeight = teen.getFatherHeight()
                    .add(teen.getMotherHeight())
                    .subtract(new BigDecimal(13))
                    .divide(new BigDecimal(2), 1, RoundingMode.HALF_UP); // 保留1位小数，四舍五入
            vo.setGeneticHeight(geneticHeight);
            }
        }

        // 其他身体数据
        vo.setFatherHeight(teen.getFatherHeight());
        vo.setMotherHeight(teen.getMotherHeight());
        vo.setInitHeight(teen.getInitHeight());
        vo.setInitWeight(teen.getInitWeight());

        // 套餐信息
        setPackageInfo(vo, parentTeenId);

        // 上次就诊情况
        setLastVisitInfo(vo, teen.getTeenId());

        return vo;
    }

    private void setPackageInfo(RlctTeenTwoVo vo, Long parentTeenId) {
        RlctUserPackageVTwo userPackage = rlctTeenTwoMapper.selectRlctUserPackage(parentTeenId);
        if (userPackage != null) {
            vo.setRemainingTimes(userPackage.getRemainingTimes());
        }
    }

    private void setLastVisitInfo(RlctTeenTwoVo vo, Long teenId) {
        RlctVisitTwo lastVisit = rlctVisitTwoMapper.selectRlctVisitTwoLastTime(teenId);
        if (lastVisit != null) {
            vo.setHeightAfterTreatment(lastVisit.getHeightAfterTreatment());
            vo.setWeightAfterTreatment(lastVisit.getWeightAfterTreatment());
            vo.setVisitDetails(lastVisit.getVisitDetails());
        }
    }


    // 2. 处理产品使用记录
    private List<ProductUse> getProductUseList(Long parentTeenId) {
        return rlctTeenTwoMapper.selectProductUseList(parentTeenId);
    }

    // 3. 处理骨龄记录
    private List<RlctBoneAgeRecord> getBoneAgeRecords(Long parentTeenId) {
        return rlctTeenTwoMapper.selectBoneAgeRecordByParentTeenId(parentTeenId);
    }


    // 4. 处理历史就诊记录
    private List<RlctVisitVo> getHistoryVisits(Long TeenId) {
        List<RlctVisitVo> visits = rlctVisitTwoMapper.selectVisitsByParentTeenId(TeenId);
        if (visits != null) {
            for (RlctVisitVo visit : visits) {
                // 查询该就诊记录下的所有治疗项目
                visit.setTreatments(getTreatmentsByVisitId(visit.getVisitId()));
            }
        }
        return visits;
    }

    //4.1 查询该就诊记录下的所有治疗项目
    private List<TreatmentVo> getTreatmentsByVisitId(Long visitId) {
        List<TreatmentDTO> treatments = rlctVisitTwoMapper.selectTreatmentsByVisitId(visitId);
        List<TreatmentVo> result = new ArrayList<>();

        for (TreatmentDTO treatment : treatments) {
            TreatmentVo vo = new TreatmentVo();
            vo.setDoctorId(treatment.getDoctorId());
            vo.setDictTreatmentType(treatment.getDictTreatmentType());
            vo.setDictTreatmentTypeName(getTreatmentTypeName(treatment.getDictTreatmentType()));
            vo.setNickName(treatment.getNickName());
            result.add(vo);
        }

        return result;
    }

    //4.1.1 查询该就诊记录下的所有治疗项目
    private String getTreatmentTypeName(String treatmentType) {
        return DictUtils.getDictLabel("record_treatment_type", treatmentType, "未知");
    }

    /**
     * 带条件查询孩子列表
     *
     * @param
     * @return 孩子
     */
    @Override
    public List<RecordListVo> selectRlctTeenTwoqueryList(String phonenumber,String nameCh,String keyword)
    {
        // 1. 参数标准化处理（trim空字符串转null）
        phonenumber = StringUtils.trimToNull(phonenumber);
        nameCh = StringUtils.trimToNull(nameCh);
        keyword = StringUtils.trimToNull(keyword);

        // 2. 校验所有参数均为空（符合条件则提前返回）
        if (Stream.of(phonenumber, nameCh, keyword).allMatch(Objects::isNull)) {  // 过滤null
            return Collections.emptyList(); // 返回空集合比null更友好
        }

        // 查询基本信息(包含关系id)
        List<RecordListVo> list  = rlctTeenTwoMapper.selectDistinctTeenIds(phonenumber, nameCh, keyword);



        list.forEach(record -> {
            RecordListVo latestVisit = rlctTeenTwoMapper.selectLatestVisitByTeenId(record.getParentTeenId());
            if (latestVisit != null) {
                record.setHeightAfterTreatment(latestVisit.getHeightAfterTreatment());
                record.setWeightAfterTreatment(latestVisit.getWeightAfterTreatment());
            }
            // 业务层计算年龄
            record.calculateAge();
        });


        return list;
    }



    /**
     * 新增孩子
     *
     * @param recordVo 档案表
     * @return 结果
     */
    @Override
    public int insertRlctTeenTwo(RecordVo recordVo)
    {
        /*孩子表*/
        RlctTeenTwo rlctTeenTwo = recordVo.getRlctTeenTwo();
        DailyHabitRecords dailyHabitRecords = recordVo.getDailyHabitRecords();
        GrowthDevelopment growthDevelopment = recordVo.getGrowthDevelopment();
        RecordOther recordOther = recordVo.getRecordOther();
        /*通过手机号查询有无存在家长数据*/
        String phonenumber = rlctTeenTwo.getPhonenumber();
        SysUser sysUser = sysUserMapper.checkPhoneUnique(phonenumber);

        /*通过手机号查询有无存在家长数据*/
        if(sysUser == null){
            SysUser sysUser1  = new SysUser();
            /*家长表*/
            sysUser1.setPhonenumber(phonenumber);
            sysUser1.setUserName(phonenumber);
            sysUser1.setNickName("快乐每一天");
            sysUser1.setUserType("22");
            sysUser1.setCreateTime(DateUtils.getNowDate());
            /*新增家长*/
            sysUserMapper.insertUser(sysUser1); // 家长id
            Long userId = sysUser1.getUserId();
            /*新增孩子表*/
            RlctTeenDTO rlctTeenDTO = new RlctTeenDTO();
            rlctTeenDTO.setTeenName(rlctTeenTwo.getTeenName());
            rlctTeenDTO.setTeenAge(Long.valueOf(DateUtil.ageOfNow(rlctTeenTwo.getTeenBirth())));
            rlctTeenDTO.setTeenSex(rlctTeenTwo.getTeenSex());
            rlctTeenDTO.setTeenBirth(rlctTeenTwo.getTeenBirth());
            rlctTeenDTO.setInitWeight(rlctTeenTwo.getInitWeight());
            rlctTeenDTO.setInitHeight(rlctTeenTwo.getInitHeight());
            rlctTeenDTO.setInitTime(rlctTeenTwo.getInitTime());
            rlctTeenDTO.setFatherHeight(rlctTeenTwo.getFatherHeight());
            rlctTeenDTO.setMotherHeight(rlctTeenTwo.getMotherHeight());
            rlctTeenDTO.setMedicalHistory(rlctTeenTwo.getMedicalHistory());
            rlctTeenDTO.setVisitGoal(rlctTeenTwo.getVisitGoal());
            rlctTeenDTO.setAllergyHistory(rlctTeenTwo.getAllergyHistory());
            rlctTeenDTO.setGeneticHeight(rlctTeenTwo.getGeneticHeight());
            rlctTeenDTO.setBoneAge(rlctTeenTwo.getBoneAge());
            rlctTeenDTO.setTeenAddress(rlctTeenTwo.getTeenAddress());
            rlctTeenDTO.setCreateTime(DateUtils.getNowDate());
            rlctTeenDTO.setTeenNameCh(PinyinUtils.getFirstLetter(rlctTeenTwo.getTeenName()));
            rlctTeenDTO.setCreateBy(SecurityUtils.getUserId().toString());
            rlctTeenTwoMapper.insertRlctTeenTwo(rlctTeenDTO);// 孩子id
            Long teenId = rlctTeenDTO.getTeenId();
            logger.info("新增孩子成功teenId={}",teenId);
            /*新增家长与孩子关系表*/
            RlctParentTeen rlctParentTeen = new RlctParentTeen();
            rlctParentTeen.setParentId(userId);
            rlctParentTeen.setTeenId(teenId);
            rlctParentTeen.setCreateTime(DateUtils.getNowDate());
            rlctParentTeen.setCreateBy(SecurityUtils.getUserId().toString());
            rlctTeenTwoMapper.insertRlctParentTeen(rlctParentTeen);
            Long parentTeenId = rlctParentTeen.getId();
            logger.info("新增家长与孩子关系成功");

            insertRlctTeen(dailyHabitRecords,growthDevelopment,recordOther,parentTeenId);
            logger.info("新增日常行为、成长发育表、档案其他成功");
        }else {
            /*新增孩子表*/
            RlctTeenDTO rlctTeenDTO = new RlctTeenDTO();
            rlctTeenDTO.setTeenName(rlctTeenTwo.getTeenName());
            rlctTeenDTO.setTeenAge(Long.valueOf(DateUtil.ageOfNow(rlctTeenTwo.getTeenBirth())));
            rlctTeenDTO.setTeenSex(rlctTeenTwo.getTeenSex());
            rlctTeenDTO.setTeenBirth(rlctTeenTwo.getTeenBirth());
            rlctTeenDTO.setInitWeight(rlctTeenTwo.getInitWeight());
            rlctTeenDTO.setInitHeight(rlctTeenTwo.getInitHeight());
            rlctTeenDTO.setInitTime(rlctTeenTwo.getInitTime());
            rlctTeenDTO.setFatherHeight(rlctTeenTwo.getFatherHeight());
            rlctTeenDTO.setMotherHeight(rlctTeenTwo.getMotherHeight());
            rlctTeenDTO.setMedicalHistory(rlctTeenTwo.getMedicalHistory());
            rlctTeenDTO.setVisitGoal(rlctTeenTwo.getVisitGoal());
            rlctTeenDTO.setAllergyHistory(rlctTeenTwo.getAllergyHistory());
            rlctTeenDTO.setGeneticHeight(rlctTeenTwo.getGeneticHeight());
            rlctTeenDTO.setBoneAge(rlctTeenTwo.getBoneAge());
            rlctTeenDTO.setTeenAddress(rlctTeenTwo.getTeenAddress());
            rlctTeenDTO.setCreateTime(DateUtils.getNowDate());
            rlctTeenDTO.setTeenNameCh(PinyinUtils.getFirstLetter(rlctTeenTwo.getTeenName()));
            rlctTeenDTO.setCreateBy(SecurityUtils.getUserId().toString());
            rlctTeenTwoMapper.insertRlctTeenTwo(rlctTeenDTO);// 孩子id
            Long teenId = rlctTeenDTO.getTeenId();
            logger.info("新增孩子成功");
            /*新增家长与孩子关系表*/
            RlctParentTeen rlctParentTeen = new RlctParentTeen();
            /*区别在于家长id*/
            rlctParentTeen.setParentId(sysUser.getUserId());
            rlctParentTeen.setTeenId(teenId);
            rlctParentTeen.setCreateTime(DateUtils.getNowDate());
            rlctParentTeen.setCreateBy(SecurityUtils.getUserId().toString());
            rlctTeenTwoMapper.insertRlctParentTeen(rlctParentTeen);  //自增id传位对象内
            Long parentTeenId = rlctParentTeen.getId();
            logger.info("新增家长与孩子关系成功");
            insertRlctTeen(dailyHabitRecords,growthDevelopment,recordOther,parentTeenId);
            logger.info("新增日常行为、成长发育表、档案其他成功");
        }

        return 1;
    }

    private void insertRlctTeen( DailyHabitRecords dailyHabitRecords, GrowthDevelopment growthDevelopment, RecordOther recordOther,Long parentTeenId) {
        /*日常行为习惯表*/
        if (dailyHabitRecords != null) {
            DailyHabitRecords result = new DailyHabitRecords();
            result.setBedtime(dailyHabitRecords.getBedtime());
            result.setSleepDuration(dailyHabitRecords.getSleepDuration());
            result.setSleepOther(dailyHabitRecords.getSleepOther());
            result.setExerciseFrequency(dailyHabitRecords.getExerciseFrequency());
            result.setExerciseDuration(dailyHabitRecords.getExerciseDuration());
            result.setExerciseOther(dailyHabitRecords.getExerciseOther());
            result.setEatingHabits(dailyHabitRecords.getEatingHabits());
            result.setEatingOther(dailyHabitRecords.getEatingOther());
            result.setCalcium(dailyHabitRecords.getCalcium());
            result.setVitaminA(dailyHabitRecords.getVitaminA());
            result.setVitaminD(dailyHabitRecords.getVitaminD());
            result.setMultivitamin(dailyHabitRecords.getMultivitamin());
            result.setGaba(dailyHabitRecords.getGaba());
            result.setNutritionOther(dailyHabitRecords.getNutritionOther());
            result.setParentTeenId(parentTeenId);
            result.setCreateTime(DateUtils.getNowDate());
            result.setCreateBy(SecurityUtils.getLoginUser().getUserId().toString());
            dailyHabitRecordsMapper.insertDailyHabitRecords(result);
        }
        /*成长发育表*/
        if (growthDevelopment != null) {
            GrowthDevelopment rusult = new GrowthDevelopment();
            rusult.setHeightTwoYear(growthDevelopment.getHeightTwoYear());
            rusult.setHeightOneYear(growthDevelopment.getHeightOneYear());
            rusult.setExpectedHeightSixMonth(growthDevelopment.getExpectedHeightSixMonth());
            rusult.setBodyHair(growthDevelopment.getBodyHair());
            rusult.setAdamApple(growthDevelopment.getAdamApple());
            rusult.setVoiceChange(growthDevelopment.getVoiceChange());
            rusult.setMenstruation(growthDevelopment.getMenstruation());
            rusult.setBreastDevelopment(growthDevelopment.getBreastDevelopment());
            rusult.setUnderarmHair(growthDevelopment.getUnderarmHair());
            rusult.setParentTeenId(parentTeenId);
            rusult.setCreateTime(DateUtils.getNowDate());
            rusult.setCreateBy(SecurityUtils.getLoginUser().getUserId().toString());
            GrowthDevelopmentMapper.insertGrowthDevelopment(rusult);
        }
        /*档案其它表*/
        if (recordOther != null) {
            RecordOther recordOther1 = new RecordOther();
            recordOther1.setVisitSource(recordOther.getVisitSource());
            recordOther1.setReferee(recordOther.getReferee());
            recordOther1.setMessagePush(recordOther.getMessagePush());
            recordOther1.setParentTeenId(parentTeenId);
            recordOther1.setCreateTime(DateUtils.getNowDate());
            recordOther1.setCreateBy(SecurityUtils.getLoginUser().getUserId().toString());
            recordOtherMapper.insertRecordOther(recordOther1);
        }

    }


    /**
     * 查询用户已购套餐列表和类型是医师的列表
    * */
    @Override
    public PadTreatmentTypeSelectionVo treatmentTypeSelection(Long parentTeenId) {
        // 1. 参数校验
        if (parentTeenId == null) {
            return null;
        }
        /*通过家长与孩子的关系的主键查询家长的id   */

        PadTreatmentTypeSelectionVo resultVo  = new PadTreatmentTypeSelectionVo();

        /*查询是否有剩余次数的套餐{取第一条套餐id}从而载入状态*/
        // 2. 查询家长ID
        Long parentId = rlctParentTeenMapper.selectByparentTeenId(parentTeenId);
        if (parentId == null) {
            logger.warn("家长ID不存在，parentTeenId: {}", parentTeenId);  // 新增日志记录
            resultVo.setBuyPackageStatus(0);
            return resultVo;
        }

        // 3. 查询用户套餐
        List<PadUserPackageVo> userPackages = rlctUserPackageMapper.selectUserPackageList(parentId);
        //用于判断一个集合是否为空。该方法不仅可以判断集合是否为 null，还可以判断集合是否不包含任何元素（即空集合）
        if (CollectionUtils.isEmpty(userPackages)) {
            logger.warn("家长ID存在套餐次数不足，parentTeenId: {}", parentTeenId);  // 新增日志记录
            resultVo.setBuyPackageStatus(0);
            return resultVo;
        }


        // 4. 设置返回数据
        resultVo.setDoctorUserList(rlctUserPackageMapper.selectDoctorUserList());
        resultVo.setPackageBuyList(userPackages);
        resultVo.setBuyPackageStatus(1);

        return resultVo ;
    }



}
