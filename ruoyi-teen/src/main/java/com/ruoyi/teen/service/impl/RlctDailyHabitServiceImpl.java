package com.ruoyi.teen.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.teen.domain.config.Diagnose;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctDailyHabitMapper;
import com.ruoyi.teen.domain.RlctDailyHabit;
import com.ruoyi.teen.service.IRlctDailyHabitService;

/**
 * 患者记录日常习惯Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Service
public class RlctDailyHabitServiceImpl extends ServiceImpl<RlctDailyHabitMapper,RlctDailyHabit> implements IRlctDailyHabitService
{
    @Autowired
    private RlctDailyHabitMapper rlctDailyHabitMapper;
    private static final Logger logger = LoggerFactory.getLogger(RlctDailyHabitServiceImpl.class);
    /**
     * 查询患者记录日常习惯
     *
     * @param id 患者记录日常习惯主键
     * @return 患者记录日常习惯
     */
    @Override
    public RlctDailyHabit selectRlctDailyHabitById(Long id)
    {
        return rlctDailyHabitMapper.selectRlctDailyHabitById(id);
    }

    /**
     * 查询患者记录日常习惯列表
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 患者记录日常习惯
     */
    @Override
    public List<RlctDailyHabit> selectRlctDailyHabitList(RlctDailyHabit rlctDailyHabit)
    {
        return rlctDailyHabitMapper.selectRlctDailyHabitList(rlctDailyHabit);
    }

    /**
     * 新增患者记录日常习惯
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 结果
     */
    @Override
    public int insertRlctDailyHabit(RlctDailyHabit rlctDailyHabit)
    {
        rlctDailyHabit.setCreateTime(DateUtils.getNowDate());
        return rlctDailyHabitMapper.insertRlctDailyHabit(rlctDailyHabit);
    }


    /**
     * 平板端打印记录表
     * @param getIds
     * @return
     */
    @Override
    public boolean insertHabit(Diagnose diagnose, Long parentTeenId){
        Date date = DateUtils.getNowDate();
        List<RlctDailyHabit> list = new ArrayList<>();
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getSleep()));//睡眠习惯
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getDuration()));//睡眠时长
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getJump()));//跳绳
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getHeightJump()));//摸高跳
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getBar()));//吊单杠
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getMilk()));//奶类
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getEgg()));//蛋类
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getVegetable()));//蔬菜
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getRaw()));//肉类
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getGrain()));//五谷杂粮
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getSnacks()));//饮料零食
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getEatSnack()));//吃宵夜
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getvAt()));//维生素A
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getvDt()));//维生素D
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getCa()));//钙
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getSynthesis()));//综合维生素
        list.add(new RlctDailyHabit(parentTeenId,date,diagnose.getAcid()));//氨基酸
        this.saveBatch(list);
        return true;
    }

    /**
     * 修改患者记录日常习惯
     *
     * @param rlctDailyHabit 患者记录日常习惯
     * @return 结果
     */
    @Override
    public int updateRlctDailyHabit(RlctDailyHabit rlctDailyHabit)
    {
        return rlctDailyHabitMapper.updateRlctDailyHabit(rlctDailyHabit);
    }

    /**
     * 批量删除患者记录日常习惯
     *
     * @param ids 需要删除的患者记录日常习惯主键
     * @return 结果
     */
    @Override
    public int deleteRlctDailyHabitByIds(Long[] ids)
    {
        return rlctDailyHabitMapper.deleteRlctDailyHabitByIds(ids);
    }

    /**
     * 删除患者记录日常习惯信息
     *
     * @param id 患者记录日常习惯主键
     * @return 结果
     */
    @Override
    public int deleteRlctDailyHabitById(Long id)
    {
        return rlctDailyHabitMapper.deleteRlctDailyHabitById(id);
    }
}
