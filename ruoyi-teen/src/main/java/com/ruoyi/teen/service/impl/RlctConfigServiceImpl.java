package com.ruoyi.teen.service.impl;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.cache.utils.CacheDictUtils;
import com.ruoyi.cache.utils.CacheRlctConfigUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.teen.domain.*;
import com.ruoyi.teen.domain.config.AttributeDetails;
import com.ruoyi.teen.domain.config.AttributeListBean;
import com.ruoyi.teen.domain.config.Diagnose;
import com.ruoyi.teen.mapper.*;
import com.ruoyi.teen.service.IRlctDailyHabitService;
import com.ruoyi.teen.service.IRlctTeenWightHeightService;
import com.ruoyi.teen.service.IRlctUserPackageService;
import com.ruoyi.teen.utils.*;
import com.ruoyi.teen.vo.ipad.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.service.IRlctConfigService;

/**
 * 配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctConfigServiceImpl implements IRlctConfigService
{
    @Autowired
    private RlctConfigMapper rlctConfigMapper;
    @Autowired
    private IRlctConfigService rlctConfigService;
    @Autowired
    private RlctUserPackageMapper rlctUserPackageMapper;
    @Autowired
    private RlctVisitMapper rlctVisitMapper;
    @Autowired
    private IRlctDailyHabitService rlctDailyHabitService;
    @Autowired
    private IRlctTeenWightHeightService rlctTeenWightHeightService;
    @Autowired
    private RlctTeenWightHeightMapper rlctTeenWightHeightMapper;
    @Autowired
    private RlctParentTeenMapper rlctParentTeenMapper;
    @Autowired
    private RlctTeenMapper tlctTeenMapper;
    @Autowired
    private RlctDailyHabitMapper rlctDailyHabitMapper;

    private static final Logger logger = LoggerFactory.getLogger(RlctConfigServiceImpl.class);
    /**
     * 查询配置
     *
     * @param id 配置主键
     * @return 配置
     */
    @Override
    public RlctConfig selectRlctConfigById(Long id)
    {
        return rlctConfigMapper.selectRlctConfigById(id);
    }

    /**
     * 查询配置列表
     *
     * @param rlctConfig 配置
     * @return 配置
     */
    @Override
    public List<RlctConfig> selectRlctConfigList(RlctConfig rlctConfig)
    {
        return rlctConfigMapper.selectRlctConfigList(rlctConfig);
    }


    /**
     * 平板端封装获取config类的数据
     * @param rlctConfig
     * @return
     */
    @Override
    public List<configVo> getList(RlctConfig rlctConfig) {
        List<RlctConfig> list = rlctConfigService.selectRlctConfigList(rlctConfig);
        // 构造 configVo 列表
        List<configVo> configVoList = list.stream()
                .collect(Collectors.groupingBy(RlctConfig::getConfigType)) // 根据 configType 分组
                .entrySet().stream()
                .map(entry -> new configVo(entry.getKey(), entry.getValue().stream()
                        .collect(Collectors.groupingBy(RlctConfig::getAttribute)) // 根据 attribute 分组
                        .entrySet().stream()
                        .map(attributeEntry -> new attributeVo(attributeEntry.getKey(), attributeEntry.getValue().stream()
                                .map(config -> new atbClassVo(config.getId(), config.getAttribute(), config.getParam(), config.getScore(), config.getSort()))
                                .collect(Collectors.toList())))
                        .collect(Collectors.toList())))
                .collect(Collectors.toList());

        for (configVo cfgVo : configVoList) {
            cfgVo.setName(CacheDictUtils.toDictLabel("rlct_config_type",cfgVo.getName()));
        }
        System.out.println(configVoList);
        return configVoList;
    }

    @Override
    public AttributeListBean listInfo(){
        AttributeListBean bean = new AttributeListBean();
        List<RlctConfig> list = rlctConfigMapper.selectRlctConfigList(new RlctConfig());
        bean.setSleepList(getAttribute(list,"入睡时间"));
        bean.setDurationList(getAttribute(list,"睡眠间长"));
        bean.setJumpList(getAttribute(list,"跳绳"));
        bean.setHeightJumpList(getAttribute(list,"摸高跳"));
        bean.setBarList(getAttribute(list,"吊单杠"));
        bean.setMilkList(getAttribute(list,"奶类"));
        bean.setEggList(getAttribute(list,"蛋类"));
        bean.setVegetableList(getAttribute(list,"蔬菜"));
        bean.setRawList(getAttribute(list,"肉类"));
        bean.setGrainList(getAttribute(list,"五谷杂粮"));
        bean.setSnacksList(getAttribute(list,"饮料零食"));
        bean.setEatSnackList(getAttribute(list,"吃夜宵"));
        bean.setvAtList(getAttribute(list,"维生素A"));
        bean.setvDtList(getAttribute(list,"维生素D"));
        bean.setCaList(getAttribute(list,"钙"));
        bean.setSynthesisList(getAttribute(list,"综合维生素"));
        bean.setAcidList(getAttribute(list,"氨基酸"));
        return bean;
    }


    /**
     * 查询出attribute为name的list
     * @param list
     * @param name
     * @return
     */
    private List<AttributeDetails> getAttribute(List<RlctConfig> list,String name){
        List<AttributeDetails> resList = new ArrayList<>();
        for (RlctConfig config : list) {
            if(config.getAttribute().equals(name)){
                resList.add(new AttributeDetails(config.getId(),config.getParam()));
            }
        }
        return resList;
    }



    /**
     * 新增配置
     *
     * @param rlctConfig 配置
     * @return 结果
     */
    @Override
    public int insertRlctConfig(RlctConfig rlctConfig)
    {
        rlctConfig.setCreateTime(DateUtils.getNowDate());
        return rlctConfigMapper.insertRlctConfig(rlctConfig);
    }

    /**
     * 修改配置
     *
     * @param rlctConfig 配置
     * @return 结果
     */
    @Override
    public int updateRlctConfig(RlctConfig rlctConfig)
    {
        rlctConfig.setUpdateTime(DateUtils.getNowDate());
        return rlctConfigMapper.updateRlctConfig(rlctConfig);
    }

    /**
     * 批量删除配置
     *
     * @param ids 需要删除的配置主键
     * @return 结果
     */
    @Override
    public int deleteRlctConfigByIds(Long[] ids)
    {
        return rlctConfigMapper.deleteRlctConfigByIds(ids);
    }

    /**
     * 删除配置信息
     *
     * @param id 配置主键
     * @return 结果
     */
    @Override
    public int deleteRlctConfigById(Long id)
    {
        return rlctConfigMapper.deleteRlctConfigById(id);
    }

//    /**
//     * 平板端套餐下单及保存信息配置表
//     * @param visitVo
//     * @return
//     */
//    @Override
//    public int finish(VisitVo visitVo)throws RuntimeException{
//        Date date= DateUtils.getNowDate();
//        BigDecimal height;
//        BigDecimal correctHeight;
//        //保存配置类就诊记录单体重、身高需要计算判断 校验
//        height = getHeight(visitVo.getParentTeenId(),visitVo.getHeight());
//        //计算修正身高
//        correctHeight = getCorrectHeight(visitVo.getParentTeenId(),visitVo.getHeight(),date);
//        RlctTeenWightHeight rlctTeenWightHeight = new RlctTeenWightHeight();
//        rlctTeenWightHeight.setWeight(visitVo.getWeight());
//        rlctTeenWightHeight.setParentTeenId(visitVo.getParentTeenId());
//        rlctTeenWightHeight.setHeight(height);
//        //修正身高
//        rlctTeenWightHeight.setCorrectHeight(correctHeight);
//        rlctTeenWightHeight.setCreateTime(date);
//        //查询套餐次数、是否正常
//        List<UserPackageVo> list = rlctUserPackageMapper
//                .selectUserPackage(Long.valueOf(visitVo.getDeductParentId()),visitVo.getId());
//        if (list!=null){
//            rlctTeenWightHeightMapper.insertRlctTeenWightHeight(rlctTeenWightHeight);
//        }else{
//            throw new RuntimeException("套餐已用完或失效");
//        }
//        //保存配置类就诊记录单
//        rlctDailyHabitService.insertHabit(visitVo.getGetIds(), visitVo.getParentTeenId());
//        //平板端下单新增就诊记录
//        visitVo.setStatus(0L);
//        visitVo.setCreateTime(DateUtils.getNowDate());
//        rlctVisitMapper.insertIpadVisit(visitVo);
//        //平板端下单时更新这个用户的套餐的套餐消费中次数+1
//        rlctUserPackageMapper.upIpadUserPackage(visitVo.getId());
//        return 1;
//    }


    /**
     * 获取生成诊疗单Word的参数
     * @param visit
     * @param diagnose
     * @return
     */
//    private Map<String,Object> getDocParam(RlctVisit visit,Diagnose diagnose){
    private Map<String,Object> getDocParam(Long visitId){
        RlctVisit visit = rlctVisitMapper.selectRlctVisitByVisitId(visitId);
        RlctParentTeen parentTeen = rlctParentTeenMapper.selectRlctParentTeenById(visit.getParentTeenId());
        RlctTeen teen = tlctTeenMapper.selectRlctTeenByTeenId(parentTeen.getTeenId());

        List<RlctTeenWightHeight> list = rlctTeenWightHeightService.getHeight(visit.getParentTeenId(),2L);
        RlctTeenWightHeight curWightHeight = list.get(0);//诊前身高
        RlctTeenWightHeight lastWightHeight = null;//上次身高
        if(list.size() > 1){
            lastWightHeight = list.get(1);
        }else{
            lastWightHeight = curWightHeight;
        }

        Map<String,Object> map = new HashMap<>();
        map.put("visitNo",visit.getVisitNo());//编号
        map.put("teenName",teen.getTeenName());//姓名
        map.put("teenSex",CacheDictUtils.toDictLabel("sys_user_sex",teen.getTeenSex()));//性别
        map.put("teenAge",teen.getTeenAge());//年龄
        map.put("initHeight", BigDecimalUtils.decimalFormat(teen.getInitHeight())+"cm");//初诊身高
        map.put("lastHeight",BigDecimalUtils.decimalFormat(lastWightHeight.getHeight())+"cm");//上次身高
        map.put("curHeight",BigDecimalUtils.decimalFormat(curWightHeight.getHeight())+"cm");//诊前身高

        for(Map.Entry<String, Object> entry:map.entrySet()){
            if(entry.getValue() == null){
                entry.setValue("");//null生成word会报错，转成空串
            }
        }
        return map;
    }

    private String createPdf(Map<String,Object> param) throws Exception {
        String wordFileName = System.getProperty("java.io.tmpdir") + File.separator + UUID.randomUUID().toString() +".doc";
        //生成Word
        File wordFile = WordUtil.createDoc(param,"diagnosisForm.ftl",wordFileName);
        //生成PDF
        String pdfPath = PDFUtils.wordToPdf(wordFile);
        return pdfPath;
    }

    public String downloadPdf(Long visitId) throws Exception{
        Map<String,Object> param = getDocParam(visitId);
        String pdfPath = createPdf(param);
        return pdfPath;
    }

    @Override
    public String saveConfigAndPdf(Diagnose diagnose) throws Exception{
        RlctVisit visit = saveConfigDiagnose(diagnose);
        Map<String,Object> param = getDocParam(visit.getVisitId());
        String pdfPath = createPdf(param);
        return pdfPath;
    }


    @Override
    public RlctVisit saveConfig(Diagnose diagnose)throws RuntimeException{
        return saveConfigDiagnose(diagnose);
    }

    @Override
    public Diagnose getLastConfig(Long teenId,Long patientId)throws RuntimeException{
        Diagnose diagnose = new Diagnose();

        RlctParentTeen parentTeen = rlctParentTeenMapper.selectByParentIdAndTeenId(patientId,teenId);
        if(parentTeen == null){
            return diagnose;
        }
        Long parentTeenId = parentTeen.getId();
        RlctTeenWightHeight wh = rlctTeenWightHeightMapper.getLastRlctTeenWightHeight(parentTeenId);
        if(wh != null){
            diagnose.setWeight(wh.getWeight());
            diagnose.setHeight(wh.getHeight());
        }

        List<RlctDailyHabit> list = rlctDailyHabitMapper.selectLastDailyHabit(parentTeenId);

        diagnose.setSleep(findRlctDailyHabit(list,1,2,3,4,5,6));
        diagnose.setDuration(findRlctDailyHabit(list,7,8,9,10));
        diagnose.setJump(findRlctDailyHabit(list,11,12,13));
        diagnose.setHeightJump(findRlctDailyHabit(list,14,15,16));
        diagnose.setBar(findRlctDailyHabit(list,17,18,19));
        diagnose.setMilk(findRlctDailyHabit(list,20,21,22));
        diagnose.setEgg(findRlctDailyHabit(list,23,24,25));
        diagnose.setVegetable(findRlctDailyHabit(list,26,27,28));
        diagnose.setRaw(findRlctDailyHabit(list,29,30,31));
        diagnose.setGrain(findRlctDailyHabit(list,32,33,34));
        diagnose.setSnacks(findRlctDailyHabit(list,35,36));
        diagnose.setEatSnack(findRlctDailyHabit(list,37,38));
        diagnose.setvAt(findRlctDailyHabit(list,39,40));
        diagnose.setvDt(findRlctDailyHabit(list,41,42));
        diagnose.setCa(findRlctDailyHabit(list,43,44));
        diagnose.setAcid(findRlctDailyHabit(list,45,46));
        diagnose.setSynthesis(findRlctDailyHabit(list,47,48));
        return diagnose;
    }

    private Long findRlctDailyHabit(List<RlctDailyHabit> list,Integer ... ids){
        List<Integer> idsList = Arrays.asList(ids);
        for (RlctDailyHabit habit : list) {
            if(idsList.contains(habit.getConfigId().intValue())){
                return habit.getConfigId();
            }
        }
        return null;
    }

    /**
     * 保存诊疗数据
     * @param diagnose
     * @return
     * @throws RuntimeException
     */
    private RlctVisit saveConfigDiagnose(Diagnose diagnose)throws RuntimeException{
        Date date= DateUtils.getNowDate();
        BigDecimal correctHeight;

        RlctParentTeen rlctParentTeen = rlctParentTeenMapper.selectByParentIdAndTeenId(diagnose.getPatientId(),diagnose.getTeenId());
        if(rlctParentTeen == null){
            throw new RuntimeException("小孩不存在，请联系管理员");
        }

        //保存配置类就诊记录单体重、身高需要计算判断 校验
        checkHeight(rlctParentTeen.getId(),diagnose.getHeight());
        //计算修正身高
        correctHeight = getCorrectHeight(diagnose.getTeenId(),diagnose.getHeight(),date);
        RlctTeenWightHeight rlctTeenWightHeight = new RlctTeenWightHeight();
        rlctTeenWightHeight.setWeight(diagnose.getWeight());
        rlctTeenWightHeight.setParentTeenId(rlctParentTeen.getId());
        rlctTeenWightHeight.setHeight(diagnose.getHeight());
        rlctTeenWightHeight.setCorrectHeight(correctHeight); //修正身高
        rlctTeenWightHeight.setCreateBy(SecurityUtils.getUsername());
        rlctTeenWightHeight.setCreateTime(date);

        //查询套餐次数、是否正常
        RlctUserPackage userPackage = rlctUserPackageMapper.selectRlctUserPackageById(diagnose.getUserPackageId());
        if(userPackage == null){
            throw new RuntimeException("套餐不存在");
        }
        if((userPackage.getPackageTotalTimes() - userPackage.getPackageNumberTimes() - userPackage.getPackageDuringConsumption()) <= 0){
            throw new RuntimeException("套餐已用完或失效");
        }

        int countWh = rlctTeenWightHeightMapper.countByParentTeenId(rlctParentTeen.getId());
        if(countWh == 0){
            //countWh=0是初诊，更新患者的初诊信息
            RlctTeen upTeen = new RlctTeen();
            upTeen.setTeenId(diagnose.getTeenId());
            upTeen.setInitHeight(diagnose.getHeight());
            upTeen.setInitWeight(diagnose.getWeight());
            upTeen.setInitTime(date);
            tlctTeenMapper.updateRlctTeen(upTeen);
        }

        //插入身高
        rlctTeenWightHeightMapper.insertRlctTeenWightHeight(rlctTeenWightHeight);

        //保存配置类就诊记录单
        rlctDailyHabitService.insertHabit(diagnose, rlctParentTeen.getId());

        //配置单数据计算成分数，保存到患者表中
        saveScore(diagnose);

        //平板端下单新增就诊记录
        RlctVisit rlctVisit = new RlctVisit();
        rlctVisit.setParentTeenId(rlctParentTeen.getId());
        rlctVisit.setDeductParentId(rlctParentTeen.getParentId().toString());
        rlctVisit.setPackageId(userPackage.getPackageId());
        rlctVisit.setStatus(0L);
        rlctVisit.setCreateBy(SecurityUtils.getUsername());
        rlctVisit.setCreateTime(date);
        rlctVisit.setUpdateBy(SecurityUtils.getUsername());
        rlctVisit.setUpdateTime(date);
        rlctVisit.setUserPackageId(userPackage.getId());
        try {
            rlctVisit.setDeptId(SecurityUtils.getDeptId());
        } catch (Exception e) {
        }
        rlctVisit.setVisitNo(VisitNoUtils.getVisitNo());
        rlctVisitMapper.insert(rlctVisit);

        //平板端下单时更新这个用户的套餐的套餐消费中次数+1
        rlctUserPackageMapper.upIpadUserPackage(diagnose.getUserPackageId());
        return rlctVisit;
    }

    //配置单数据计算成分数，保存到患者表中
    private void saveScore(Diagnose diagnose){
        //随眠起始时间分数
        BigDecimal teenSleepTime = CacheRlctConfigUtils.getRlctConfigById(diagnose.getSleep()).getScore();

        //随眠时长分数
        BigDecimal teenSleepDurationTime = CacheRlctConfigUtils.getRlctConfigById(diagnose.getDuration()).getScore();

        //运动习惯分数
        BigDecimal teenExerciseHabit = BigDecimal.ZERO;
        teenExerciseHabit = teenExerciseHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getJump()).getScore());//跳绳
        teenExerciseHabit = teenExerciseHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getHeightJump()).getScore());//摸高跳
        teenExerciseHabit = teenExerciseHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getBar()).getScore());//吊单杠
        if(teenExerciseHabit.compareTo(BigDecimal.ZERO) == -1){
            teenExerciseHabit = BigDecimal.ZERO;
        }

        //饮食习惯分数
        BigDecimal teenEatHabit = BigDecimal.ZERO;
        teenEatHabit = teenEatHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getMilk()).getScore());//奶类
        teenEatHabit = teenEatHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getEgg()).getScore());//蛋类
        teenEatHabit = teenEatHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getVegetable()).getScore());//蔬菜
        teenEatHabit = teenEatHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getRaw()).getScore());//肉类
        teenEatHabit = teenEatHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getGrain()).getScore());//五谷杂粮
        teenEatHabit = teenEatHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getSnacks()).getScore());//饮料零食
        teenEatHabit = teenEatHabit.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getEatSnack()).getScore());//吃宵夜
        if(teenEatHabit.compareTo(BigDecimal.ZERO) == -1){
            teenEatHabit = BigDecimal.ZERO;
        }

        //营养素分数
        BigDecimal teenNutrient = BigDecimal.ZERO;
        teenNutrient = teenNutrient.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getvAt()).getScore());//维生素A
        teenNutrient = teenNutrient.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getvDt()).getScore());//维生素D
        teenNutrient = teenNutrient.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getCa()).getScore());//钙
        teenNutrient = teenNutrient.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getSynthesis()).getScore());//综合维生素
        teenNutrient = teenNutrient.add(CacheRlctConfigUtils.getRlctConfigById(diagnose.getAcid()).getScore());//氨基酸
        if(teenNutrient.compareTo(BigDecimal.ZERO) == -1){
            teenNutrient = BigDecimal.ZERO;
        }

        RlctTeen rlctTeen = new RlctTeen();
        rlctTeen.setTeenId(diagnose.getTeenId());
        rlctTeen.setTeenSleepTime(teenSleepTime); //随眠起始时间分数
        rlctTeen.setTeenSleepDurationTime(teenSleepDurationTime);//随眠时长分数
        rlctTeen.setTeenExerciseHabit(teenExerciseHabit);//运动习惯分数
        rlctTeen.setTeenEatHabit(teenExerciseHabit);//饮食习惯分数
        rlctTeen.setTeenNutrient(teenNutrient);//营养素分数
        tlctTeenMapper.updateRlctTeen(rlctTeen);
    }

    /**
     * 判断身高是否小于(前两次身高平均值-0.5cm)
     * @param id  父母与孩子的关系id
     * @param height 当前的测量的身高
     * @return
     */
    private void checkHeight(Long id,BigDecimal height) throws RuntimeException{
        BigDecimal rHeight = BigDecimal.ZERO;
        List<RlctTeenWightHeight> list = rlctTeenWightHeightService.getHeight(id,2L);
        if(list.size() != 2){
            return;
        }
        for (RlctTeenWightHeight rtw : list) {
            rHeight = rHeight.add(rtw.getHeight());
        }
        //身高平均值
        rHeight=rHeight.divide(new BigDecimal("2.00"),2, RoundingMode.HALF_UP);
        rHeight = rHeight.subtract(new BigDecimal("0.50"));
        //小于身高平均值
        if(height.compareTo(rHeight) == -1){
            throw new RuntimeException("身高小于前两次平均值");
        }
    }

    /**
     * 计算修正身高
     * @param id 父母与孩子的关系id
     * @param date 当前时间
     * @return
     */
    private BigDecimal getCorrectHeight(Long teenId,BigDecimal height,Date date){
        BigDecimal correctHeight= new BigDecimal(0.00);
        RlctTeen teen = tlctTeenMapper.selectRlctTeenByTeenId(teenId);
        if (teen.getInitHeight() == null){
            //初诊身高==null,返回原高
            return height;
        }else{
            //获取上一次孩子的身高测量时间
            Date lastDate = teen.getInitTime();
            int lastHour=lastDate.getHours();
            //当前孩子的身高测量时间
            int hour = date.getHours();
            //计算孩子的修正身高=当前身高 + (当前时间hours-上一次时间hours)/100
            correctHeight=correctHeight.add(height
                    .add(new BigDecimal(hour - lastHour).divide(new BigDecimal(100))));
        }
        return correctHeight;
    }

//    private BigDecimal getCorrectHeight(Long id,BigDecimal height,Date date){
//        BigDecimal correctHeight= new BigDecimal(0.00);
//        List<RlctTeenWightHeight> listOne = rlctTeenWightHeightService.getHeight(id,1L);
//        System.out.println(listOne);
//        if (listOne.size() !=0){
//            for (RlctTeenWightHeight rtwOne : listOne) {
//                //获取上一次孩子的身高测量时间
//                Date lastDate = rtwOne.getCreateTime();
//                int lastHour=lastDate.getHours();
//                //当前孩子的身高测量时间
//                int hour = date.getHours();
//                //计算孩子的修正身高=当前身高 + (当前时间hours-上一次时间hours)/100
//                correctHeight=correctHeight.add(height
//                        .add(new BigDecimal(hour - lastHour).divide(new BigDecimal(100))));
//            }
//        }
//        return correctHeight;
//    }
}
