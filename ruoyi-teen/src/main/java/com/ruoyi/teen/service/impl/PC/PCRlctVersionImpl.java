package com.ruoyi.teen.service.impl.PC;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.teen.domain.RlctPadVersion;
import com.ruoyi.teen.mapper.pc.PCRlctVersionMapper;
import com.ruoyi.teen.service.pc.PCRlctVersionService;
import com.ruoyi.teen.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class PCRlctVersionImpl implements PCRlctVersionService {
    //logger
    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(PCRlctVersionImpl.class);

    @Autowired
    private PCRlctVersionMapper rlctVersionMapper;

    /**
     * 查询版本列表
    * */
    @Override
    public List<RlctPadVersion> selectRlctVersionList(RlctPadVersion rlctPadVersion) {
        return rlctVersionMapper.selectRlctVersionList(rlctPadVersion);

    }


    /**
    *  新增版本
    * */
    @Override
    public int insertRlctVersion(RlctPadVersion rlctPadVersion) {
        rlctPadVersion.setCreateTime(DateUtils.getNowDate());
        rlctPadVersion.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        return rlctVersionMapper.insertRlctVersion(rlctPadVersion);
    }


    /**
     * 获取版本详情
    * */
    @Override
    public RlctPadVersion selectRlctVersionByVersionId(Long versionId) {
        return rlctVersionMapper.selectRlctVersionByVersionId(versionId);
    }

    /**
     * 修改版本详情
    * */
    @Override
    public int updateRlctVersion(RlctPadVersion rlctPadVersion) {
        rlctPadVersion.setUpdateTime(DateUtils.getNowDate());
        rlctPadVersion.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
        return rlctVersionMapper.updateRlctVersion(rlctPadVersion);
    }


    /**
    * 批量删除版本
    * */
    @Override
    public int deleteRlctVersionByVersionIds(Long[] versionIds) {
        return rlctVersionMapper.deleteRlctVersionByVersionIds(versionIds);
    }
}
