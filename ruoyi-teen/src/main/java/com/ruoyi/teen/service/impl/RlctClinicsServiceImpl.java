package com.ruoyi.teen.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctClinicsMapper;
import com.ruoyi.teen.domain.RlctClinics;
import com.ruoyi.teen.service.IRlctClinicsService;

/**
 * 医馆信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctClinicsServiceImpl implements IRlctClinicsService 
{
    @Autowired
    private RlctClinicsMapper rlctClinicsMapper;

    /**
     * 查询医馆信息
     * 
     * @param clinicId 医馆信息主键
     * @return 医馆信息
     */
    @Override
    public RlctClinics selectRlctClinicsByClinicId(Long clinicId)
    {
        return rlctClinicsMapper.selectRlctClinicsByClinicId(clinicId);
    }

    /**
     * 查询医馆信息列表
     * 
     * @param rlctClinics 医馆信息
     * @return 医馆信息
     */
    @Override
    public List<RlctClinics> selectRlctClinicsList(RlctClinics rlctClinics)
    {
        return rlctClinicsMapper.selectRlctClinicsList(rlctClinics);
    }

    /**
     * 新增医馆信息
     * 
     * @param rlctClinics 医馆信息
     * @return 结果
     */
    @Override
    public int insertRlctClinics(RlctClinics rlctClinics)
    {
        rlctClinics.setCreateTime(DateUtils.getNowDate());
        rlctClinics.setCreateBy(SecurityUtils.getUsername());
        return rlctClinicsMapper.insertRlctClinics(rlctClinics);
    }

    /**
     * 修改医馆信息
     * 
     * @param rlctClinics 医馆信息
     * @return 结果
     */
    @Override
    public int updateRlctClinics(RlctClinics rlctClinics)
    {
        rlctClinics.setUpdateTime(DateUtils.getNowDate());
        rlctClinics.setUpdateBy(SecurityUtils.getUsername());
        return rlctClinicsMapper.updateRlctClinics(rlctClinics);
    }

    /**
     * 批量删除医馆信息
     * 
     * @param clinicIds 需要删除的医馆信息主键
     * @return 结果
     */
    @Override
    public int deleteRlctClinicsByClinicIds(Long[] clinicIds)
    {
        return rlctClinicsMapper.deleteRlctClinicsByClinicIds(clinicIds);
    }

    /**
     * 删除医馆信息信息
     * 
     * @param clinicId 医馆信息主键
     * @return 结果
     */
    @Override
    public int deleteRlctClinicsByClinicId(Long clinicId)
    {
        return rlctClinicsMapper.deleteRlctClinicsByClinicId(clinicId);
    }
}
