package com.ruoyi.teen.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctTreatmentsMapper;
import com.ruoyi.teen.domain.RlctTreatments;
import com.ruoyi.teen.service.IRlctTreatmentsService;

/**
 * 项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctTreatmentsServiceImpl implements IRlctTreatmentsService 
{
    @Autowired
    private RlctTreatmentsMapper rlctTreatmentsMapper;

    /**
     * 查询项目
     * 
     * @param treatmentId 项目主键
     * @return 项目
     */
    @Override
    public RlctTreatments selectRlctTreatmentsByTreatmentId(Long treatmentId)
    {
        return rlctTreatmentsMapper.selectRlctTreatmentsByTreatmentId(treatmentId);
    }

    /**
     * 查询项目列表
     * 
     * @param rlctTreatments 项目
     * @return 项目
     */
    @Override
    public List<RlctTreatments> selectRlctTreatmentsList(RlctTreatments rlctTreatments)
    {
        return rlctTreatmentsMapper.selectRlctTreatmentsList(rlctTreatments);
    }

    /**
     * 新增项目
     * 
     * @param rlctTreatments 项目
     * @return 结果
     */
    @Override
    public int insertRlctTreatments(RlctTreatments rlctTreatments)
    {
        rlctTreatments.setCreateTime(DateUtils.getNowDate());
        return rlctTreatmentsMapper.insertRlctTreatments(rlctTreatments);
    }

    /**
     * 修改项目
     * 
     * @param rlctTreatments 项目
     * @return 结果
     */
    @Override
    public int updateRlctTreatments(RlctTreatments rlctTreatments)
    {
        rlctTreatments.setUpdateTime(DateUtils.getNowDate());
        return rlctTreatmentsMapper.updateRlctTreatments(rlctTreatments);
    }

    /**
     * 批量删除项目
     * 
     * @param treatmentIds 需要删除的项目主键
     * @return 结果
     */
    @Override
    public int deleteRlctTreatmentsByTreatmentIds(Long[] treatmentIds)
    {
        return rlctTreatmentsMapper.deleteRlctTreatmentsByTreatmentIds(treatmentIds);
    }

    /**
     * 删除项目信息
     * 
     * @param treatmentId 项目主键
     * @return 结果
     */
    @Override
    public int deleteRlctTreatmentsByTreatmentId(Long treatmentId)
    {
        return rlctTreatmentsMapper.deleteRlctTreatmentsByTreatmentId(treatmentId);
    }
}
