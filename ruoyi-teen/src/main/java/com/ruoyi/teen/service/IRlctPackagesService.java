package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctPackages;

/**
 * 套餐Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctPackagesService 
{
    /**
     * 查询套餐
     * 
     * @param packageId 套餐主键
     * @return 套餐
     */
    public RlctPackages selectRlctPackagesByPackageId(Long packageId);

    /**
     * 查询套餐列表
     * 
     * @param rlctPackages 套餐
     * @return 套餐集合
     */
    public List<RlctPackages> selectRlctPackagesList(RlctPackages rlctPackages);

    /**
     * 新增套餐
     * 
     * @param rlctPackages 套餐
     * @return 结果
     */
    public int insertRlctPackages(RlctPackages rlctPackages);

    /**
     * 修改套餐
     * 
     * @param rlctPackages 套餐
     * @return 结果
     */
    public int updateRlctPackages(RlctPackages rlctPackages);

    /**
     * 批量删除套餐
     * 
     * @param packageIds 需要删除的套餐主键集合
     * @return 结果
     */
    public int deleteRlctPackagesByPackageIds(Long[] packageIds);

    /**
     * 删除套餐信息
     * 
     * @param packageId 套餐主键
     * @return 结果
     */
    public int deleteRlctPackagesByPackageId(Long packageId);
}
