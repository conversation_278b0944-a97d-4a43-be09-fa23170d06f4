package com.ruoyi.teen.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctProductMapper;
import com.ruoyi.teen.domain.RlctProduct;
import com.ruoyi.teen.service.IRlctProductService;

/**
 * 产品广告Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctProductServiceImpl implements IRlctProductService 
{
    @Autowired
    private RlctProductMapper rlctProductMapper;

    /**
     * 查询产品广告
     * 
     * @param productId 产品广告主键
     * @return 产品广告
     */
    @Override
    public RlctProduct selectRlctProductByProductId(Long productId)
    {
        return rlctProductMapper.selectRlctProductByProductId(productId);
    }

    /**
     * 查询产品广告列表
     * 
     * @param rlctProduct 产品广告
     * @return 产品广告
     */
    @Override
    public List<RlctProduct> selectRlctProductList(RlctProduct rlctProduct)
    {
        return rlctProductMapper.selectRlctProductList(rlctProduct);
    }

    /**
     * 新增产品广告
     * 
     * @param rlctProduct 产品广告
     * @return 结果
     */
    @Override
    public int insertRlctProduct(RlctProduct rlctProduct)
    {
        rlctProduct.setCreateTime(DateUtils.getNowDate());
        return rlctProductMapper.insertRlctProduct(rlctProduct);
    }

    /**
     * 修改产品广告
     * 
     * @param rlctProduct 产品广告
     * @return 结果
     */
    @Override
    public int updateRlctProduct(RlctProduct rlctProduct)
    {
        rlctProduct.setUpdateTime(DateUtils.getNowDate());
        return rlctProductMapper.updateRlctProduct(rlctProduct);
    }

    /**
     * 批量删除产品广告
     * 
     * @param productIds 需要删除的产品广告主键
     * @return 结果
     */
    @Override
    public int deleteRlctProductByProductIds(Long[] productIds)
    {
        return rlctProductMapper.deleteRlctProductByProductIds(productIds);
    }

    /**
     * 删除产品广告信息
     * 
     * @param productId 产品广告主键
     * @return 结果
     */
    @Override
    public int deleteRlctProductByProductId(Long productId)
    {
        return rlctProductMapper.deleteRlctProductByProductId(productId);
    }
}
