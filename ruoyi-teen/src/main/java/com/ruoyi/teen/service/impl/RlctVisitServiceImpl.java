package com.ruoyi.teen.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.teen.domain.*;
import com.ruoyi.teen.mapper.*;
import com.ruoyi.teen.vo.ipad.*;
import com.ruoyi.teen.vo.RlctVisitVo;
import com.ruoyi.teen.vo.physician.TreatmentImageListBean;
import com.ruoyi.teen.vo.physician.TreatmentImageVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.service.IRlctVisitService;
import org.springframework.util.CollectionUtils;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 就诊记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctVisitServiceImpl implements IRlctVisitService {

    @Autowired
    private RlctVisitMapper rlctVisitMapper;
    @Autowired
    private RlctVisitHistoryMapper rlctVisitHistoryMapper;

    @Autowired
    private RlctConsumptionMapper rlctConsumptionMapper;

    @Autowired
    private RlctParentTeenMapper rlctParentTeenMapper;

    @Autowired
    private RlctUserPackageMapper rlctUserPackageMapper;

    @Autowired
    private RlctVisitTwoMapper rlctVisitTwoMapper;

    @Autowired
    private RlctTeenTwoMapper rlctTeenTwoMapper;

    @Autowired
    private DailyHabitRecordsMapper dailyHabitRecordsMapper;


    private static final Logger logger = LoggerFactory.getLogger(RlctVisitServiceImpl.class);

    /**
     * 查询就诊记录
     *
     * @param visitId 就诊记录主键
     * @return 就诊记录
     */
    @Override
    public RlctVisit selectRlctVisitByVisitId(Long visitId) {
        return rlctVisitMapper.selectRlctVisitByVisitId(visitId);
    }

    /**
     * 查询就诊记录列表
     *
     * @param rlctVisit 就诊记录
     * @return 就诊记录
     */
    @Override
    public List<RlctVisitVo> selectRlctVisitList(RlctVisitVo rlctVisit) {
        return rlctVisitMapper.selectRlctVisitList(rlctVisit);
    }

//    /**
//     * 平板端下单新增就诊记录
//     *
//     * @param rlctVisitVo
//     * @return
//     */
//    @Override
//    public int insertIpadVisit(VisitVo rlctVisitVo) {
//        rlctVisitVo.setStatus(0L);
//        rlctVisitVo.setCreateTime(DateUtils.getNowDate());
//        return rlctVisitMapper.insertIpadVisit(rlctVisitVo);
//    }

    /**
     * 平板端完成治疗更新就诊记录、插入就诊历史记录
     *
     * @param rlctVisitVo
     * @return
     */
//    @Override
//    public int upIpadVist(VisitVo rlctVisitVo) {
//        try {
//            rlctVisitVo.setStatus(1L);
//            rlctVisitVo.setUpdateTime(DateUtils.getNowDate());
//            rlctVisitMapper.upIpadVist(rlctVisitVo);
//            /**插入就诊历史记录*/
//            rlctVisitHistoryService.finishVist(rlctVisitVo.getVisitId());
//        }catch (Exception e){
//            return -1;
//        }
//        return 1;
//    }

//    /**
//     * 新增就诊记录
//     *
//     * @param rlctVisit 就诊记录
//     * @return 结果
//     */
//    @Override
//    public int insertRlctVisit(RlctVisit rlctVisit)
//    {
//        rlctVisit.setCreateTime(DateUtils.getNowDate());
//        return rlctVisitMapper.insertRlctVisit(rlctVisit);
//    }

    /**
     * 修改就诊记录
     *
     * @param rlctVisit 就诊记录
     * @return 结果
     */
    @Override
    public int updateRlctVisit(RlctVisit rlctVisit) {
        //就诊记录历史
        Date curDate = new Date();
        RlctVisit resRlctVisit = rlctVisitMapper.selectRlctVisitByVisitId(rlctVisit.getVisitId());
        RlctVisitHistory his = new RlctVisitHistory();
        his.setVisitId(rlctVisit.getVisitId());
        his.setBeforeTreatmentImageUrl(resRlctVisit.getBeforeTreatmentImageUrl());
        his.setAfterTreatmentImageUrl(resRlctVisit.getAfterTreatmentImageUrl());
        his.setVisitBeforeUpdateImageUrl(rlctVisit.getBeforeTreatmentImageUrl());
        his.setVisitAfterUpdateImageUrl(rlctVisit.getAfterTreatmentImageUrl());
        his.setUpdateBy(SecurityUtils.getUsername());
        his.setUpdateTime(curDate);

        //就诊记录
        rlctVisit.setUpdateTime(curDate);
        int res = rlctVisitMapper.updateRlctVisit(rlctVisit);
        rlctVisitHistoryMapper.insertRlctVisitHistory(his);
        return res;
    }

    /**
     * 批量删除就诊记录
     *
     * @param visitIds 需要删除的就诊记录主键
     * @return 结果
     */
    @Override
    public int deleteRlctVisitByVisitIds(Long[] visitIds) {
        return rlctVisitMapper.deleteRlctVisitByVisitIds(visitIds);
    }

    /**
     * 删除就诊记录信息
     *
     * @param visitId 就诊记录主键
     * @return 结果
     */
    @Override
    public int deleteRlctVisitByVisitId(Long visitId) {
        return rlctVisitMapper.deleteRlctVisitByVisitId(visitId);
    }

    /**
     * 获取就诊记录列表
     *
     * @param rlctParentTeen 父母与孩子关系的实体
     * @return 结果
     */
    @Override
    public List<RlctVisitVo> getVisitList(Long teenId, Integer status) {
        List<RlctVisitVo> list = rlctVisitMapper.getVisitList(teenId, status);
        for (RlctVisitVo visitVo : list) {
            visitVo.setVisitDetails(visitVo.getVisitDetails() == null ? "" : visitVo.getVisitDetails());

            //就诊前照片
            if (StringUtils.isNotBlank(visitVo.getBeforeTreatmentImageUrl())) {
                String[] btUrls = visitVo.getBeforeTreatmentImageUrl().split(",");
                List<TreatmentImageVo> btList = new ArrayList<>();
                for (String btUrl : btUrls) {
                    TreatmentImageVo treatmentImageVo = new TreatmentImageVo();
                    treatmentImageVo.setPathFileName(btUrl);
                    treatmentImageVo.setName(btUrl.substring(btUrl.lastIndexOf("/") + 1));
                    btList.add(treatmentImageVo);
                }
                visitVo.setBeforeImages(btList);
            }

            //就诊后照片
            if (StringUtils.isNotBlank(visitVo.getAfterTreatmentImageUrl())) {
                String[] btUrls = visitVo.getAfterTreatmentImageUrl().split(",");
                List<TreatmentImageVo> btList = new ArrayList<>();
                for (String btUrl : btUrls) {
                    TreatmentImageVo treatmentImageVo = new TreatmentImageVo();
                    treatmentImageVo.setPathFileName(btUrl);
                    treatmentImageVo.setName(btUrl.substring(btUrl.lastIndexOf("/") + 1));
                    btList.add(treatmentImageVo);
                }
                visitVo.setAfterImages(btList);
            }
        }
        return list;
    }

    /**
     * 完成治疗
     *
     * @param rlctVisitVo 就诊实体
     * @return 完成一次治疗
     */
    @Override
    public int finishTreat(Long visitId) {

        RlctVisit rlctVisit = rlctVisitMapper.selectRlctVisitByVisitId(visitId);

        Date date = new Date();

        // 完成治疗，状态设置为1
        rlctVisit.setStatus(1L);
        rlctVisit.setHealTime(date);
        LoginUser loginUser = getLoginUser();
        rlctVisit.setUserId(loginUser.getUser().getUserId());
        rlctVisitMapper.updateRlctVisit(rlctVisit);

        // 消费记录插入一条
        RlctParentTeen rlctParentTeen = rlctParentTeenMapper.selectRlctParentTeenById(rlctVisit.getParentTeenId());
        insertConsumptionRecord(rlctVisit, date, rlctParentTeen);

        // 套餐使用次数+1
        rlctUserPackageMapper.finishUserPackage(rlctVisit.getUserPackageId());
        return 1;
    }

    private void insertConsumptionRecord(RlctVisit rlctVisit, Date date, RlctParentTeen rlctParentTeen) {
        RlctConsumption rlctConsumption = new RlctConsumption();
        rlctConsumption.setUserId(Long.valueOf(rlctVisit.getDeductParentId()));
        rlctConsumption.setUserPackageId(rlctVisit.getUserPackageId());
        rlctConsumption.setDockerId(rlctVisit.getUserId());
        rlctConsumption.setConsumeTime(date);
        rlctConsumption.setTeenId(rlctParentTeen.getTeenId());

        rlctConsumptionMapper.insertRlctConsumption(rlctConsumption);
    }

    public void updateBeforeTreatmentImageUrl(TreatmentImageListBean images) {
        RlctVisit rlctVisit = new RlctVisit();
        rlctVisit.setVisitId(images.getVisitId());
        rlctVisit.setBeforeTreatmentImageUrl(images.toImages());
        rlctVisitMapper.updateBeforeTreatmentImageUrl(rlctVisit);
    }

    public void updateAfterTreatmentImageUrl(TreatmentImageListBean images) {
        RlctVisit rlctVisit = new RlctVisit();
        rlctVisit.setVisitId(images.getVisitId());
        rlctVisit.setAfterTreatmentImageUrl(images.toImages());
        rlctVisitMapper.updateAfterTreatmentImageUrl(rlctVisit);
    }


    /*提交status 2L 暂存行为           status 0L 治疗中    1L 分配套餐*/
    @Override
    public AjaxResult insertRlctSubmitStoreRecord(SubmitRecordVo submitRecordVo, Long status) {
        if (status == null || (status!=0L && status!=2L && status!=1L)){
            return AjaxResult.error("参数错误");
        }

        /*公共部分*/
        Long parentTeenId = submitRecordVo.getParentTeenId();
        // 2. 查询家长ID
        Long parentId = rlctParentTeenMapper.selectByparentTeenId(parentTeenId);
        if (parentId == null) {
            logger.warn("家长ID不存在OR被删除OR状态不正常，parentTeenId: {}", parentTeenId);  // 新增日志记录
            return AjaxResult.error("家长ID不存在OR被删除OR状态不正常");
        }
        RlctThisTimeVisitVo visits = submitRecordVo.getRlctThisTimeVisitTwo();
        DailyHabitRecords records = submitRecordVo.getDailyHabitRecords();
        LoginUser loginUser = getLoginUser();
        String userId = String.valueOf(loginUser.getUser().getUserId());  //  从缓存得到 当前用户ID

        Date nowDate = DateUtils.getNowDate();
        RlctVisit twoRlctVisit = rlctVisitTwoMapper.selectRlctVisitByStatus(parentTeenId, 2L); // 查暂存行为
        RlctVisit oneRlctVisit = rlctVisitTwoMapper.selectRlctVisitByStatus(parentTeenId, 0L); // 查治疗中
        Long userPackageId = submitRecordVo.getRlctThisTimeVisitTwo().getUserPackageId();  //消费次数

        if ( status == 1){  //1L 分配套餐

            RlctUserPackage rlctUserPackage = checkAnomaly(userPackageId,userId,parentId,parentTeenId);        // 分配套餐提交就诊校验方法
            Long packageId = rlctUserPackage.getPackageId();  //套餐id
            /*查询是否有剩余次数的套餐{取第一条套餐id}从而载入状态*/
            // 更新就诊记录就进行冻结次数+1
            rlctUserPackageMapper.upIpadUserPackage(userPackageId);
            // 更新用户与套餐关系表 谁进行扣次数
            RlctUserPackage data = new RlctUserPackage();
            data.setId(userPackageId);
            data.setUpdateBy(userId);
            data.setUpdateTime(DateUtils.getNowDate());
            rlctUserPackageMapper.updateRlctUserPackage(data);
            if (twoRlctVisit != null){
                /* 以下是更新 */
                // 更新本次就诊记录
                rlctVisits(twoRlctVisit.getVisitId(), submitRecordVo,parentId,packageId,userPackageId); // 更新本次就诊记录
                logger.debug("rlctVisits更新本次就诊记录成功");

                // 暂存行为绝对不可能会存在产品数据
                if (submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName() != null){
                    // 新增产品表
                    if (submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName() != ""){
                        insertProductUse(submitRecordVo, userId,twoRlctVisit.getVisitId());
                        logger.debug("insertProductUse新增产品表成功");
                    }
                }

                // 载入基本信息日常行为表
                DailyHabitRecords recordsDTO  = dailyHabitRecords(records);
                // 根据就诊id搜索日常行为主键
                Long habitId = dailyHabitRecordsMapper.selectDailyHabitRecordsHabitIdByVisitId(twoRlctVisit.getVisitId());
                recordsDTO.setHabitId(habitId);
                recordsDTO.setParentTeenId(parentTeenId);
                recordsDTO.setUpdateBy(userId);
                recordsDTO.setUpdateTime(DateUtils.getNowDate());
                dailyHabitRecordsMapper.updateDailyHabitRecordsByhabitId(recordsDTO);
                logger.debug("habitId={}更新日常行为成功",habitId);
                return AjaxResult.success("消费套餐次数 And 暂存行为转治疗工作 成功");
            }else{
                /* 以下是新增本次就诊记录*/
                Long visitId = createNewVisitRecord(submitRecordVo,userPackageId,parentId.toString(),packageId); //新增就诊记录方法 返回就诊id
                logger.debug("visitId={}新增本次就诊记录成功",visitId);
                List<TreatmentVo> treatments = submitRecordVo.getRlctThisTimeVisitTwo().getTreatments();
                submitTreatmentWork(treatments,visitId);  // 新增治疗工作
                logger.debug("userId={}新增治疗工作成功",userId);
                // 新增产品表
                if (submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName() != null ){
                    if (submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName() != ""){
                        insertProductUse(submitRecordVo, userId,visitId);
                        logger.debug("insertProductUse新增产品表成功");
                    }
                }
                // 载入基本信息日常行为表
                DailyHabitRecords recordsDTO  = dailyHabitRecords(records);
                recordsDTO.setVisitId(visitId);
                recordsDTO.setParentTeenId(parentTeenId);
                recordsDTO.setCreateBy(userId);
                recordsDTO.setCreateTime(DateUtils.getNowDate());
                dailyHabitRecordsMapper.insertDailyHabitRecords(recordsDTO);
                logger.debug("insertDailyHabitRecords新增日常行为表成功");
                logger.info("userId={}分配套餐提交就诊记录成功",userId);
                return AjaxResult.success("消费套餐次数 And 新增分配治疗工作 成功");
            }

        }


        if (status == 2){
            if (twoRlctVisit != null){
                // 载入基本信息日常行为表
                DailyHabitRecords recordsDTO  = dailyHabitRecords(records);
                // 根据就诊id搜索日常行为主键
                Long habitId = dailyHabitRecordsMapper.selectDailyHabitRecordsHabitIdByVisitId(twoRlctVisit.getVisitId());
                recordsDTO.setHabitId(habitId);
                recordsDTO.setParentTeenId(parentTeenId);
                recordsDTO.setUpdateBy(userId);
                recordsDTO.setUpdateTime(DateUtils.getNowDate());
                dailyHabitRecordsMapper.updateDailyHabitRecordsByhabitId(recordsDTO);  // 更新日常行为表
                return AjaxResult.success("日常行为表更新成功");
            }else if (twoRlctVisit == null){
                // 新增就诊记录(只创建空白单) 取就诊id  暂存行为的 不需要新增导购员
                RlctVisitVTwoDTO rlctVisit = new RlctVisitVTwoDTO();
                rlctVisit.setParentTeenId(parentTeenId);
                rlctVisit.setStatus(2L);
                rlctVisit.setDeptId(SecurityUtils.getDeptId());
                rlctVisit.setAdmissionTime(nowDate); // 接诊时间是
                rlctVisit.setCreateBy(userId);
                rlctVisit.setCreateTime(nowDate);
                rlctVisitMapper.insertRlctVisitVTwo(rlctVisit); // 返回就诊id
                Long visitId = rlctVisit.getVisitId();
                // 载入基本信息日常行为表
                DailyHabitRecords recordsDTO  = dailyHabitRecords(records);
                recordsDTO.setVisitId(visitId);
                recordsDTO.setParentTeenId(parentTeenId);
                recordsDTO.setCreateBy(userId);
                recordsDTO.setCreateTime(DateUtils.getNowDate());
                dailyHabitRecordsMapper.insertDailyHabitRecords(recordsDTO); // 新增日常行为表
                return AjaxResult.success("日常行为表新增成功");
           }
        }

        if (status == 0){
            if (oneRlctVisit != null){
                Long visitId = oneRlctVisit.getVisitId();  // 治疗中就诊id
                // 载入基本信息本次就诊记录
                RlctVisitVTwoDTO visitDTO  = RlctVisitVTwo(visits);
                visitDTO.setVisitId(visitId);
                visitDTO.setUpdateBy(userId);
                visitDTO.setUpdateTime(DateUtils.getNowDate());
                rlctVisitMapper.updateRlctVisit(visitDTO);                // 更新本次就诊记录
                logger.debug("userId={}更新本次就诊记录成功",userId);
                // 处理提交的治疗项目
                List<TreatmentVo> treatments = submitRecordVo.getRlctThisTimeVisitTwo().getTreatments();
                Long doctorId = getLoginUser().getUser().getUserId();
                updateTherapySubmission(treatments, visitId, doctorId);//更新提交治疗工作的方法

                // 1、查找该就诊id的产品方案、如果有根据id更新产品方案关联表关联，如果没有则新增关联表记录
                RlctProductUse rlctProductUse = rlctVisitTwoMapper.selectProductUseByVisitId(visitId);


                if (rlctProductUse != null) {
                    if (submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName() != null  ){
                        if (submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName() != ""){
                            RlctProductUse rlctProductUseDTO = new RlctProductUse();
                            rlctProductUseDTO.setUserProductId(rlctProductUse.getUserProductId());
                            rlctProductUseDTO.setVisitId(visitId);
                            rlctProductUseDTO.setProductUseName(submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName());
                            rlctProductUseDTO.setParentTeenId(parentTeenId);
                            rlctProductUseDTO.setDoctorId(doctorId);
                            rlctProductUseDTO.setUpdateBy(String.valueOf(doctorId));
                            rlctProductUseDTO.setUpdateTime(DateUtils.getNowDate());
                            rlctVisitMapper.updateProductUseByuserProductId(rlctProductUseDTO);
                            logger.debug("更新产品使用记录成功");
                        }
                    }
                } else {
                    // 新增产品表
                    if (submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName() != null ){
                        if (submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName() != ""){
                            insertProductUse(submitRecordVo, userId,visitId);
                            logger.debug("insertProductUse新增产品表成功");
                        }
                    }
                }

                //  . 获取数据库中现有的治疗记录 （当前医师）
                List<RlctDoctorVisic> dbRecords = rlctVisitMapper.selectByVisitAndDoctor(visitId, doctorId);
                if (CollectionUtils.isEmpty(dbRecords)) {
                    logger.info("就诊ID {} 下没有找到医生的治疗记录", visitId);
                    throw new RuntimeException("没有找到该用户的治疗记录");
                }
                List<Long> idsToDelete = new ArrayList<>(); // 伪删除记录列表
                // . 检查数据库中的记录是否在前端提供的数据中
                for (RlctDoctorVisic dbRecord : dbRecords) {
                    boolean foundInFrontend = false;

                    // 检查记录是否在前端提供的ID列表或类型列表中
                    for (TreatmentVo treatment : treatments) {
                        if (dbRecord.getTreatmentType().equals(treatment.getDictTreatmentType())) {
                            foundInFrontend = true;
                            break; // 过滤已存在的记录
                        }
                    }

                    if (!foundInFrontend) {
                        idsToDelete.add(dbRecord.getDoctorVisicId());
                    }
                }
                // . 执行批量操作
                if (!idsToDelete.isEmpty()) {
                    int deleted = rlctVisitMapper.batchSoftDelete(idsToDelete, doctorId, DateUtils.getNowDate());
                    logger.info("已伪删除{}条治疗记录", deleted);
                }


                logger.debug("userId={}更新提交治疗工作成功",userId);
                // 载入基本信息日常行为表
                DailyHabitRecords recordsDTO  = dailyHabitRecords(records);
                // 根据就诊id搜索日常行为主键
                Long habitId = dailyHabitRecordsMapper.selectDailyHabitRecordsHabitIdByVisitId(visitId);
                recordsDTO.setHabitId(habitId);
                recordsDTO.setParentTeenId(parentTeenId);
                recordsDTO.setUpdateBy(userId);
                recordsDTO.setUpdateTime(DateUtils.getNowDate());
                dailyHabitRecordsMapper.updateDailyHabitRecordsByhabitId(recordsDTO);
                logger.debug("userId={}更新日常行为表成功",userId);
            }else {
                logger.error("治疗中状态没有存在,提交人={}",userId);
                return AjaxResult.error("没有存在治疗中");
            }
            return AjaxResult.success("医师提交成功");
        }


        return AjaxResult.error("什么都没操作");
    }

    // 分配套餐提交就诊校验方法
    private RlctUserPackage checkAnomaly(Long userPackageId, String userId, Long parentId, Long parentTeenId) {
        RlctUserPackage rlctUserPackage = rlctUserPackageMapper.selectRlctUserPackageById(userPackageId); // 这里查询是一期的代码            // 根据前端提供的用户与套餐关系id，再去进行校验是否跟他传来的家长id一致 否则无法进行提交 顺便 获取套餐id

        //  通过前端提供的套餐关系id再次去校验是否有次数 通过套餐id查询该套餐是否有套餐次数与家长id是否一直
        RlctUserPackageVTwo userPackageData =  rlctUserPackageMapper.selectCheckRemainingTimes(userPackageId);
        if (rlctUserPackage == null){
            logger.error("用户与套餐关系id不存在,提交人={}",userId);
            throw new RuntimeException("用户与套餐关系id不存在");
        }
        if (!rlctUserPackage.getUserId().equals(parentId)){
            logger.error("用户与套餐关系id不一致,提交人={}",userId);
            throw new RuntimeException("用户与套餐关系id不一致");
        }
        if (userPackageData == null){
            logger.error("用户与套餐关系id不存在,提交人={}",userId);
        }
        Long remainingTimes = userPackageData.getRemainingTimes();
        if (remainingTimes <= 0 || remainingTimes == null){
            if (userPackageData.getUserId() != parentId){
                logger.warn("套餐关系id= {} 与套餐家长id不对应，parentId={}",userPackageId ,parentId);
                throw new RuntimeException("套餐关系id与套餐家长id不对应，parentId");
            }
            logger.warn("套餐次数不足或状态异常，parentTeenId: {}", parentTeenId);
            throw new RuntimeException("套餐次数不足或状态异常");
        }
        return rlctUserPackage;
    }

    private RlctVisitVTwoDTO RlctVisitVTwo(RlctThisTimeVisitVo visits) {
        RlctVisitVTwoDTO result = new RlctVisitVTwoDTO();
        result.setVisitDetails(visits.getVisitDetails());
        result.setBeforeTreatmentImageUrl(visits.getBeforeTreatmentImageUrl());
        result.setAfterTreatmentImageUrl(visits.getAfterTreatmentImageUrl());
        result.setHeightAfterTreatment(visits.getHeightAfterTreatment());
        result.setWeightAfterTreatment(visits.getWeightAfterTreatment());
        result.setHeightBeforeTreatment(visits.getHeightBeforeTreatment());
        result.setWeightBeforeTreatment(visits.getWeightBeforeTreatment());
        return result;
    }

    private void rlctVisits(Long visitId , SubmitRecordVo submitRecordVo, Long parentId, Long packageId, Long userPackageId) {
        // 处理提交的治疗项目
        List<TreatmentVo> treatments = submitRecordVo.getRlctThisTimeVisitTwo().getTreatments();
        Long doctorId = getLoginUser().getUser().getUserId();

        submitTreatmentWork(treatments,visitId);

        // 修改本次就诊记录
        RlctVisitVTwoDTO result = new RlctVisitVTwoDTO();
        result.setVisitId(visitId);
        result.setDeductParentId(parentId.toString());
        result.setParentTeenId(submitRecordVo.getParentTeenId());
        result.setVisitDetails(submitRecordVo.getRlctThisTimeVisitTwo().getVisitDetails());
        result.setBeforeTreatmentImageUrl(submitRecordVo.getRlctThisTimeVisitTwo().getBeforeTreatmentImageUrl());
        result.setAfterTreatmentImageUrl(submitRecordVo.getRlctThisTimeVisitTwo().getAfterTreatmentImageUrl());
        result.setHeightAfterTreatment(submitRecordVo.getRlctThisTimeVisitTwo().getHeightAfterTreatment());
        result.setWeightAfterTreatment(submitRecordVo.getRlctThisTimeVisitTwo().getWeightAfterTreatment());
        result.setHeightBeforeTreatment(submitRecordVo.getRlctThisTimeVisitTwo().getHeightBeforeTreatment());
        result.setWeightBeforeTreatment(submitRecordVo.getRlctThisTimeVisitTwo().getWeightBeforeTreatment());


        result.setUserPackageId(userPackageId);
        result.setPackageId(packageId);
        result.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId()); // 当前新增的部门
        result.setSalesId(SecurityUtils.getLoginUser().getUser().getUserId()); // 当前引导人新增就诊的业绩
        result.setUpdateBy(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        result.setUpdateTime(DateUtils.getNowDate());
        result.setStatus(0L);
        rlctVisitMapper.updateRlctVisit(result);
    }

    private DailyHabitRecords dailyHabitRecords(DailyHabitRecords records) {
        DailyHabitRecords recordsDTO = new DailyHabitRecords();
        recordsDTO.setBedtime(records.getBedtime());
        recordsDTO.setSleepDuration(records.getSleepDuration());
        recordsDTO.setSleepOther(records.getSleepOther());
        recordsDTO.setExerciseFrequency(records.getExerciseFrequency());
        recordsDTO.setExerciseDuration(records.getExerciseDuration());
        recordsDTO.setExerciseOther(records.getExerciseOther());
        recordsDTO.setEatingHabits(records.getEatingHabits());
        recordsDTO.setEatingOther(records.getEatingOther());
        recordsDTO.setCalcium(records.getCalcium());
        recordsDTO.setVitaminA(records.getVitaminA());
        recordsDTO.setVitaminD(records.getVitaminD());
        recordsDTO.setMultivitamin(records.getMultivitamin());
        recordsDTO.setGaba(records.getGaba());
        recordsDTO.setNutritionOther(records.getNutritionOther());
        return records;
    }


    /*新增就诊记录*/
    private Long createNewVisitRecord(SubmitRecordVo submitRecordVo, Long userPackageId, String ParentId, Long packageId) {
        RlctThisTimeVisitVo rlctThisTimeVisitTwo = submitRecordVo.getRlctThisTimeVisitTwo();


            RlctVisitVTwoDTO rlctVisit = new RlctVisitVTwoDTO();
            rlctVisit.setVisitDetails(rlctThisTimeVisitTwo.getVisitDetails());

            rlctVisit.setParentTeenId(submitRecordVo.getParentTeenId());
            rlctVisit.setBeforeTreatmentImageUrl(rlctThisTimeVisitTwo.getBeforeTreatmentImageUrl());
            rlctVisit.setAfterTreatmentImageUrl(rlctThisTimeVisitTwo.getAfterTreatmentImageUrl());
            rlctVisit.setDeductParentId(ParentId);

            rlctVisit.setAdmissionTime(DateUtils.getNowDate()); //接诊时间
            rlctVisit.setHeightAfterTreatment(rlctThisTimeVisitTwo.getHeightAfterTreatment());
            rlctVisit.setHeightBeforeTreatment(rlctThisTimeVisitTwo.getHeightBeforeTreatment());
            rlctVisit.setWeightAfterTreatment(rlctThisTimeVisitTwo.getWeightAfterTreatment());
            rlctVisit.setWeightBeforeTreatment(rlctThisTimeVisitTwo.getWeightBeforeTreatment());
            rlctVisit.setStatus(0L);

            rlctVisit.setSalesId(SecurityUtils.getUserId());
            rlctVisit.setPackageId(packageId);
            rlctVisit.setUserPackageId(userPackageId);
            rlctVisit.setCreateTime(DateUtils.getNowDate());
            rlctVisit.setCreateBy(String.valueOf(getLoginUser().getUser().getUserId()));
            rlctVisit.setDeptId(SecurityUtils.getDeptId());
            rlctVisitMapper.insertRlctVisitVTwo(rlctVisit); // 返回就诊id
            return rlctVisit.getVisitId();
    }

    /*
    * 新增治疗工作
    * */
    private void submitTreatmentWork(List<TreatmentVo> treatments,Long visitId) {
        if (treatments == null){
            throw new IllegalArgumentException("治疗项目列表不能为空");
        }
        List<RlctDoctorVisic> rlctDoctorVisic = new ArrayList<>();  // 1. 初始化治疗工作列表
        for (TreatmentVo sourceVo : treatments) {   // 2. 遍历所有治疗类型
            // 3 创建新记录
            RlctDoctorVisic newRecord = new RlctDoctorVisic();
            newRecord.setVisitId(visitId);
            newRecord.setDoctorId(sourceVo.getDoctorId());
            newRecord.setTreatmentType(sourceVo.getDictTreatmentType());
            newRecord.setTreatmentTime(DateUtils.getNowDate());
            newRecord.setReviewStatus("0"); // 未审核
            newRecord.setDelFlag("0"); // 有效记录
            newRecord.setCreateBy(String.valueOf(SecurityUtils.getLoginUser().getUser().getUserId()));
            newRecord.setCreateTime(DateUtils.getNowDate());
            rlctDoctorVisic.add(newRecord);
            logger.info("新增医生{}的治疗类型{}记录", sourceVo.getDoctorId(), sourceVo.getDictTreatmentTypeName());
        }
        // 4 批量插入新记录
        if (!rlctDoctorVisic.isEmpty()) {  // 返回false 中至少存在一个元素
            int inserted = rlctVisitMapper.insertRlctDoctorVisic(rlctDoctorVisic);
            logger.info("成功新增{}条治疗工作记录", inserted);
        } else {
            logger.info("无需新增治疗工作记录");
        }
    }



    /*新增日常行为习惯*/
    private void insertDailyHabitRecords(DailyHabitRecords recordsDailyHabit, Long parentTeenId, String userId) {
        DailyHabitRecords recordsDTO = new DailyHabitRecords();
        recordsDTO.setBedtime(recordsDailyHabit.getBedtime());
        recordsDTO.setSleepDuration(recordsDailyHabit.getSleepDuration());
        recordsDTO.setSleepOther(recordsDailyHabit.getSleepOther());
        recordsDTO.setExerciseFrequency(recordsDailyHabit.getExerciseFrequency());
        recordsDTO.setExerciseDuration(recordsDailyHabit.getExerciseDuration());
        recordsDTO.setExerciseOther(recordsDailyHabit.getExerciseOther());
        recordsDTO.setEatingHabits(recordsDailyHabit.getEatingHabits());
        recordsDTO.setEatingOther(recordsDailyHabit.getEatingOther());
        recordsDTO.setCalcium(recordsDailyHabit.getCalcium());
        recordsDTO.setVitaminA(recordsDailyHabit.getVitaminA());
        recordsDTO.setVitaminD(recordsDailyHabit.getVitaminD());
        recordsDTO.setMultivitamin(recordsDailyHabit.getMultivitamin());
        recordsDTO.setGaba(recordsDailyHabit.getGaba());
        recordsDTO.setNutritionOther(recordsDailyHabit.getNutritionOther());

        recordsDTO.setVisitId(recordsDailyHabit.getVisitId());
        recordsDTO.setParentTeenId(parentTeenId);
        recordsDTO.setCreateTime(DateUtils.getNowDate());
        recordsDTO.setCreateBy(userId);
        dailyHabitRecordsMapper.insertDailyHabitRecords(recordsDTO);
    }

    /*
     * 新增产品方案
     * */
    private void insertProductUse(SubmitRecordVo submitRecordVo, String userId, Long visitId) {
        ProductUseVo productUseVo = new ProductUseVo();

        productUseVo.setVisitId(visitId);
        productUseVo.setProductUseName(submitRecordVo.getRlctThisTimeVisitTwo().getProductUseName());
        productUseVo.setParentTeenId(submitRecordVo.getParentTeenId());
        productUseVo.setDoctorId(SecurityUtils.getUserId());
        productUseVo.setCreateBy(String.valueOf(userId));
        productUseVo.setCreateTime(DateUtils.getNowDate());
        rlctVisitMapper.insertRLctProductUse(productUseVo);
    }

    /*
     *
     * 处理提交治疗工作
     * @param treatments 治疗项目列表（不能为空）
     * @param visitId 就诊ID
     * @param doctorId 医生ID
     * @throws IllegalArgumentException 当treatments为null时抛出
     */
    public void updateTherapySubmission(List<TreatmentVo> treatments, Long visitId, Long doctorId) {
        // 1. 参数校验
        if (treatments == null || treatments.size() == 0 ) {
            throw new IllegalArgumentException("治疗项目列表不能为空");
        }
        if (visitId == null || doctorId == null) {
            throw new IllegalArgumentException("就诊ID和医生ID不能为空");
        }

        Date now = DateUtils.getNowDate();


        List<RlctDoctorVisic> recordsToAdd = new ArrayList<>();  // 1. 初始化治疗工作列表
        for (TreatmentVo sourceVo : treatments) {   // 2. 遍历所有治疗类型
            String treatmentType = sourceVo.getDictTreatmentType();

            // 3. 检查是否已存在该医生的治疗记录
            RlctDoctorVisic data = rlctVisitTwoMapper.selectVisitIdByDoctorAndTypes(visitId, doctorId, treatmentType);

            if (data != null) {
                Long existingRecordId = data.getDoctorVisicId();
                logger.info("医生{}已存在治疗类型{}的记录，记录ID：{}", doctorId, treatmentType, existingRecordId);
                // 更新存在该医生的治疗记录的治疗时间
                RlctDoctorVisic toUpdate = new RlctDoctorVisic();
                toUpdate.setDoctorVisicId(existingRecordId);
                toUpdate.setTreatmentTime(now);
                rlctVisitMapper.updateRlctDoctorVisicByDoctorVisicId(toUpdate);
                continue; // 跳过已存在的记录
            }

//            // 4. 处理新治疗记录  在搜一遍数据库中是否有这个类型的记录
//            Long allocatedRecordId = rlctVisitTwoMapper.selectVisitIdByDoctorAndType(visitId, null, treatmentType);
//
//            // 5. 伪删除历史分配记录（如果存在）
//            if (allocatedRecordId != null) {
//                RlctDoctorVisic toDelete = new RlctDoctorVisic();
//                toDelete.setDoctorVisicId(allocatedRecordId);
//                toDelete.setDeleteBy(currentUserId);
//                toDelete.setDeleteTime(now);
//                toDelete.setDelFlag("2"); // 伪删除标记
//                rlctVisitMapper.updateRlctDoctorVisicByVisitId(toDelete);
//                logger.info("已伪删除历史分配记录ID：{}", allocatedRecordId);
//            }

            // 6. 创建新记录  可以将新的医生数据进行插入
            RlctDoctorVisic newRecord = new RlctDoctorVisic();
            newRecord.setDoctorId(doctorId);
            newRecord.setTreatmentType(treatmentType);
            newRecord.setTreatmentTime(now);
            newRecord.setReviewStatus("0"); // 未审核
            newRecord.setDelFlag("0"); // 有效记录
            newRecord.setCreateBy(String.valueOf(doctorId));
            newRecord.setCreateTime(now);
            newRecord.setVisitId(visitId);

            recordsToAdd.add(newRecord);
            logger.info("新增医生{}的治疗类型{}记录", doctorId, treatmentType);
        }



        // 7. 批量插入新记录
        if (!recordsToAdd.isEmpty()) { // rlctDoctorVisic中至少存在一个元素
            int inserted = rlctVisitMapper.insertRlctDoctorVisic(recordsToAdd);
            logger.info("成功新增{}条治疗工作记录", inserted);
        } else {
            logger.info("无需新增治疗工作记录");
        }



    }


    /*
     * 只开产品提交 用就诊记录的mapper进行增加
     * */
    @Override
    public int insertsubmitProduct(SubmitProductVo submitProductVo) {

        Long userId = getLoginUser().getUser().getUserId();
        RlctProductUse RlctProductUse = new RlctProductUse();
        RlctProductUse.setDoctorId(userId);
        RlctProductUse.setProductUseName(submitProductVo.getProductUseName());
        RlctProductUse.setParentTeenId(submitProductVo.getParentTeenId());
        RlctProductUse.setCreateBy(userId.toString());
        RlctProductUse.setCreateTime(DateUtils.getNowDate());
        return rlctVisitMapper.insertsubmitProduct(RlctProductUse);

    }
}
