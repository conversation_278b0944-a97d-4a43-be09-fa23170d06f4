package com.ruoyi.teen.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctVisitHistoryMapper;
import com.ruoyi.teen.domain.RlctVisitHistory;
import com.ruoyi.teen.service.IRlctVisitHistoryService;

/**
 * 就诊历史记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctVisitHistoryServiceImpl implements IRlctVisitHistoryService
{
    @Autowired
    private RlctVisitHistoryMapper rlctVisitHistoryMapper;

    /**
     * 查询就诊历史记录
     *
     * @param id 就诊历史记录主键
     * @return 就诊历史记录
     */
    @Override
    public RlctVisitHistory selectRlctVisitHistoryById(Long id)
    {
        return rlctVisitHistoryMapper.selectRlctVisitHistoryById(id);
    }

    /**
     * 查询就诊历史记录列表
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 就诊历史记录
     */
    @Override
    public List<RlctVisitHistory> selectRlctVisitHistoryList(RlctVisitHistory rlctVisitHistory)
    {
        return rlctVisitHistoryMapper.selectRlctVisitHistoryList(rlctVisitHistory);
    }

    /**
     * 新增就诊历史记录
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 结果
     */
    @Override
    public int insertRlctVisitHistory(RlctVisitHistory rlctVisitHistory)
    {
        return rlctVisitHistoryMapper.insertRlctVisitHistory(rlctVisitHistory);
    }
//    /**
//     * 完成治疗时插入就诊历史表
//     * @param visitId
//     * @return
//     */
//    @Override
//    public int finishVist(Long visitId) {
//        return rlctVisitHistoryMapper.finishVist(visitId);
//    }

    /**
     * 修改就诊历史记录
     *
     * @param rlctVisitHistory 就诊历史记录
     * @return 结果
     */
    @Override
    public int updateRlctVisitHistory(RlctVisitHistory rlctVisitHistory)
    {
        rlctVisitHistory.setUpdateTime(DateUtils.getNowDate());
        return rlctVisitHistoryMapper.updateRlctVisitHistory(rlctVisitHistory);
    }

    /**
     * 批量删除就诊历史记录
     *
     * @param ids 需要删除的就诊历史记录主键
     * @return 结果
     */
    @Override
    public int deleteRlctVisitHistoryByIds(Long[] ids)
    {
        return rlctVisitHistoryMapper.deleteRlctVisitHistoryByIds(ids);
    }

    /**
     * 删除就诊历史记录信息
     *
     * @param id 就诊历史记录主键
     * @return 结果
     */
    @Override
    public int deleteRlctVisitHistoryById(Long id)
    {
        return rlctVisitHistoryMapper.deleteRlctVisitHistoryById(id);
    }
}
