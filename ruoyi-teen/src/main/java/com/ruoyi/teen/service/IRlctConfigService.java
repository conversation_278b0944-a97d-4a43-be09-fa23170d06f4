package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctConfig;
import com.ruoyi.teen.domain.RlctVisit;
import com.ruoyi.teen.domain.config.AttributeListBean;
import com.ruoyi.teen.domain.config.Diagnose;
import com.ruoyi.teen.vo.ipad.VisitVo;
import com.ruoyi.teen.vo.ipad.configVo;

/**
 * 配置Service接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctConfigService
{
    /**
     * 查询配置
     *
     * @param id 配置主键
     * @return 配置
     */
    public RlctConfig selectRlctConfigById(Long id);

    /**
     * 查询配置列表
     *
     * @param rlctConfig 配置
     * @return 配置集合
     */
    public List<RlctConfig> selectRlctConfigList(RlctConfig rlctConfig);

    /**
     * 配置类列表的封装
     * @param rlctConfig 配置
     * @return
     */
    List<configVo> getList(RlctConfig rlctConfig);
    AttributeListBean listInfo();

    /**
     * 新增配置
     *
     * @param rlctConfig 配置
     * @return 结果
     */
    public int insertRlctConfig(RlctConfig rlctConfig);

    /**
     * 修改配置
     *
     * @param rlctConfig 配置
     * @return 结果
     */
    public int updateRlctConfig(RlctConfig rlctConfig);

    /**
     * 批量删除配置
     *
     * @param ids 需要删除的配置主键集合
     * @return 结果
     */
    public int deleteRlctConfigByIds(Long[] ids);

    /**
     * 删除配置信息
     *
     * @param id 配置主键
     * @return 结果
     */
    public int deleteRlctConfigById(Long id);

    /**
     * 平板端套餐下单及保存信息配置表
     * @return
     */
    public RlctVisit saveConfig(Diagnose diagnose);
    public Diagnose getLastConfig(Long teenId,Long patientId);

    /**
     * 保存配置，并下载诊疗单的pdf数据
     * @param diagnose
     */
    public String saveConfigAndPdf(Diagnose diagnose) throws Exception;
    public String downloadPdf(Long visitId) throws Exception;

}
