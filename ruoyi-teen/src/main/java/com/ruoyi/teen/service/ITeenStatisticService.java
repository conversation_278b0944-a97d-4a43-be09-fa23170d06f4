package com.ruoyi.teen.service;

import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.domain.statistic.EchartSeriesData;
import com.ruoyi.teen.vo.ipad.PadUserInfo;
import com.ruoyi.teen.vo.ipad.PadUserInfoVTwo;
import com.ruoyi.teen.vo.physician.RlctTeenVo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


public interface ITeenStatisticService
{

    public EchartSeriesData statisticVisitTimes(String tel);

    public EchartSeriesData statisticGroupUp(String tel);

    public EchartSeriesData statisticHeightWightByTeenId(Long teenId);

    public EchartSeriesData statisticVisitTimesByTeenId(Long teenId);
    public EchartSeriesData statisticRadarByTeenId(Long teenId);

    public EchartSeriesData statisticPrice();
    public PadUserInfo padUserInfo();

    PadUserInfoVTwo padUserInfoTwo();
}
