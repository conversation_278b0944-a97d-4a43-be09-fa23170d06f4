package com.ruoyi.teen.service.impl.PC;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.teen.service.pc.PCRlctPackagesVTWOService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctPackagesMapper;
import com.ruoyi.teen.domain.RlctPackages;
import com.ruoyi.teen.service.IRlctPackagesService;

/**
 * 套餐Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctPackagesVTWOServiceImpl implements PCRlctPackagesVTWOService
{
    @Autowired
    private RlctPackagesMapper rlctPackagesMapper;

    /**
     * 查询套餐
     *
     * @param packageId 套餐主键
     * @return 套餐
     */
    @Override
    public RlctPackages selectRlctPackagesByPackageId(Long packageId)
    {
        return rlctPackagesMapper.selectRlctPackagesByPackageId(packageId);
    }

    /**
     * 查询套餐列表
     *
     * @param rlctPackages 套餐
     * @return 套餐
     */
    @Override
    public List<RlctPackages> selectRlctPackagesList(RlctPackages rlctPackages)
    {
        return rlctPackagesMapper.selectRlctPackagesList(rlctPackages);
    }

    /**
     * 新增套餐
     *
     * @param rlctPackages 套餐
     * @return 结果
     */
    @Override
    public int insertRlctPackages(RlctPackages rlctPackages)
    {
        rlctPackages.setCreateTime(DateUtils.getNowDate());
        return rlctPackagesMapper.insertRlctPackages(rlctPackages);
    }

    /**
     * 修改套餐
     *
     * @param rlctPackages 套餐
     * @return 结果
     */
    @Override
    public int updateRlctPackages(RlctPackages rlctPackages)
    {
        rlctPackages.setUpdateTime(DateUtils.getNowDate());
        return rlctPackagesMapper.updateRlctPackages(rlctPackages);
    }

    /**
     * 批量删除套餐
     *
     * @param packageIds 需要删除的套餐主键
     * @return 结果
     */
    @Override
    public int deleteRlctPackagesByPackageIds(Long[] packageIds)
    {
        return rlctPackagesMapper.deleteRlctPackagesByPackageIds(packageIds);
    }

    /**
     * 删除套餐信息
     *
     * @param packageId 套餐主键
     * @return 结果
     */
    @Override
    public int deleteRlctPackagesByPackageId(Long packageId)
    {
        return rlctPackagesMapper.deleteRlctPackagesByPackageId(packageId);
    }
}
