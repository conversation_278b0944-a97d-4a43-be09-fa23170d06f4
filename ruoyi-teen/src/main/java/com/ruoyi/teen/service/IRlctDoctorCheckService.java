package com.ruoyi.teen.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.teen.vo.ipad.PadConfirmCheckVo;
import com.ruoyi.teen.vo.ipad.RlctDoctorVisicDVO;
import com.ruoyi.teen.vo.ipad.RlctDoctorVisicVo;

import java.util.List;

public interface IRlctDoctorCheckService {

    /**
     * 巡诊审核列表 0待审核 1审核通过
     */
    List<RlctDoctorVisicVo> doctornochecklist(Integer auditStatus);

    AjaxResult confirmcheck(PadConfirmCheckVo padConfirmCheckVo);

    AjaxResult checkstatus(RlctDoctorVisicDVO rlctDoctorVisicDvo);

    /*伪删除*/
    AjaxResult delete(RlctDoctorVisicVo rlctDoctorVisicVo);

}
