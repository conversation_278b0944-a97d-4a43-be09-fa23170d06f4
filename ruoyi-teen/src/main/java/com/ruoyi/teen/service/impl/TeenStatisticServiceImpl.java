package com.ruoyi.teen.service.impl;

import com.ruoyi.cache.utils.CacheDictUtils;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.domain.statistic.*;
import com.ruoyi.teen.mapper.RlctTeenMapper;
import com.ruoyi.teen.mapper.TeenStatisticMapper;
import com.ruoyi.teen.service.ITeenStatisticService;
import com.ruoyi.teen.utils.DateUtils;
import com.ruoyi.teen.vo.ipad.PadUserInfo;
import com.ruoyi.teen.vo.ipad.PadUserInfoVTwo;
import com.ruoyi.teen.vo.ipad.UserServiceRecordVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.apache.commons.lang3.time.DateUtils.isSameDay;

/**
 * 医馆信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class TeenStatisticServiceImpl implements ITeenStatisticService
{
    private static final Logger logger = LoggerFactory.getLogger(TeenStatisticServiceImpl.class);
    @Autowired
    private TeenStatisticMapper teenStatisticMapper;

    @Autowired
    private RlctTeenMapper rlctTeenMapper;

    @Autowired
    private ISysUserService userService;


    /**
     * //统计家长下各小孩的就诊次数,近10个月的就诊次数
     * @param tel
     */
    @Override
    public EchartSeriesData statisticVisitTimes(String tel){


        //legend
        List<String> legend = getTeenNames(tel);
        List<TeenStatisticBean> list = teenStatisticMapper.statisticVisitTimes(tel);

        //X轴名称显示
        List<String> xAxisData = getXAxisData(10);

        //数据
        List<SeriesData> seriesDataList = new ArrayList<>();
        for (String s : legend) {
            SeriesData<Integer> seriesData = new SeriesData<>();
            seriesData.setName(s);
            List<Integer>  yAxisData = new ArrayList<>();
            for (String xa : xAxisData) {
                yAxisData.add(getCountData(list,s,xa));
            }
            seriesData.setData(yAxisData);
            seriesDataList.add(seriesData);
        }

        EchartSeriesData resData = new EchartSeriesData();
        resData.setLegend(legend);
        resData.setxAxisData(xAxisData);
        resData.setSeriesDataList(seriesDataList);
        return resData;
    }

    private Integer getCountData(List<TeenStatisticBean> list,String name,String month){
        if(list == null || list.size() == 0){
            return 0;
        }
        for (TeenStatisticBean tsb : list) {
            if(StringUtils.isBlank(tsb.getMonth())){
                continue;
            }
            if(tsb.getName().equals(name) && tsb.getMonth().equals(month)){
                return tsb.getCount();
            }
        }
        return 0;
    }

    /**
     * 生成X轴的数据，前6个月(含当前月份)
     * @return
     */
    private List<String> getXAxisData(int monthNum){
        Calendar cal = Calendar.getInstance();
        List<String> res = new ArrayList<>();
        for(int i=0;i<monthNum;i++){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String date = sdf.format(cal.getTime());
            res.add(0,date);
            cal.add(Calendar.MONTH,-1);
        }
        return res;
    }

//    public static void main(String[] args) {
//        System.out.println(getCurMonthDay());
//    }


    private List<String> getTeenNames(String tel){
        List<RlctTeen> teenList = rlctTeenMapper.getListByTel(tel);
        if(teenList == null || teenList.size() == 0){
            return new ArrayList<>();
        }
        List<String> teenNames = new ArrayList<>();
        for (RlctTeen rlctTeen : teenList) {
            teenNames.add(rlctTeen.getTeenName());
        }
        return teenNames;
    }

    /**
     * //图表数据：成长曲线对比数据接口（多小孩）
     * @param tel
     */
    @Override
    public EchartSeriesData statisticGroupUp(String tel){
        //legend
        List<String> legend = getTeenNames(tel);
        List<TeenStatisticHeightBean> list = teenStatisticMapper.statisticGroupUp(tel);

        //X轴名称显示
        List<String> xAxisData = getXAxisData(10);

        //数据
        List<SeriesData> seriesDataList = new ArrayList<>();
        for (String s : legend) {
            SeriesData<BigDecimal> seriesData = new SeriesData<BigDecimal>();
            seriesData.setName(s);
            List<BigDecimal>  yAxisData = new ArrayList<>();
            for (String xa : xAxisData) {
                yAxisData.add(getHeightData(list,s,xa));
            }

            //填充0
            yAxisData = fillData(yAxisData);

            seriesData.setData(yAxisData);
            seriesDataList.add(seriesData);
        }

        EchartSeriesData resData = new EchartSeriesData();
        resData.setLegend(legend);
        resData.setxAxisData(xAxisData);
        resData.setSeriesDataList(seriesDataList);
        return resData;
    }


//    public static void main(String[] args) {
//        List<BigDecimal> list = new ArrayList<>();
//        list.add(new BigDecimal(0));
//        list.add(new BigDecimal(4));
//        list.add(new BigDecimal(5));
//        list.add(new BigDecimal(6));
//        list.add(new BigDecimal(0));
//        list.add(new BigDecimal(7));
//        list.add(new BigDecimal(7));
//
//        List<BigDecimal> list2 = fillData(list);
//        System.out.println(list2);
//    }

    /**
     * 将0或null填成最近的值
     * @param list
     * @return
     */
    private static List<BigDecimal> fillData(List<BigDecimal> list){
        if(list == null || list.size() == 0){
            return list;
        }

        //向下填充
        //比如：0,0,5,6,0,7,0，填充成0,0,5,6,6,7,7
        List<BigDecimal> res1List = new ArrayList<>();
        BigDecimal beforeV = BigDecimal.ZERO;
        for (BigDecimal bigDecimal : list) {
            if(bigDecimal == null){
                bigDecimal = BigDecimal.ZERO;
            }
            if(bigDecimal.compareTo(BigDecimal.ZERO) == 0 && beforeV.compareTo(BigDecimal.ZERO) == 0){
                res1List.add(bigDecimal);
                continue;
            }
            if(bigDecimal.compareTo(BigDecimal.ZERO) != 0){
                beforeV = bigDecimal;
                res1List.add(bigDecimal);
                continue;
            }
            if(bigDecimal.compareTo(BigDecimal.ZERO) == 0 && beforeV.compareTo(BigDecimal.ZERO) != 0){
                res1List.add(beforeV);
            }
        }


        //再向上填充
        //比如：0,0,5,6,6,7,7，填充成5,5,5,6,6,7,7
        List<BigDecimal> res2List = new ArrayList<>();
        beforeV = BigDecimal.ZERO;
        for (int i = res1List.size() - 1; i >= 0; i--) {
            BigDecimal bigDecimal = res1List.get(i);
            if(bigDecimal == null){
                bigDecimal = BigDecimal.ZERO;
            }
            if(bigDecimal.compareTo(BigDecimal.ZERO) == 0 && beforeV.compareTo(BigDecimal.ZERO) == 0){
                res2List.add(0,bigDecimal);
                continue;
            }
            if(bigDecimal.compareTo(BigDecimal.ZERO) != 0){
                beforeV = bigDecimal;
                res2List.add(0,bigDecimal);
                continue;
            }
            if(bigDecimal.compareTo(BigDecimal.ZERO) == 0 && beforeV.compareTo(BigDecimal.ZERO) != 0){
                res2List.add(0,beforeV);
            }
        }


        return res2List;
    }

    private BigDecimal getHeightData(List<TeenStatisticHeightBean> list,String name,String month){
        if(list == null || list.size() == 0){
            return BigDecimal.ZERO;
        }
        for (TeenStatisticHeightBean tsb : list) {
            if(StringUtils.isBlank(tsb.getMonth())){
                continue;
            }
            if(tsb.getName().equals(name) && tsb.getMonth().equals(month)){
                return tsb.getHeight();
            }
        }
        return BigDecimal.ZERO;
    }


    /**
     * //根据该手机号码下的孩子查询详情--图表数据：身高体重数据接口
     * @param teenId
     */
    @Override
    public EchartSeriesData statisticHeightWightByTeenId(Long teenId){
        //legend
        List<String> legend = new ArrayList<>();
        legend.add("身高(cm)");
        legend.add("体重(公斤)");
        List<HeightWightBean> list = teenStatisticMapper.statisticHeightWightByTeenId(teenId);
        Collections.reverse(list);//倒序
        //X轴名称显示
        List<String> xAxisData = new ArrayList<>();

        //数据
        List<SeriesData> seriesDataList = new ArrayList<>();

        SeriesData<BigDecimal> seriesDataHeight = new SeriesData<BigDecimal>();//身高
        seriesDataHeight.setName(legend.get(0));
        SeriesData<BigDecimal> seriesDataWeight = new SeriesData<BigDecimal>();//体重
        seriesDataWeight.setName(legend.get(1));

        for (HeightWightBean bean : list) {
            String xData = DateFormatUtils.format(bean.getCreateTime(), "yyyy-MM");
            xAxisData.add(xData);
            seriesDataHeight.putData(bean.getHeight());
            seriesDataWeight.putData(bean.getWeight());
        }
        seriesDataList.add(seriesDataHeight);
        seriesDataList.add(seriesDataWeight);

        EchartSeriesData resData = new EchartSeriesData();
        resData.setLegend(legend);
        resData.setxAxisData(xAxisData);
        resData.setSeriesDataList(seriesDataList);
        return resData;
    }

    /**
     * //根据该手机号码下的孩子查询详情--图表数据：每月就诊次数数据接口
     * @param teenId
     */
    @Override
    public EchartSeriesData statisticVisitTimesByTeenId(Long teenId){

        RlctTeen rlctTeen = rlctTeenMapper.selectRlctTeenByTeenId(teenId);
        if(rlctTeen == null){
            throw new RuntimeException("患者不存在");
        }


        //legend
        List<String> legend = new ArrayList<>();
        legend.add(rlctTeen.getTeenName());
        List<TeenStatisticBean> list = teenStatisticMapper.statisticVisitTimesByTeenId(teenId);
        java.util.Collections.reverse(list);// 将 List 倒序

        //X轴名称显示
        List<String> xAxisData = new ArrayList<>();

        //数据
        List<SeriesData> seriesDataList = new ArrayList<>();

        SeriesData<Integer> seriesData = new SeriesData<>();//身高
        seriesData.setName(legend.get(0));

        for (TeenStatisticBean bean : list) {
            xAxisData.add(bean.getMonth());
            seriesData.putData(bean.getCount());
        }
        seriesDataList.add(seriesData);

        EchartSeriesData resData = new EchartSeriesData();
        resData.setLegend(legend);
        resData.setxAxisData(xAxisData);
        resData.setSeriesDataList(seriesDataList);
        return resData;
    }

    /**
     * 根据该手机号码下的孩子查询详情--图表数据：健康管理五维图数据接口
     * @param teenId
     * @return
     */
    public EchartSeriesData statisticRadarByTeenId(Long teenId){
        //legend
        List<String> legend = new ArrayList<>();
        legend.add("健康管理五维图");

        //indicator
        List<RadarTitle> indicator = new ArrayList<>();
        indicator.add(new RadarTitle("睡眠起始时间",new BigDecimal("6")));
        indicator.add(new RadarTitle("睡眠时长",new BigDecimal("6")));
        indicator.add(new RadarTitle("运动量",new BigDecimal("6")));
        indicator.add(new RadarTitle("营业补充",new BigDecimal("6")));
        indicator.add(new RadarTitle("饮食习惯",new BigDecimal("6")));

        //数据
        RlctTeen rlctTeen = rlctTeenMapper.selectRlctTeenByTeenId(teenId);
        List<SeriesData> seriesDataList = new ArrayList<>();

        SeriesData<BigDecimal> seriesData = new SeriesData<>();//身高
        seriesData.setName(legend.get(0));
        seriesData.putData(rlctTeen.getTeenSleepTime());
        seriesData.putData(rlctTeen.getTeenSleepDurationTime());
        seriesData.putData(rlctTeen.getTeenExerciseHabit());
        seriesData.putData(rlctTeen.getTeenNutrient());
        seriesData.putData(rlctTeen.getTeenEatHabit());
        seriesDataList.add(seriesData);

        EchartSeriesData resData = new EchartSeriesData();
        resData.setLegend(legend);
        resData.setIndicator(indicator);
        resData.setSeriesDataList(seriesDataList);
        return resData;
    }


    /**
     * 生成当月的天数
     * @return
     */
    private List<String> getCurMonthDay(){
        List<String> list = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH,1);
        for(int i=0;i<daysInMonth;i++){
            SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
            list.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_MONTH,1);
        }
        return list;
    }


    private BigDecimal getPriceData(List<TeenStatisticPriceBean> list,String month){
        if(list == null || list.size() == 0){
            return BigDecimal.ZERO;
        }
        for (TeenStatisticPriceBean tsb : list) {
            if(StringUtils.isBlank(tsb.getMonth())){
                continue;
            }
            if(tsb.getMonth().equals(month)){
                return tsb.getPrice();
            }
        }
        return BigDecimal.ZERO;
    }

    @Override
    public EchartSeriesData statisticPrice(){
        //legend
        List<String> legend = new ArrayList<>();
        legend.add("本月充值记录");
        List<TeenStatisticPriceBean> list = teenStatisticMapper.statisticPrice();
        java.util.Collections.reverse(list);// 将 List 倒序

        //X轴名称显示
        List<String> xAxisData = getCurMonthDay();

        //数据
        List<SeriesData> seriesDataList = new ArrayList<>();

        SeriesData<BigDecimal> seriesData = new SeriesData<>();//身高
        seriesData.setName(legend.get(0));

        for (String xAxisDatum : xAxisData) {
            seriesData.putData(getPriceData(list,xAxisDatum));
        }

        seriesDataList.add(seriesData);
        EchartSeriesData resData = new EchartSeriesData();
        resData.setLegend(legend);
        resData.setxAxisData(xAxisData);
        resData.setSeriesDataList(seriesDataList);
        return resData;
    }


    @Override
    public PadUserInfo padUserInfo() {
        int totalService = teenStatisticMapper.countTotalService();//总服务人数
        int todayService = teenStatisticMapper.countTodayService();//今日服务人数
        int totalArchives = teenStatisticMapper.countTotalArchives();//总建档人数
        int todayArchives = teenStatisticMapper.countTodayArchives();//今日建档人数

        PadUserInfo info = new PadUserInfo();
        info.setTotalService(totalService);
        info.setTodayService(todayService);
        info.setTotalArchives(totalArchives);
        info.setTodayArchives(todayArchives);

        SysUser sysUser = userService.selectUserById(SecurityUtils.getLoginUser().getUserId());
        info.setUserId(sysUser.getUserId());
        info.setNickName(sysUser.getNickName());
        info.setPhonenumber(sysUser.getPhonenumber());
        info.setSex(CacheDictUtils.toDictLabel("sys_user_sex", sysUser.getSex()));
//        info.setAge(DateUtils.getCurrentAge(sysUser.getBirthday()));
        return info;
    }

    @Override
    public PadUserInfoVTwo padUserInfoTwo() {

        Long doctorId = SecurityUtils.getUserId();
        if (doctorId == null){
            logger.warn("doctorId is null");
            throw new RuntimeException("doctorId is null");
        }
        // 根据医生id查询总充值金额
        Long totalPrice = teenStatisticMapper.sumPackagePrice(doctorId,null,null);
        String date = DateUtils.getDate();
        LocalDateTime startTime = LocalDate.now().atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1);
        // 根据医生id查询今日充值金额
        Long todayPrice = teenStatisticMapper.sumPackagePrice(doctorId,startTime ,endTime);

        // 根据医生id查询总建档人数（孩子数）
        Integer totalArchives = teenStatisticMapper.countTotalArchivesByDoctorId(doctorId,null,null);
        // 根据医生id查询今日建档人数
        Integer todayArchives = teenStatisticMapper.countTotalArchivesByDoctorId(doctorId,startTime ,endTime);


        // 本月建档数服务记录/本月充值金额服务记录 封装方法
        List<UserServiceRecordVo> userServicePriceRecordList = getUserServicePriceRecordList(doctorId);

        PadUserInfoVTwo info = new PadUserInfoVTwo();
        info.setTotalPrice(totalPrice);
        info.setTodayPrice(todayPrice);
        info.setTotalArchives(totalArchives);
        info.setTodayArchives(todayArchives);
        info.setUserServicePriceRecordList(userServicePriceRecordList);

        SysUser sysUser = userService.selectUserById(SecurityUtils.getLoginUser().getUserId());
        info.setAvatar(sysUser.getAvatar());
        info.setUserId(sysUser.getUserId());
        info.setNickName(sysUser.getNickName());
        info.setPhonenumber(sysUser.getPhonenumber());
        info.setSex(CacheDictUtils.toDictLabel("sys_user_sex", sysUser.getSex()));
//        info.setAge(DateUtils.getCurrentAge(sysUser.getBirthday()));
        return info;
    }

    private List<UserServiceRecordVo> getUserServicePriceRecordList(Long doctorId) {
        // 获取本月第一天
        String firstDayOfStartMonth = DateUtils.getMonthStart();
        // 获取当前时间+1天
        Date nowDate = DateUtils.getNowDate();
        Date tomorrow = DateUtils.addDays(nowDate, 1);
        logger.info("开始查询医生服务记录 | doctorId: {}, 时间范围: {} 至 {}",
                doctorId, firstDayOfStartMonth, tomorrow);
        // 2. 查询本月每日新增孩子数
        // 根据医生id查询总建档人数（孩子数）
        List<UserServiceRecordVo> dbData  = teenStatisticMapper.selectDailyServiceRecords(doctorId,firstDayOfStartMonth, tomorrow);

        // 根据医生id查询充值金额（业绩）
        List<UserServiceRecordVo> dbPriceData  = teenStatisticMapper.selectDailyServicePrice(doctorId,firstDayOfStartMonth, tomorrow);
        logger.debug("查询结果 | 孩子记录数: {}, 金额记录数: {}",
                dbData.size(), dbPriceData.size());
        // 3. 生成本月所有日期列表
        List<UserServiceRecordVo> allDays  = generateAllDaysInMonth(firstDayOfStartMonth, tomorrow);
        logger.debug("生成空白数据全量日期 | 天数: {}", allDays.size());
        // 4. 合并数据
        List<UserServiceRecordVo> result = mergeDataWithoutMap(dbData , allDays ,dbPriceData);
        return result;
    }

    /*
    * 合并每日数据（带日志记录）
    * */
    private List<UserServiceRecordVo> mergeDataWithoutMap(List<UserServiceRecordVo> dbData, List<UserServiceRecordVo> allDays, List<UserServiceRecordVo> dbPriceData) {

        logger.info("开始合并数据 | 基础天数: {}, 孩子记录数: {}, 金额记录数: {}",
                allDays.size(), dbData.size(), dbPriceData.size());
        List<UserServiceRecordVo> result = new ArrayList<>(allDays);

        // 遍历数据库结果，更新对应日期的数据 孩子数
        int matchedDays = 0;
        for (UserServiceRecordVo dbVo : dbData) {
            boolean found = false;
            for (UserServiceRecordVo dayVo : result) {
                if (DateUtils.isSameDay(dayVo.getTime(), dbVo.getTime())) { //工具自带比较日期
                    dayVo.setTotalArchives(dbVo.getTotalArchives());
                    matchedDays++;
                    logger.debug("匹配孩子数数据 | 日期: {}, 数量: {}", dayVo.getTime(), dbVo.getTotalArchives());
                    break;
                }
            }
            if (!found) {
                logger.warn("未找到匹配的日期记录 | 孩子数数据日期: {}", dbVo.getTime());
            }
        }

        // 遍历数据库结果，更新对应日期的数据金额数
        // 合并金额数据
        int matchedPrices = 0;
        for (UserServiceRecordVo dbVo : dbPriceData) {
            boolean found = false;
            for (UserServiceRecordVo dayVo : result) {
                if (DateUtils.isSameDay(dayVo.getTime(), dbVo.getTime())) { //工具自带比较日期
                    dayVo.setTotalPrice(dbVo.getTotalPrice());
                    matchedPrices++;
                    logger.debug("匹配金额数据 | 日期: {}, 金额: {}", dayVo.getTime(), dbVo.getTotalPrice());
                    break;
                }
            }
            if (!found) {
                logger.warn("未找到匹配的日期记录 | 金额数据日期: {}", dbVo.getTime());
            }
        }
        logger.info("合并完成 | 匹配天数: {}/{} (孩子数), {}/{} (金额)",
                matchedDays, dbData.size(), matchedPrices, dbPriceData.size());
        return result;
    }

    private List<UserServiceRecordVo> generateAllDaysInMonth(String startDateStr, Date endDate) {
        List<UserServiceRecordVo> result = new ArrayList<>();
        Date startDate = DateUtils.parseDate(startDateStr);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        // 遍历从本月第一天到明天的所有日期
        while (calendar.getTime().before(endDate)) {
            UserServiceRecordVo vo = new UserServiceRecordVo();
            vo.setTime(calendar.getTime());
            vo.setTotalArchives(0L); // 默认服务次数为0
            vo.setTotalPrice(BigDecimal.ZERO); // 默认总金额为0
            result.add(vo);
            calendar.add(Calendar.DATE, 1); // 增加一天
        }

        return result;
    }
}
