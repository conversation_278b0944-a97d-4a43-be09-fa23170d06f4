package com.ruoyi.teen.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.teen.domain.RlctParentTeen;
import com.ruoyi.teen.domain.RlctVisit;
import com.ruoyi.teen.vo.RlctVisitVo;
import com.ruoyi.teen.vo.ipad.SubmitProductVo;
import com.ruoyi.teen.vo.ipad.SubmitRecordVo;
import com.ruoyi.teen.vo.ipad.TreatmentVo;
import com.ruoyi.teen.vo.ipad.VisitVo;
import com.ruoyi.teen.vo.physician.TreatmentImageListBean;

/**
 * 就诊记录Service接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctVisitService
{
    /**
     * 查询就诊记录
     *
     * @param visitId 就诊记录主键
     * @return 就诊记录
     */
    public RlctVisit selectRlctVisitByVisitId(Long visitId);

    /**
     * 查询就诊记录列表
     *
     * @param rlctVisit 就诊记录
     * @return 就诊记录集合
     */
    public List<RlctVisitVo> selectRlctVisitList(RlctVisitVo rlctVisit);

    /**
     * 平板端下单新增就诊记录
     *
     * @param rlctVisitVo
     * @return
     */
//    public int insertIpadVisit(VisitVo rlctVisitVo);

    /**
     * 完成治疗更新就诊记录
     *
     * @param rlctVisitVo
     * @return
     */
//    public int upIpadVist(VisitVo rlctVisitVo);
    /**
     * 新增就诊记录
     *
     * @param rlctVisit 就诊记录
     * @return 结果
     */
//    public int insertRlctVisit(RlctVisit rlctVisit);

    /**
     * 修改就诊记录
     *
     * @param rlctVisit 就诊记录
     * @return 结果
     */
    public int updateRlctVisit(RlctVisit rlctVisit);

    /**
     * 批量删除就诊记录
     *
     * @param visitIds 需要删除的就诊记录主键集合
     * @return 结果
     */
    public int deleteRlctVisitByVisitIds(Long[] visitIds);

    /**
     * 删除就诊记录信息
     *
     * @param visitId 就诊记录主键
     * @return 结果
     */
    public int deleteRlctVisitByVisitId(Long visitId);

    /**
     * 获取就诊记录列表
     * @return 结果
     */
    List<RlctVisitVo> getVisitList(Long teenId,Integer status);

    /**
     * 完成治疗
     * @param rlctVisitVo 就诊实体
     * @return 结果
     */
    int finishTreat(Long visitId);

    public void updateBeforeTreatmentImageUrl(TreatmentImageListBean images);

    public void updateAfterTreatmentImageUrl(TreatmentImageListBean images);

    /*提交按钮*/
    public AjaxResult insertRlctSubmitStoreRecord(SubmitRecordVo submitRecordVo, Long status);

    /*只开产品*/
    int insertsubmitProduct(SubmitProductVo submitProductVo);

    void updateTherapySubmission(List<TreatmentVo> treatments, Long visitId, Long doctorId);
}
