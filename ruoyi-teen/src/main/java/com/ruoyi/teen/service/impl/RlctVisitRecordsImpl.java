package com.ruoyi.teen.service.impl;


import ch.qos.logback.classic.pattern.Util;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.teen.mapper.RlctVisitRecordsMapper;
import com.ruoyi.teen.service.IRlctVisitRecordsService;
import com.ruoyi.teen.utils.DateUtils;
import com.ruoyi.teen.vo.ipad.VisitRecordsVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class RlctVisitRecordsImpl implements IRlctVisitRecordsService {
    // 日志
    private static final Logger logger = LoggerFactory.getLogger(RlctVisitRecordsImpl.class);

    @Autowired
    private RlctVisitRecordsMapper rlctVisitRecordsMapper;
    @Override
    public List<VisitRecordsVo> selectRlctVisitRecordsList(Date startDate, Date endDate) {
        /*结束时间进行加一天，数据库查询需要*/
        Date enddate = DateUtils.addDays(endDate, 1);
        /*根据开始时间日期和结束时间日期搜索visitId*/
        List<Long> visitIdList = rlctVisitRecordsMapper.selectRlctVisitRecordsList(startDate,enddate);
        if (visitIdList == null || (visitIdList.size() ==0)){
            logger.error("selectRlctVisitRecordsList-UserId={}没有搜索到数据", SecurityUtils.getUserId());
        }
        // 抛出一个运行时异常

        List<VisitRecordsVo> visitrecordsvolist = rlctVisitRecordsMapper.selectByVisitId(visitIdList);
        return visitrecordsvolist;
    }
}
