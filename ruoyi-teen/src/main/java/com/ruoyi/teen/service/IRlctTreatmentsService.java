package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctTreatments;

/**
 * 项目Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctTreatmentsService 
{
    /**
     * 查询项目
     * 
     * @param treatmentId 项目主键
     * @return 项目
     */
    public RlctTreatments selectRlctTreatmentsByTreatmentId(Long treatmentId);

    /**
     * 查询项目列表
     * 
     * @param rlctTreatments 项目
     * @return 项目集合
     */
    public List<RlctTreatments> selectRlctTreatmentsList(RlctTreatments rlctTreatments);

    /**
     * 新增项目
     * 
     * @param rlctTreatments 项目
     * @return 结果
     */
    public int insertRlctTreatments(RlctTreatments rlctTreatments);

    /**
     * 修改项目
     * 
     * @param rlctTreatments 项目
     * @return 结果
     */
    public int updateRlctTreatments(RlctTreatments rlctTreatments);

    /**
     * 批量删除项目
     * 
     * @param treatmentIds 需要删除的项目主键集合
     * @return 结果
     */
    public int deleteRlctTreatmentsByTreatmentIds(Long[] treatmentIds);

    /**
     * 删除项目信息
     * 
     * @param treatmentId 项目主键
     * @return 结果
     */
    public int deleteRlctTreatmentsByTreatmentId(Long treatmentId);
}
