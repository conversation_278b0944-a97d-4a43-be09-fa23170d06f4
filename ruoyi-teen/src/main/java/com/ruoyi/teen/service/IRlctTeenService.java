package com.ruoyi.teen.service;

import java.util.List;
import com.ruoyi.teen.domain.RlctTeen;
import com.ruoyi.teen.vo.ipad.PadPatientDetailVo;
import com.ruoyi.teen.vo.ipad.PadPatientTeenVo;
import com.ruoyi.teen.vo.ipad.PadPatientVo;
import com.ruoyi.teen.vo.physician.RlctTeenVo;

/**
 * 孩子Service接口
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public interface IRlctTeenService
{
    /**
     * 查询孩子
     *
     * @param teenId 孩子主键
     * @return 孩子
     */
    public RlctTeen selectRlctTeenByTeenId(Long teenId);

    /**
     * 查询孩子列表
     *
     * @param rlctTeen 孩子
     * @return 孩子集合
     */
    public List<RlctTeen> selectRlctTeenList(RlctTeen rlctTeen);

    /**
     * 获取用户绑定的孩子(PC端)
     * @param userId 家长id
     * @return 结果  含父母与孩子关系id
     */
    List<RlctTeen> getTeenList(Long userId);

    /**
     * 新增孩子
     *
     * @param rlctTeen 孩子
     * @return 结果
     */
    public int insertRlctTeen(RlctTeen rlctTeen);

    /**
     * 修改孩子
     *
     * @param rlctTeen 孩子
     * @return 结果
     */
    public int updateRlctTeen(RlctTeen rlctTeen);

    /**
     * 批量删除孩子
     *
     * @param teenIds 需要删除的孩子主键集合
     * @return 结果
     */
    public int deleteRlctTeenByTeenIds(Long[] teenIds);

    /**
     * 删除孩子信息
     *
     * @param teenId 孩子主键
     * @return 结果
     */
    public int deleteRlctTeenByTeenId(Long teenId);


    /**
     * 获取用户绑定的孩子(平板端)
     * @param tel 用户的电话号码
     * @return 结果  含父母与孩子关系id
     */
    List<RlctTeen> getTeenListByTel(String tel);
    List<PadPatientVo> patientList(String tel);
    List<PadPatientTeenVo> patientTeenList(String teenNameCh);
    PadPatientDetailVo patientDetailByTel(String tel);

    /**
     * 获取用户绑定的孩子
     * @param tel 用户的电话号码
     * @return 结果
     */
    List<RlctTeen> getListByTel(String tel);

    /**
     * 家长新增（绑定）孩子
     * @param rlctTeen 孩子实体
     * @return
     */
    int insertTeen(RlctTeen rlctTeen);
    int initTeenNameJob();

    /**
     * 医师端获取孩子信息
     * @param teenId 孩子id
     * @return 结果
     */
    RlctTeenVo selectTeen(Long teenId);

    //更新年龄
    public void updateTeenAge();
}
