package com.ruoyi.teen.service.impl;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.sms.platform.TencentSms;
import com.ruoyi.teen.domain.RlctTodayPerformanceDTO;
import com.ruoyi.teen.mapper.RlctDoctorStatisMapper;
import com.ruoyi.teen.service.IRlctDoctorStatisticsService;
import com.ruoyi.teen.vo.ipad.RlctMonthPerformanceVo;
import com.ruoyi.teen.vo.patient.RlctPersonalInfoVo;
import com.ruoyi.teen.vo.patient.RlctTodayPerformanceVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class RlctDoctorStatisticsImpl implements IRlctDoctorStatisticsService {

    private static final Logger logger = LoggerFactory.getLogger(TencentSms.class);
    @Autowired
    private RlctDoctorStatisMapper rlctDoctorStatis;
    @Override
    public RlctPersonalInfoVo personalInfo(Long userId) {
        logger.debug("开始查询个人信息");
//         2. 获取当前用户ID
//        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            logger.warn("未获取到用户ID，可能未登录");
            throw new ServiceException("用户未登录");
        }

        try {
            // 3. 查询数据库
            RlctPersonalInfoVo result = rlctDoctorStatis.selectDoctorPersonalInfo(userId);
            // 4. 记录成功日志（使用占位符优化性能）
            logger.info("医生个人信息查询成功，用户ID: {}", userId);

            return result;
        } catch (Exception e) {
            // 5. 记录错误日志
            logger.error("查询医生个人信息异常，用户ID: {}", userId, e);
            throw new ServiceException("获取个人信息失败");
        }
    }


    @Override
    public List<RlctTodayPerformanceVo> todayPerformance(Long userId) {
        // 1. 获取当前用户ID
//        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            logger.warn("未获取到用户ID，可能未登录");
            throw new ServiceException("用户未登录");
        }
        try {

            // 自动计算当天范围
            LocalDate today = LocalDate.now();
            String startDate = today.atStartOfDay().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            String endDate = today.plusDays(1).atStartOfDay().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            // 获取实际数据
            List<RlctTodayPerformanceDTO> rlctTodayPerformance = rlctDoctorStatis.selectPerformance(userId,startDate,endDate);

            // 获取字典表中所有治疗类型
            List<SysDictData> treatmentTypes = DictUtils.getDictCache("record_treatment_type");
            // 3. 创建DTO列表（每个治疗类型对应一个默认DTO）
            List<RlctTodayPerformanceDTO> defaultDTOs = new ArrayList<>();

            // 4. 使用增强for循环处理每个治疗类型
            for (SysDictData dictData : treatmentTypes) {
                // 4.1 创建DTO对象
                RlctTodayPerformanceDTO dto = new RlctTodayPerformanceDTO();
                // 4.2 设置治疗类型ID（将dictValue转为Long）
                dto.setTreatmentType(Long.parseLong(dictData.getDictValue()));
                // 4.3 设置默认计数为0
                dto.setCount(BigDecimal.ZERO);
                // 4.4 添加到列表
                defaultDTOs.add(dto);
                // 日志输出检查（调试用）
                logger.debug("创建默认DTO: treatmentType={}, count=0", dto.getTreatmentType());
            }

            // 5. 双重循环匹配并更新count
            for (RlctTodayPerformanceDTO defaultDto : defaultDTOs) {
                for (RlctTodayPerformanceDTO dbDto : rlctTodayPerformance) {
                    if (defaultDto.getTreatmentType().equals(dbDto.getTreatmentType())) {
                        defaultDto.setCount(dbDto.getCount());
                        break; // 找到匹配后跳出内层循环
                    }
                }
            }

            //System.out.println("治疗名称："+DictUtils.getDictLabel("record_treatment_type",String.valueOf(2), "未知"));
            List<RlctTodayPerformanceVo> result = new ArrayList<>();
            for (RlctTodayPerformanceDTO rlctTodayPerformanceDTO : defaultDTOs) {
                RlctTodayPerformanceVo rlctTodayPerformanceVo = new RlctTodayPerformanceVo();
                rlctTodayPerformanceVo.setTreatmentType(rlctTodayPerformanceDTO.getTreatmentType());
                rlctTodayPerformanceVo.setTreatmentTypeName(DictUtils.getDictLabel("record_treatment_type",String.valueOf(rlctTodayPerformanceDTO.getTreatmentType()), "未知"));
                rlctTodayPerformanceVo.setCount(rlctTodayPerformanceDTO.getCount());
                result.add(rlctTodayPerformanceVo);
            }

            // 查询 完成治疗接诊人数记录
            BigDecimal  todayPeople = rlctDoctorStatis.selectPeoplePerformance(userId,startDate,endDate);
            RlctTodayPerformanceVo rlctTodayPerformanceVo = new RlctTodayPerformanceVo();
            rlctTodayPerformanceVo.setTreatmentTypeName("接诊");
            if (todayPeople == null){
                todayPeople = BigDecimal.ZERO;
            }
            rlctTodayPerformanceVo.setCount(todayPeople);
            result.add(rlctTodayPerformanceVo);

            logger.info("医生今日业绩查询成功，用户ID: {}", userId);
            return result;
        } catch (Exception e) {
            // 5. 记录错误日志
            logger.error("查询医生今日业绩异常，用户ID: {}", userId, e);
            throw new ServiceException("获取今日业绩失败");
        }
    }

    @Override
    public List<RlctMonthPerformanceVo> monthPerformance(Long userId) {
        // 1. 用户ID校验（提前失败）
//        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            logger.warn("未获取到用户ID，可能未登录");
            throw new ServiceException("用户未登录");
        }
        try {
            // 2. 获取时间范围
            // 获取本月第一天
            String firstDayOfStartMonth = DateUtils.getMonthStart();
            // 获取下月第一天
            String firstDayOfStartNextMonth = DateUtils.getNextMonthStart();
            // 获取上月第一天
            String firstDayOfStartLastMonth = DateUtils.getLastMonthStart();


            // 3. 查询数据（并行查询提升性能）
            List<RlctTodayPerformanceDTO> thisMonthPerformance = rlctDoctorStatis.selectPerformance(userId,firstDayOfStartMonth,firstDayOfStartNextMonth);
            List<RlctTodayPerformanceDTO> lastData = rlctDoctorStatis.selectPerformance(userId,firstDayOfStartLastMonth,firstDayOfStartMonth);


            // 4. 获取所有治疗类型
            List<SysDictData> treatmentTypes = DictUtils.getDictCache("record_treatment_type");
            List<RlctMonthPerformanceVo> result = new ArrayList<>();
            // 5. 处理每种治疗类型
            for (SysDictData dictData : treatmentTypes) {
                Long treatmentType = Long.parseLong(dictData.getDictValue());
                String treatmentName = dictData.getDictLabel();

                // 5.1 查找本月数据
                BigDecimal currentCount = findCountByType(thisMonthPerformance, treatmentType);

                // 5.2 查找上月数据
                BigDecimal lastCount = findCountByType(lastData, treatmentType);

                // 5.3 计算差值和增长率
                BigDecimal difference = currentCount.subtract(lastCount);
                BigDecimal growthRate = calculateGrowthRate(currentCount, lastCount);

                // 5.4 构建结果对象
                RlctMonthPerformanceVo vo = new RlctMonthPerformanceVo();
                vo.setTreatmentType(treatmentType);
                vo.setTreatmentTypeName(treatmentName);
                vo.setCurrentCount(currentCount);
                vo.setLastCount(lastCount);
                vo.setDifference(difference);
                vo.setGrowthRate(growthRate);

                result.add(vo);
            }

            // 查询本月接诊人数
            BigDecimal  thisMonthPeople = rlctDoctorStatis.selectPeoplePerformance(userId,firstDayOfStartMonth,firstDayOfStartNextMonth);
            // 查询上月接诊人数
            BigDecimal  lastPeople = rlctDoctorStatis.selectPeoplePerformance(userId,firstDayOfStartLastMonth,firstDayOfStartMonth);

            BigDecimal difference = thisMonthPeople.subtract(lastPeople);
            BigDecimal growthRate = calculateGrowthRate(thisMonthPeople, lastPeople);

            RlctMonthPerformanceVo vo = new RlctMonthPerformanceVo();
            vo.setTreatmentTypeName("接诊");
            vo.setCurrentCount(thisMonthPeople);
            vo.setLastCount(lastPeople);
            vo.setDifference(difference);
            vo.setGrowthRate(growthRate);
            result.add(vo);


            return result;
        } catch (Exception e) {
            // 5. 记录错误日志
            logger.error("查询医生月业绩异常，用户ID: {}", userId, e);
            throw new ServiceException("获取月业绩失败");
        }
    }

    // 辅助方法：根据类型查找数量
    private BigDecimal findCountByType(List<RlctTodayPerformanceDTO> data, Long treatmentType) {
        for (RlctTodayPerformanceDTO dto : data) {
            if (dto.getTreatmentType().equals(treatmentType)) {
                return dto.getCount();
            }
        }
        return BigDecimal.ZERO;
    }

    // 辅助方法：计算增长率
    private BigDecimal calculateGrowthRate(BigDecimal current, BigDecimal last) {
        if (last.compareTo(BigDecimal.ZERO) == 0) {
            return current.compareTo(BigDecimal.ZERO) > 0 ?
                    new BigDecimal("1.00") : // 上月为0且本月有数据
                    BigDecimal.ZERO;        // 上月和本月都为0
        }
        return current.subtract(last).divide(last, 1, RoundingMode.HALF_UP);
    }

}


