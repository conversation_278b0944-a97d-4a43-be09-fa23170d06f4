package com.ruoyi.teen.service.impl;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.teen.domain.RlctPadVersion;
import com.ruoyi.teen.mapper.RlctPadVersionMapper;
import com.ruoyi.teen.service.PadVersionService;
import com.ruoyi.teen.vo.pc.PadVersionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PadVersionImpl implements PadVersionService {

    // 引入日志
    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(PadVersionImpl.class);

    @Autowired
    private RlctPadVersionMapper rlctPadVersionMapper;

    @Override
    public AjaxResult getLatestVersionUrl(RlctPadVersion  rlctPadVersion) {
        // 1. 参数校验
        String versionNumber = rlctPadVersion.getVersionNumber();
        if (StringUtils.isBlank(versionNumber)) {
            return AjaxResult.error("版本号不能为空");
        }
        // 2. 获取最新版本（添加try-catch保护）
        try {
            PadVersionVo padVersionVo = new PadVersionVo();
            //  获取最新版本（忽略传入的versionNumber）
            RlctPadVersion latestVersion = rlctPadVersionMapper.selectNewestVersion();
            if (latestVersion == null) {
                return AjaxResult.error("系统未发布任何版本");
            }

            // 2. 查询当前版本信息
            RlctPadVersion currentVersion = rlctPadVersionMapper.selectLatestVersion(versionNumber.trim());
            if (currentVersion == null) {
                padVersionVo.setVersionUrl(latestVersion.getVersionUrl());
                padVersionVo.setStatus("0"); // 0 要更新
                return AjaxResult.success("当前版本未登记",padVersionVo);
            }


            // 4. 比较版本
            if (!currentVersion.getVersionNumber().equalsIgnoreCase(latestVersion.getVersionNumber())) {
                padVersionVo.setVersionUrl(latestVersion.getVersionUrl());
                padVersionVo.setStatus("0"); // 0 要更新
                return AjaxResult.success("发现新版本", padVersionVo);
            }
            padVersionVo.setVersionUrl(latestVersion.getVersionUrl());
            padVersionVo.setStatus("1"); // 1 最新版本

            return AjaxResult.success("当前已是最新版本", padVersionVo);

        } catch (Exception e) {
            logger.error("获取版本信息异常", e);
            return AjaxResult.error("系统繁忙，请稍后再试");
        }
    }
}
