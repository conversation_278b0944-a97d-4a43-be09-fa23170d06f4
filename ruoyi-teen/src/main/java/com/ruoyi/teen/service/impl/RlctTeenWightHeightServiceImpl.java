package com.ruoyi.teen.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.teen.domain.RlctParentTeen;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.teen.mapper.RlctTeenWightHeightMapper;
import com.ruoyi.teen.domain.RlctTeenWightHeight;
import com.ruoyi.teen.service.IRlctTeenWightHeightService;

/**
 * 身高体重Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
@Service
public class RlctTeenWightHeightServiceImpl implements IRlctTeenWightHeightService
{
    @Autowired
    private RlctTeenWightHeightMapper rlctTeenWightHeightMapper;

    /**
     * 查询身高体重
     *
     * @param id 身高体重主键
     * @return 身高体重
     */
    @Override
    public RlctTeenWightHeight selectRlctTeenWightHeightById(Long id)
    {
        return rlctTeenWightHeightMapper.selectRlctTeenWightHeightById(id);
    }

    /**
     * 查询身高体重列表
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 身高体重
     */
    @Override
    public List<RlctTeenWightHeight> selectRlctTeenWightHeightList(RlctTeenWightHeight rlctTeenWightHeight)
    {
        return rlctTeenWightHeightMapper.selectRlctTeenWightHeightList(rlctTeenWightHeight);
    }

    /**
     * 新增身高体重
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 结果
     */
    @Override
    public int insertRlctTeenWightHeight(RlctTeenWightHeight rlctTeenWightHeight)
    {
        rlctTeenWightHeight.setCreateTime(DateUtils.getNowDate());
        return rlctTeenWightHeightMapper.insertRlctTeenWightHeight(rlctTeenWightHeight);
    }

    /**
     * 修改身高体重
     *
     * @param rlctTeenWightHeight 身高体重
     * @return 结果
     */
    @Override
    public int updateRlctTeenWightHeight(RlctTeenWightHeight rlctTeenWightHeight)
    {
        return rlctTeenWightHeightMapper.updateRlctTeenWightHeight(rlctTeenWightHeight);
    }

    /**
     * 批量删除身高体重
     *
     * @param ids 需要删除的身高体重主键
     * @return 结果
     */
    @Override
    public int deleteRlctTeenWightHeightByIds(Long[] ids)
    {
        return rlctTeenWightHeightMapper.deleteRlctTeenWightHeightByIds(ids);
    }

    /**
     * 删除身高体重信息
     *
     * @param id 身高体重主键
     * @return 结果
     */
    @Override
    public int deleteRlctTeenWightHeightById(Long id)
    {
        return rlctTeenWightHeightMapper.deleteRlctTeenWightHeightById(id);
    }

    /**
     * 获取孩子的身高体重
     * @param parentTeen 父母孩子关系实体
     * @return 结果
     */
    @Override
    public List<RlctTeenWightHeight> getBodyInfo(RlctParentTeen parentTeen) {
            return rlctTeenWightHeightMapper.getBodyInfo(parentTeen);
    }

    /**
     * 获取孩子的身高体重(平板端)
     * @param parentTeen 父母孩子关系实体
     * @return
     */
//    @Override
//    public List<RlctTeenWightHeight> BodyInfo(RlctParentTeen parentTeen) {
//        return rlctTeenWightHeightMapper.BodyInfo(parentTeen);
//    }

    /**
     * 获取孩子的前n次身高
     * @param id
     * @param n
     * @return
     */
    @Override
    public List<RlctTeenWightHeight> getHeight(Long parentTeenId,Long n) {
        return rlctTeenWightHeightMapper.getHeight(parentTeenId,n);
    }
}
