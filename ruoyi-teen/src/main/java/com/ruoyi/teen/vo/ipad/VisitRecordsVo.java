package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class VisitRecordsVo {
    private String teenName;
    /*就诊id*/
    private Long parentTeenId;
    /*接诊时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date admissionTime;
    /*诊前身高*/
    private String heightBeforeTreatment;
    /*诊后身高*/
    private String heightAfterTreatment;
    /*诊后体重*/
    private String weightAfterTreatment;
    /*初诊时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date initTime;
    /*初诊身高*/
    private String initHeight;
    /*当前套餐已用次数*/
    private Integer packageNumberTimes;
    /*当前套餐剩余次数*/
    private Integer remainingTimes;
    /*身高增长*/
    private String heightGrowth;
    /*备注（治疗方案）*/
    private String visitDetails;

    public Long getParentTeenId() {
        return parentTeenId;
    }

    public void setParentTeenId(Long parentTeenId) {
        this.parentTeenId = parentTeenId;
    }

    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public Date getAdmissionTime() {
        return admissionTime;
    }

    public void setAdmissionTime(Date admissionTime) {
        this.admissionTime = admissionTime;
    }

    public String getHeightBeforeTreatment() {
        return heightBeforeTreatment;
    }

    public void setHeightBeforeTreatment(String heightBeforeTreatment) {
        this.heightBeforeTreatment = heightBeforeTreatment;
    }

    public String getHeightAfterTreatment() {
        return heightAfterTreatment;
    }

    public void setHeightAfterTreatment(String heightAfterTreatment) {
        this.heightAfterTreatment = heightAfterTreatment;
    }

    public String getWeightAfterTreatment() {
        return weightAfterTreatment;
    }

    public void setWeightAfterTreatment(String weightAfterTreatment) {
        this.weightAfterTreatment = weightAfterTreatment;
    }

    public Date getInitTime() {
        return initTime;
    }

    public void setInitTime(Date initTime) {
        this.initTime = initTime;
    }

    public String getInitHeight() {
        return initHeight;
    }

    public void setInitHeight(String initHeight) {
        this.initHeight = initHeight;
    }

    public Integer getPackageNumberTimes() {
        return packageNumberTimes;
    }

    public void setPackageNumberTimes(Integer packageNumberTimes) {
        this.packageNumberTimes = packageNumberTimes;
    }

    public Integer getRemainingTimes() {
        return remainingTimes;
    }

    public void setRemainingTimes(Integer remainingTimes) {
        this.remainingTimes = remainingTimes;
    }

    public String getHeightGrowth() {
        return heightGrowth;
    }

    public void setHeightGrowth(String heightGrowth) {
        this.heightGrowth = heightGrowth;
    }

    public String getVisitDetails() {
        return visitDetails;
    }

    public void setVisitDetails(String visitDetails) {
        this.visitDetails = visitDetails;
    }
}
