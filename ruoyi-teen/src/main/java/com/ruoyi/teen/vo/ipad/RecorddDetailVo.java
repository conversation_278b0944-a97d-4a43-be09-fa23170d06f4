package com.ruoyi.teen.vo.ipad;

import com.ruoyi.teen.domain.*;

import java.util.List;

public class RecorddDetailVo {
    private RlctTeenTwoVo rlctTeenTwo;

    /*产品使用记录*/
    private List<ProductUse> productUse;

    /*骨龄*/
    private List<RlctBoneAgeRecord> boneAgeRecord;

    /*历史就诊记录*/
    private List<RlctVisitVo> rlctHistoryVisitTwoList;

    /*本次现在就诊记录*/
    private RlctThisTimeVisitVo rlctThisTimeVisitTwo;


    /*日常行为登记*/
    private DailyHabitRecordsVo dailyHabitRecords;

    /*是否分配治疗工作状态  0-代表显示分配（接诊）按钮  1-限时提交接诊按钮  */
    private Integer status;


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public RlctThisTimeVisitVo getRlctThisTimeVisitTwo() {
        return rlctThisTimeVisitTwo;
    }

    public void setRlctThisTimeVisitTwo(RlctThisTimeVisitVo rlctThisTimeVisitTwo) {
        this.rlctThisTimeVisitTwo = rlctThisTimeVisitTwo;
    }

    public List<RlctVisitVo> getRlctHistoryVisitTwoList() {
        return rlctHistoryVisitTwoList;
    }

    public void setRlctHistoryVisitTwoList(List<RlctVisitVo> rlctHistoryVisitTwoList) {
        this.rlctHistoryVisitTwoList = rlctHistoryVisitTwoList;
    }

    public List<ProductUse> getProductUse() {
        return productUse;
    }

    public void setProductUse(List<ProductUse> productUse) {
        this.productUse = productUse;
    }

    public RlctTeenTwoVo getRlctTeenTwo() {
        return rlctTeenTwo;
    }

    public void setRlctTeenTwo(RlctTeenTwoVo rlctTeenTwo) {
        this.rlctTeenTwo = rlctTeenTwo;
    }

    public DailyHabitRecordsVo getDailyHabitRecords() {
        return dailyHabitRecords;
    }

    public void setDailyHabitRecords(DailyHabitRecordsVo dailyHabitRecords) {
        this.dailyHabitRecords = dailyHabitRecords;
    }


    public List<RlctBoneAgeRecord> getBoneAgeRecord() {
        return boneAgeRecord;
    }

    public void setBoneAgeRecord(List<RlctBoneAgeRecord> boneAgeRecord) {
        this.boneAgeRecord = boneAgeRecord;
    }
}
