package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.teen.domain.RlctTeen;

import java.util.Date;
import java.util.List;

public class PadPatientDetailVo {
    //userId
    private String patientId;
    private String nickName;
    private String phonenumber;//手机号
    private String sex;//性别

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;//建档时间
    private Integer visitTimes;//就诊次数
    private Integer unUsedTimes;//未使用数
    private List<RlctTeen> teenList;//患者列表

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getVisitTimes() {
        return visitTimes;
    }

    public void setVisitTimes(Integer visitTimes) {
        this.visitTimes = visitTimes;
    }

    public Integer getUnUsedTimes() {
        return unUsedTimes;
    }

    public void setUnUsedTimes(Integer unUsedTimes) {
        this.unUsedTimes = unUsedTimes;
    }

    public List<RlctTeen> getTeenList() {
        return teenList;
    }

    public void setTeenList(List<RlctTeen> teenList) {
        this.teenList = teenList;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }
}
