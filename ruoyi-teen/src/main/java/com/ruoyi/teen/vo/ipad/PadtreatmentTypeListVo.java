package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class PadtreatmentTypeListVo {
    private Long doctorVisicId;
    private Long treatmentType;
    private String treatmentTypeName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime; //审核时间

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public Long getTreatmentType() {
        return treatmentType;
    }

    public void setTreatmentType(Long treatmentType) {
        this.treatmentType = treatmentType;
    }

    public Long getDoctorVisicId() {
        return doctorVisicId;
    }

    public void setDoctorVisicId(Long doctorVisicId) {
        this.doctorVisicId = doctorVisicId;
    }

    public String getTreatmentTypeName() {
        return treatmentTypeName;
    }

    public void setTreatmentTypeName(String treatmentTypeName) {
        this.treatmentTypeName = treatmentTypeName;
    }
}
