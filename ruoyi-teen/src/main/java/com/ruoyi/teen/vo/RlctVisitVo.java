package com.ruoyi.teen.vo;

import com.ruoyi.teen.domain.RlctVisit;
import com.ruoyi.teen.vo.physician.TreatmentImageVo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Component("patientRlctVisitVo")
public class RlctVisitVo extends RlctVisit {

    private String packageName;

    private String nickName;

    private String phonenumber;

    private String teenName;

    private List<TreatmentImageVo> beforeImages;
    private List<TreatmentImageVo> AfterImages;

    public List<TreatmentImageVo> getBeforeImages() {
        return beforeImages;
    }

    public void setBeforeImages(List<TreatmentImageVo> beforeImages) {
        this.beforeImages = beforeImages;
    }

    public List<TreatmentImageVo> getAfterImages() {
        return AfterImages;
    }

    public void setAfterImages(List<TreatmentImageVo> afterImages) {
        AfterImages = afterImages;
    }

    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    @Override
    public String toString() {
        return "RlctVisitVo{" +
                "packageName='" + packageName + '\'' +
                ", nickName='" + nickName + '\'' +
                ", phonenumber='" + phonenumber + '\'' +
                '}';
    }
}
