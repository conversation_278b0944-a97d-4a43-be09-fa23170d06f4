package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

public class PadPatientTeenVo {
    //userId
    private String patientId;

    private String nickName;
    private String phonenumber;

    private Long parentTeenId;
    private Long teenId;
    private String teenName;
    private String teenNameCh;
    private Long teenAge;
    private String teenSex;
    private String teenSexDict;

    private BigDecimal lastHeight;//上一次的身高
    private BigDecimal lastWeight;//上一次的体重

    public Long getParentTeenId() {
        return parentTeenId;
    }

    public void setParentTeenId(Long parentTeenId) {
        this.parentTeenId = parentTeenId;
    }

    public BigDecimal getLastHeight() {
        return lastHeight;
    }

    public void setLastHeight(BigDecimal lastHeight) {
        this.lastHeight = lastHeight;
    }

    public BigDecimal getLastWeight() {
        return lastWeight;
    }

    public void setLastWeight(BigDecimal lastWeight) {
        this.lastWeight = lastWeight;
    }

    /** 孩子出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date teenBirth;


    public Long getTeenId() {
        return teenId;
    }

    public void setTeenId(Long teenId) {
        this.teenId = teenId;
    }

    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public String getTeenNameCh() {
        return teenNameCh;
    }

    public void setTeenNameCh(String teenNameCh) {
        this.teenNameCh = teenNameCh;
    }

    public Long getTeenAge() {
        return teenAge;
    }

    public void setTeenAge(Long teenAge) {
        this.teenAge = teenAge;
    }

    public String getTeenSex() {
        return teenSex;
    }

    public void setTeenSex(String teenSex) {
        this.teenSex = teenSex;
    }

    public String getTeenSexDict() {
        return teenSexDict;
    }

    public void setTeenSexDict(String teenSexDict) {
        this.teenSexDict = teenSexDict;
    }

    public Date getTeenBirth() {
        return teenBirth;
    }

    public void setTeenBirth(Date teenBirth) {
        this.teenBirth = teenBirth;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }
}
