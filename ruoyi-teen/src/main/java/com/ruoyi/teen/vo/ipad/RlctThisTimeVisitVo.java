package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class RlctThisTimeVisitVo {
    /** 就诊id */
    @Excel(name = "就诊id")
    private Long visitId;
    /** 接诊时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接诊时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date admissionTime;

    /** 文本字段，用于存储就诊的详细信息，如诊断、处方等 */
    @Excel(name = "文本字段，用于存储就诊的详细信息，如诊断、处方等")
    private String visitDetails;

    /** 存储治疗前身高 */
    @Excel(name = "存储治疗前身高")
    private BigDecimal heightBeforeTreatment;

    /** 存储治疗后身高 */
    @Excel(name = "存储治疗后身高")
    private BigDecimal heightAfterTreatment;

    /** 存储治疗前体重 */
    @Excel(name = "存储治疗前体重")
    private BigDecimal weightBeforeTreatment;

    /** 存储治疗后体重 */
    @Excel(name = "存储治疗后体重")
    private BigDecimal weightAfterTreatment;

    /*用户与套餐关系id*/
    private Long userPackageId;

    /*套餐项目名*/
    private String packageName;

    /*本次消费次数*/
    private Long currentConsumptionCount;

    /*扣款账户*/
    private String phonenumber;

    // 治疗项目列表
    private List<TreatmentVo> treatments;

    /*治疗前记录图片*/
    private String beforeTreatmentImageUrl;

    /*治疗后记录图片*/
    private String afterTreatmentImageUrl;

    /*产品方案*/
    private String productUseName;


    public Long getCurrentConsumptionCount() {
        return currentConsumptionCount;
    }

    public void setCurrentConsumptionCount(Long currentConsumptionCount) {
        this.currentConsumptionCount = currentConsumptionCount;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public Long getUserPackageId() {
        return userPackageId;
    }

    public void setUserPackageId(Long userPackageId) {
        this.userPackageId = userPackageId;
    }

    public Date getAdmissionTime() {
        return admissionTime;
    }

    public void setAdmissionTime(Date admissionTime) {
        this.admissionTime = admissionTime;
    }

    public String getVisitDetails() {
        return visitDetails;
    }

    public void setVisitDetails(String visitDetails) {
        this.visitDetails = visitDetails;
    }

    public BigDecimal getHeightBeforeTreatment() {
        return heightBeforeTreatment;
    }

    public void setHeightBeforeTreatment(BigDecimal heightBeforeTreatment) {
        this.heightBeforeTreatment = heightBeforeTreatment;
    }

    public BigDecimal getHeightAfterTreatment() {
        return heightAfterTreatment;
    }

    public void setHeightAfterTreatment(BigDecimal heightAfterTreatment) {
        this.heightAfterTreatment = heightAfterTreatment;
    }

    public BigDecimal getWeightBeforeTreatment() {
        return weightBeforeTreatment;
    }

    public void setWeightBeforeTreatment(BigDecimal weightBeforeTreatment) {
        this.weightBeforeTreatment = weightBeforeTreatment;
    }

    public BigDecimal getWeightAfterTreatment() {
        return weightAfterTreatment;
    }

    public void setWeightAfterTreatment(BigDecimal weightAfterTreatment) {
        this.weightAfterTreatment = weightAfterTreatment;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public List<TreatmentVo> getTreatments() {
        return treatments;
    }

    public void setTreatments(List<TreatmentVo> treatments) {
        this.treatments = treatments;
    }

    public String getBeforeTreatmentImageUrl() {
        return beforeTreatmentImageUrl;
    }

    public void setBeforeTreatmentImageUrl(String beforeTreatmentImageUrl) {
        this.beforeTreatmentImageUrl = beforeTreatmentImageUrl;
    }

    public String getAfterTreatmentImageUrl() {
        return afterTreatmentImageUrl;
    }

    public void setAfterTreatmentImageUrl(String afterTreatmentImageUrl) {
        this.afterTreatmentImageUrl = afterTreatmentImageUrl;
    }

    public String getProductUseName() {
        return productUseName;
    }

    public void setProductUseName(String productUseName) {
        this.productUseName = productUseName;
    }
}
