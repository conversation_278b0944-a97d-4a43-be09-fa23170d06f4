package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

public class RlctDoctorVisicVo {
    private Long doctorVisicId; // 医生与就诊关系id
    private String nickName; // 医生昵称
    private List<PadtreatmentTypeListVo> treatmentTypeList;  // 治疗类型
    private String teenName;  // 孩子名称
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date treatmentTime; // 治疗时间
    private String reviewStatus;  // 审核状态
    private Long userPackageId;// 用户与套餐关系id
    private Long visitId;  // 就诊id
    private Long doctorId; // 医生id
    private Date reviewTime;  //审核时间

    /*治疗完成时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date healTime;


    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public List<PadtreatmentTypeListVo> getTreatmentTypeList() {
        return treatmentTypeList;
    }

    public void setTreatmentTypeList(List<PadtreatmentTypeListVo> treatmentTypeList) {
        this.treatmentTypeList = treatmentTypeList;
    }

    public Date getHealTime() {
        return healTime;
    }

    public void setHealTime(Date healTime) {
        this.healTime = healTime;
    }

    public Long getUserPackageId() {
        return userPackageId;
    }

    public void setUserPackageId(Long userPackageId) {
        this.userPackageId = userPackageId;
    }

    public Long getDoctorVisicId() {
        return doctorVisicId;
    }

    public void setDoctorVisicId(Long doctorVisicId) {
        this.doctorVisicId = doctorVisicId;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }



    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public Date getTreatmentTime() {
        return treatmentTime;
    }

    public void setTreatmentTime(Date treatmentTime) {
        this.treatmentTime = treatmentTime;
    }
}
