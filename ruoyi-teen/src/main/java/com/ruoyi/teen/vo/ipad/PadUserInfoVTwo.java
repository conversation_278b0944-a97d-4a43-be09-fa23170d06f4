package com.ruoyi.teen.vo.ipad;

import java.util.List;

public class PadUserInfoVTwo extends PadUserInfo{

    // 头像URL
    private String avatar;
    // 总金额
    private Long totalPrice;
    // 每日充值记录
    private Long todayPrice;

    // 本月充值金额服务记录
    List<UserServiceRecordVo> userServicePriceRecordList;


    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public List<UserServiceRecordVo> getUserServicePriceRecordList() {
        return userServicePriceRecordList;
    }

    public void setUserServicePriceRecordList(List<UserServiceRecordVo> userServicePriceRecordList) {
        this.userServicePriceRecordList = userServicePriceRecordList;
    }

    public Long getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Long totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Long getTodayPrice() {
        return todayPrice;
    }

    public void setTodayPrice(Long todayPrice) {
        this.todayPrice = todayPrice;
    }
}
