package com.ruoyi.teen.vo.ipad;

import com.ruoyi.common.annotation.Excel;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

public class RlctPackagesVo {

    /** 套餐的id */
    @Excel(name = "套餐的id")
    private String packageId;

    /** 套餐的名称 */
    @Excel(name = "套餐的名称")
    @NotNull(message = "套餐名称不能为空")
    @Size(min = 0,max = 255,message = "套餐名称长度不能超过255个字符串")
    private String packageName;

    /** 套餐的原价 */
    @Excel(name = "套餐的原价")
    private BigDecimal packagePrice;

    /** 套餐的会员价 */
    @Excel(name = "套餐的会员价")
    private BigDecimal packageMemberPrice;

    /*套餐次数*/
    private Long packageNumber;

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPackagePrice() {
        return packagePrice;
    }

    public void setPackagePrice(BigDecimal packagePrice) {
        this.packagePrice = packagePrice;
    }

    public BigDecimal getPackageMemberPrice() {
        return packageMemberPrice;
    }

    public void setPackageMemberPrice(BigDecimal packageMemberPrice) {
        this.packageMemberPrice = packageMemberPrice;
    }

    public Long getPackageNumber() {
        return packageNumber;
    }

    public void setPackageNumber(Long packageNumber) {
        this.packageNumber = packageNumber;
    }
}
