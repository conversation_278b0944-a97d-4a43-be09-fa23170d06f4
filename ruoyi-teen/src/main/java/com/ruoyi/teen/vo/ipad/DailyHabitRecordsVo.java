package com.ruoyi.teen.vo.ipad;

import com.ruoyi.common.annotation.Excel;

public class DailyHabitRecordsVo {

    /** 入睡时间 */
    @Excel(name = "入睡时间")
    private String bedtime;

    /** 睡眠时长 */
    @Excel(name = "睡眠时长")
    private String sleepDuration;

    /** 睡眠其它 */
    @Excel(name = "睡眠其它")
    private String sleepOther;

    /** 运动频率 */
    @Excel(name = "运动频率")
    private String exerciseFrequency;

    /** 运动时长 */
    @Excel(name = "运动时长")
    private String exerciseDuration;

    /** 运动其它 */
    @Excel(name = "运动其它")
    private String exerciseOther;

    /** 饮食习惯（字典：rlct_eating_habits,0 荤多菜少 1荤少菜多） */
    @Excel(name = "饮食习惯", readConverterExp = "字=典：rlct_eating_habits,0,荤=多菜少,1=荤少菜多")
    private String eatingHabits;

    /** 饮食其它 */
    @Excel(name = "饮食其它")
    private String eatingOther;

    /** 钙(字典sys_yes_no  Y是 N否    ) */
    @Excel(name = "钙(字典sys_yes_no  Y是 N否    )")
    private String calcium;

    /** 维生素A（字典sys_yes_no Y是 N否） */
    @Excel(name = "维生素A", readConverterExp = "字=典sys_yes_no,Y=是,N=否")
    private String vitaminA;

    /** 维生素D(字典sys_yes_no  Y是 N否    ) */
    @Excel(name = "维生素D(字典sys_yes_no  Y是 N否    )")
    private String vitaminD;

    /** 复合维生素(字典sys_yes_no  Y是 N否    ) */
    @Excel(name = "复合维生素(字典sys_yes_no  Y是 N否    )")
    private String multivitamin;

    /** 氨基丁酸(字典sys_yes_no  Y是 N否    ) */
    @Excel(name = "氨基丁酸(字典sys_yes_no  Y是 N否    )")
    private String gaba;

    /** 营养素其它 */
    @Excel(name = "营养素其它")
    private String nutritionOther;

    public String getBedtime() {
        return bedtime;
    }

    public void setBedtime(String bedtime) {
        this.bedtime = bedtime;
    }

    public String getSleepDuration() {
        return sleepDuration;
    }

    public void setSleepDuration(String sleepDuration) {
        this.sleepDuration = sleepDuration;
    }

    public String getSleepOther() {
        return sleepOther;
    }

    public void setSleepOther(String sleepOther) {
        this.sleepOther = sleepOther;
    }

    public String getExerciseFrequency() {
        return exerciseFrequency;
    }

    public void setExerciseFrequency(String exerciseFrequency) {
        this.exerciseFrequency = exerciseFrequency;
    }

    public String getExerciseDuration() {
        return exerciseDuration;
    }

    public void setExerciseDuration(String exerciseDuration) {
        this.exerciseDuration = exerciseDuration;
    }

    public String getExerciseOther() {
        return exerciseOther;
    }

    public void setExerciseOther(String exerciseOther) {
        this.exerciseOther = exerciseOther;
    }

    public String getEatingHabits() {
        return eatingHabits;
    }

    public void setEatingHabits(String eatingHabits) {
        this.eatingHabits = eatingHabits;
    }

    public String getEatingOther() {
        return eatingOther;
    }

    public void setEatingOther(String eatingOther) {
        this.eatingOther = eatingOther;
    }

    public String getCalcium() {
        return calcium;
    }

    public void setCalcium(String calcium) {
        this.calcium = calcium;
    }

    public String getVitaminA() {
        return vitaminA;
    }

    public void setVitaminA(String vitaminA) {
        this.vitaminA = vitaminA;
    }

    public String getVitaminD() {
        return vitaminD;
    }

    public void setVitaminD(String vitaminD) {
        this.vitaminD = vitaminD;
    }

    public String getMultivitamin() {
        return multivitamin;
    }

    public void setMultivitamin(String multivitamin) {
        this.multivitamin = multivitamin;
    }

    public String getGaba() {
        return gaba;
    }

    public void setGaba(String gaba) {
        this.gaba = gaba;
    }

    public String getNutritionOther() {
        return nutritionOther;
    }

    public void setNutritionOther(String nutritionOther) {
        this.nutritionOther = nutritionOther;
    }
}
