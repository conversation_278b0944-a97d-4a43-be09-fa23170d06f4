package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class VisitVo {

    /**用户与套餐的关系id*/
    private Long id;
    private List<Long> getIds;
    private BigDecimal weight;
    private BigDecimal height;

    /** 主键，唯一标识每个就诊记录的ID */
    private Long visitId;
    /** 套餐Id */
    private Long packageId;

    /** 父母孩子关系id */
    private Long parentTeenId;

    /** 扣款账户(父母的id) */
    private String deductParentId;

    /** 治疗师Id */
    private Long userId;

    /** 状态(0治疗中1完成治疗)完成治疗后插入消费表 */
    private Long status;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 治疗的医师 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<Long> getGetIds() {
        return getIds;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public void setGetIds(List<Long> getIds) {
        this.getIds = getIds;
    }

    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    public Long getParentTeenId() {
        return parentTeenId;
    }

    public void setParentTeenId(Long parentTeenId) {
        this.parentTeenId = parentTeenId;
    }

    public String getDeductParentId() {
        return deductParentId;
    }

    public void setDeductParentId(String deductParentId) {
        this.deductParentId = deductParentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "VisitVo{" +
                "visitId=" + visitId +
                ", packageId=" + packageId +
                ", parentTeenId=" + parentTeenId +
                ", deductParentId='" + deductParentId + '\'' +
                ", userId=" + userId +
                ", status=" + status +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                '}';
    }
}
