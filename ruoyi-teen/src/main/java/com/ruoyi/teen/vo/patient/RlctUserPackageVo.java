package com.ruoyi.teen.vo.patient;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.teen.domain.RlctUserPackage;

/**
 * <AUTHOR>
 * @date 2023/11/24
 */
public class RlctUserPackageVo extends RlctUserPackage {

    /** 套餐的名称 */
    @Excel(name = "套餐的名称")
    private String packageName;

    /** 套餐图片 */
    private String packageImageUrl;

    /** 剩余次数 */
    private Long remainingTimes;

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Long getRemainingTimes() {
        return remainingTimes;
    }

    public void setRemainingTimes(Long remainingTimes) {
        this.remainingTimes = remainingTimes;
    }

    public String getPackageImageUrl() {
        return packageImageUrl;
    }

    public void setPackageImageUrl(String packageImageUrl) {
        this.packageImageUrl = packageImageUrl;
    }

    @Override
    public String toString() {
        return "RlctUserPackageVo{" +
                "packageName='" + packageName + '\'' +
                ", packageImageUrl='" + packageImageUrl + '\'' +
                ", remainingTimes=" + remainingTimes +
                '}';
    }
}
