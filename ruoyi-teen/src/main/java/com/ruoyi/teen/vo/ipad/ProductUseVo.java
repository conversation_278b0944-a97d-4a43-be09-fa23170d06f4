package com.ruoyi.teen.vo.ipad;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.domain.BaseEntity;

public class ProductUseVo  extends BaseEntity {
    /** 主键，唯一标识每个产品使用记录的ID */
    @TableId(value = "userProductId", type = IdType.AUTO)
    private Long userProductId;
    private String productUseName;
    private Long doctorId;
    private Long parentTeenId;
    private Long visitId;

    public Long getUserProductId() {
        return userProductId;
    }

    public void setUserProductId(Long userProductId) {
        this.userProductId = userProductId;
    }

    public String getProductUseName() {
        return productUseName;
    }

    public void setProductUseName(String productUseName) {
        this.productUseName = productUseName;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public Long getParentTeenId() {
        return parentTeenId;
    }

    public void setParentTeenId(Long parentTeenId) {
        this.parentTeenId = parentTeenId;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }
}
