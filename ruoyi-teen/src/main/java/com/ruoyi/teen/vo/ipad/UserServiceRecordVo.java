package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

public class UserServiceRecordVo {
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date time;//时间
    private Long totalArchives; //档案数
    private BigDecimal totalPrice; //金额数

    public Long getTotalArchives() {
        return totalArchives;
    }

    public void setTotalArchives(Long totalArchives) {
        this.totalArchives = totalArchives;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }
}
