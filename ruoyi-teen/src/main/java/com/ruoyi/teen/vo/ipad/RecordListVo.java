package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Date;

public class RecordListVo {
    private Long parentTeenId;
    private String teenName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date teenBirth;
    private Long  teenAge;
    private BigDecimal heightAfterTreatment;
    private BigDecimal weightAfterTreatment;
    private String  phoneNumber;

    public Long getTeenAge() {
        return teenAge;
    }

    public void setTeenAge(Long teenAge) {
        this.teenAge = teenAge;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Long getParentTeenId() {
        return parentTeenId;
    }

    public void setParentTeenId(Long parentTeenId) {
        this.parentTeenId = parentTeenId;
    }

    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public Date getTeenBirth() {
        return teenBirth;
    }

    public void setTeenBirth(Date teenBirth) {
        this.teenBirth = teenBirth;
    }

    public BigDecimal getHeightAfterTreatment() {
        return heightAfterTreatment;
    }

    public void setHeightAfterTreatment(BigDecimal heightAfterTreatment) {
        this.heightAfterTreatment = heightAfterTreatment;
    }

    public BigDecimal getWeightAfterTreatment() {
        return weightAfterTreatment;
    }

    public void setWeightAfterTreatment(BigDecimal weightAfterTreatment) {
        this.weightAfterTreatment = weightAfterTreatment;
    }

    // 计算年龄的方法（业务层调用）
    public void calculateAge() {
        if (this.teenBirth == null) {
            this.teenAge = null;
            return;
        }
        LocalDate birthDate = teenBirth.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        this.teenAge = (long) Period.between(birthDate, LocalDate.now()).getYears();
    }
}
