package com.ruoyi.teen.vo.ipad;

import com.ruoyi.common.annotation.Excel;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

public class PackageBuyTimesVo {

    /** 套餐的名称 */
    @Excel(name = "套餐的名称")
    @NotNull(message = "套餐名称不能为空")
    @Size(min = 0,max = 255,message = "套餐名称长度不能超过255个字符串")
    private String packageName;

    /*总次数*/
    private Long packageTotalTimes;
    /*已消费次数*/
    private Long packageNumberTimes;

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Long getPackageTotalTimes() {
        return packageTotalTimes;
    }

    public void setPackageTotalTimes(Long packageTotalTimes) {
        this.packageTotalTimes = packageTotalTimes;
    }

    public Long getPackageNumberTimes() {
        return packageNumberTimes;
    }

    public void setPackageNumberTimes(Long packageNumberTimes) {
        this.packageNumberTimes = packageNumberTimes;
    }
}
