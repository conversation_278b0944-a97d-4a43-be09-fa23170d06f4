package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

public class RlctTeenTwoVo {
    /** 孩子姓名 */
    private String teenName;
    /** 孩子年龄 */
    private Long teenAge;

    /** 孩子性别 */
    private String teenSex;

    /** 孩子出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date teenBirth;
    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phonenumber;
    /** 初诊身高 */
    private BigDecimal initHeight;
    /** 初诊体重 */
    private BigDecimal initWeight;
    /** 过敏史 */
    @Excel(name = "过敏史")
    private String allergyHistory;
    /** 父亲身高 */
    @Excel(name = "父亲身高")
    private BigDecimal fatherHeight;
    /** 母亲身高 */
    @Excel(name = "母亲身高")
    private BigDecimal motherHeight;
    /*套餐情况：剩余次数*/
    private Long remainingTimes;
    /*遗传身高*/
    private BigDecimal geneticHeight;
    /*治疗方案*/
    private String visitDetails;
    /*治疗后的身高*/
    private BigDecimal heightAfterTreatment;
    /*治疗后的体重*/
    private BigDecimal weightAfterTreatment;

    public BigDecimal getGeneticHeight() {
        return geneticHeight;
    }

    public void setGeneticHeight(BigDecimal geneticHeight) {
        this.geneticHeight = geneticHeight;
    }



    public BigDecimal getInitHeight() {
        return initHeight;
    }

    public void setInitHeight(BigDecimal initHeight) {
        this.initHeight = initHeight;
    }

    public BigDecimal getInitWeight() {
        return initWeight;
    }

    public void setInitWeight(BigDecimal initWeight) {
        this.initWeight = initWeight;
    }

    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public Long getTeenAge() {
        return teenAge;
    }

    public void setTeenAge(Long teenAge) {
        this.teenAge = teenAge;
    }

    public String getTeenSex() {
        return teenSex;
    }

    public void setTeenSex(String teenSex) {
        this.teenSex = teenSex;
    }

    public Date getTeenBirth() {
        return teenBirth;
    }

    public void setTeenBirth(Date teenBirth) {
        this.teenBirth = teenBirth;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getAllergyHistory() {
        return allergyHistory;
    }

    public void setAllergyHistory(String allergyHistory) {
        this.allergyHistory = allergyHistory;
    }

    public BigDecimal getFatherHeight() {
        return fatherHeight;
    }

    public void setFatherHeight(BigDecimal fatherHeight) {
        this.fatherHeight = fatherHeight;
    }

    public BigDecimal getMotherHeight() {
        return motherHeight;
    }

    public void setMotherHeight(BigDecimal motherHeight) {
        this.motherHeight = motherHeight;
    }

    public Long getRemainingTimes() {
        return remainingTimes;
    }

    public void setRemainingTimes(Long remainingTimes) {
        this.remainingTimes = remainingTimes;
    }

    public String getVisitDetails() {
        return visitDetails;
    }

    public void setVisitDetails(String visitDetails) {
        this.visitDetails = visitDetails;
    }

    public BigDecimal getHeightAfterTreatment() {
        return heightAfterTreatment;
    }

    public void setHeightAfterTreatment(BigDecimal heightAfterTreatment) {
        this.heightAfterTreatment = heightAfterTreatment;
    }

    public BigDecimal getWeightAfterTreatment() {
        return weightAfterTreatment;
    }

    public void setWeightAfterTreatment(BigDecimal weightAfterTreatment) {
        this.weightAfterTreatment = weightAfterTreatment;
    }
}
