package com.ruoyi.teen.vo.ipad;

import com.ruoyi.teen.domain.DailyHabitRecords;
import com.ruoyi.teen.domain.GrowthDevelopment;
import com.ruoyi.teen.domain.RecordOther;
import com.ruoyi.teen.domain.RlctTeenTwo;

public class RecordVo {
    private RlctTeenTwo rlctTeenTwo;

    private DailyHabitRecords dailyHabitRecords;

    private GrowthDevelopment growthDevelopment;

    private RecordOther recordOther;

    public GrowthDevelopment getGrowthDevelopment() {
        return growthDevelopment;
    }

    public void setGrowthDevelopment(GrowthDevelopment growthDevelopment) {
        this.growthDevelopment = growthDevelopment;
    }

    public RecordOther getRecordOther() {
        return recordOther;
    }

    public void setRecordOther(RecordOther recordOther) {
        this.recordOther = recordOther;
    }

    public DailyHabitRecords getDailyHabitRecords() {
        return dailyHabitRecords;
    }

    public void setDailyHabitRecords(DailyHabitRecords dailyHabitRecords) {
        this.dailyHabitRecords = dailyHabitRecords;
    }

    public RlctTeenTwo getRlctTeenTwo() {
        return rlctTeenTwo;
    }

    public void setRlctTeenTwo(RlctTeenTwo rlctTeenTwo) {
        this.rlctTeenTwo = rlctTeenTwo;
    }
}
