package com.ruoyi.teen.vo.pc;

import com.ruoyi.teen.domain.RlctUserPackage;

/**
 * @author: sanzhi
 * @date: 2023/12/11 16:46
 * 演示:
 */
public class RlctUserPackagePCVo extends RlctUserPackage {

    /*用户名*/
    public String userName;
    public String packageName;
    public String packageAuthorizationerNickName;//授权人昵称

    public String getPackageAuthorizationerNickName() {
        return packageAuthorizationerNickName;
    }

    public void setPackageAuthorizationerNickName(String packageAuthorizationerNickName) {
        this.packageAuthorizationerNickName = packageAuthorizationerNickName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    @Override
    public String toString() {
        return "RlctUserPackagePCVo{" +
                "userName='" + userName + '\'' +
                '}';
    }
}
