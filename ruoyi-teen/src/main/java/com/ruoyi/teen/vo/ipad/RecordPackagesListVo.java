package com.ruoyi.teen.vo.ipad;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import java.util.Date;

public class RecordPackagesListVo {
    private Long parentTeenId;
    private String teenName;
    /*年龄*/
    @Excel(name = "年龄")
    private Long teenAge;
    /*性别*/
    @Excel(name = "性别")
    private String teenSex;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date teenBirth;
    @Excel(name = "套餐数")
    private Long packageCount; // 用户购买套餐的总数
    /** 剩余套餐次数 */
    @Excel(name = "剩余套餐次数")
    private Long remainingTimes ;

    public Long getParentTeenId() {
        return parentTeenId;
    }

    public void setParentTeenId(Long parentTeenId) {
        this.parentTeenId = parentTeenId;
    }

    public Long getTeenAge() {
        return teenAge;
    }

    public void setTeenAge(Long teenAge) {
        this.teenAge = teenAge;
    }

    public String getTeenSex() {
        return teenSex;
    }

    public void setTeenSex(String teenSex) {
        this.teenSex = teenSex;
    }


    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public Date getTeenBirth() {
        return teenBirth;
    }

    public void setTeenBirth(Date teenBirth) {
        this.teenBirth = teenBirth;
    }

    public Long getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(Long packageCount) {
        this.packageCount = packageCount;
    }

    public Long getRemainingTimes() {
        return remainingTimes;
    }

    public void setRemainingTimes(Long remainingTimes) {
        this.remainingTimes = remainingTimes;
    }
}
