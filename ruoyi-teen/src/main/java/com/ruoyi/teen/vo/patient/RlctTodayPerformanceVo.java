package com.ruoyi.teen.vo.patient;

import java.math.BigDecimal;

public class RlctTodayPerformanceVo {
/*    *//*关节松解*//*
    private Long jointRelease;
    *//*指针点穴*//*
    private Long pointerAcupoints;
    *//*鼻炎刮痧*//*
    private Long noseScratch;
    *//*专项训练*//*
    private Long specialTraining;
    *//*艾灸*//*
    private Long aircure;
    *//*接诊人数*//*
    private Long receptionNumber;*/
    private Long treatmentType;         // 治疗类型
    private String treatmentTypeName;
    private BigDecimal count;

    public Long getTreatmentType() {
        return treatmentType;
    }

    public void setTreatmentType(Long treatmentType) {
        this.treatmentType = treatmentType;
    }

    public String getTreatmentTypeName() {
        return treatmentTypeName;
    }

    public void setTreatmentTypeName(String treatmentTypeName) {
        this.treatmentTypeName = treatmentTypeName;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }
}
