package com.ruoyi.teen.vo.physician;

import com.ruoyi.common.utils.StringUtils;

import java.util.List;

public class TreatmentImageListBean {
    private Long visitId;
    private List<TreatmentImageVo> images;

    public String toImages(){
        if(images == null || images.size() == 0){
            return null;
        }
        String res = null;
        for (TreatmentImageVo image : images) {
            if(res == null){
                res = image.getPathFileName();
            }else{
                res += "," + image.getPathFileName();
            }
        }
        return res;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public List<TreatmentImageVo> getImages() {
        return images;
    }

    public void setImages(List<TreatmentImageVo> images) {
        this.images = images;
    }
}
