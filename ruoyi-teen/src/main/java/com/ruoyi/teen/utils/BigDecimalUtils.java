package com.ruoyi.teen.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class BigDecimalUtils {

    /**
     * 保留两位小数（四舍五入）
     * @param bigDecimal
     * @return
     */
    public static BigDecimal setScale(BigDecimal bigDecimal){
        if(bigDecimal == null){
            return null;
        }else{
            return bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }


    /**
     * BigDecimal 格式化输出，最长保留两位小数(四舍五入)
     * 如：23.12654  --> 23.13
     * 如：23.1200  --> 23.12
     * 如：23.1000  --> 23.1
     * 如：23.0000  --> 23
     * 如：23  --> 23
     * @param bigDecimal
     * @return
     */
    public static String decimalFormat(BigDecimal bigDecimal){
        if(bigDecimal == null){
            bigDecimal = BigDecimal.ZERO;
        }
        bigDecimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
        DecimalFormat decimalFormat = new DecimalFormat("0.##");
        return decimalFormat.format(bigDecimal.doubleValue());
    }


    public static void main(String[] args) throws Exception{
//        System.out.println(decimalFormat(null));
//        System.out.println(decimalFormat(new BigDecimal("0")));
//        System.out.println(decimalFormat(new BigDecimal("23.12654")));
//        System.out.println(decimalFormat(new BigDecimal("23.1210")));
//        System.out.println(decimalFormat(new BigDecimal("23.1200")));
//        System.out.println(decimalFormat(new BigDecimal("23.1000")));
//        System.out.println(decimalFormat(new BigDecimal("23.0000")));
//        System.out.println(decimalFormat(new BigDecimal("23")));
//        System.out.println("-------------------------------");
//        System.out.println(decimalFormatTwo(null));
//        System.out.println(decimalFormatTwo(new BigDecimal("0")));
//        System.out.println(decimalFormatTwo(new BigDecimal("23.12654")));
//        System.out.println(decimalFormatTwo(new BigDecimal("23.1200")));
//        System.out.println(decimalFormatTwo(new BigDecimal("23.1000")));
//        System.out.println(decimalFormatTwo(new BigDecimal("23.0000")));
//        System.out.println(decimalFormatTwo(new BigDecimal("23")));

        System.out.println(calPercentage(1,1));
    }

    /**
     * BigDecimal 格式化输出，保留两位小数(四舍五入)
     * 如：23.12654  --> 23.13
     * 如：23.1200  --> 23.12
     * 如：23.1000  --> 23.10
     * 如：23.0000  --> 23.00
     * 如：23  --> 23.00
     * @param bigDecimal
     * @return
     */
    public static String decimalFormatTwo(BigDecimal bigDecimal){
        if(bigDecimal == null){
            bigDecimal = BigDecimal.ZERO;
        }
        bigDecimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(bigDecimal.doubleValue());
    }

    /**
     * 金额是否大于0
     * @param bigDecimal
     * @return
     */
    public static boolean greateZero(BigDecimal bigDecimal){
        if(bigDecimal == null){
            return false;
        }
        int res =bigDecimal.compareTo(new BigDecimal(0));
        if(res > 0){
            return true;
        }
        return false;
    }


    /**
     * 计算百分比
     * @return
     */
    public static BigDecimal calPercentage(Integer num,Integer total){
        if(num == null){
            num = 0;
        }
        if(total == null){
            total = 0;
        }
        return calPercentage(new BigDecimal(num),new BigDecimal(total));
    }


    /**
     * 计算百分比
     * @return
     */
    public static BigDecimal calPercentage(Long num,Long total){
        if(num == null){
            num = 0L;
        }
        if(total == null){
            total = 0L;
        }
        return calPercentage(new BigDecimal(num),new BigDecimal(total));
    }

    /**
     * 计算百分比
     * @return
     */
    public static BigDecimal calPercentage(BigDecimal num,BigDecimal total){
        if(num == null || total == null || total.compareTo(new BigDecimal(0)) == 0){
            return new BigDecimal(0);
        }
        num = num.multiply(new BigDecimal(100));
        BigDecimal satisfaction = num.divide(total,2,BigDecimal.ROUND_HALF_UP);
        return satisfaction;
    }
}
