package com.ruoyi.teen.utils;

import cn.hutool.core.io.FileUtil;
import com.ruoyi.teen.service.impl.RlctUserPackageServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

public class DownloadUtils {
    private static final Logger logger = LoggerFactory.getLogger(DownloadUtils.class);

    public static void downloadPdf(String filePath, HttpServletResponse response) {
        download(filePath,response,MediaType.APPLICATION_PDF_VALUE,null);
    }

    public static void downloadImage(String filePath, HttpServletResponse response) {
        download(filePath,response,MediaType.IMAGE_JPEG_VALUE,null);
    }

    /**
     * 下载图片
     * @param filePath  完整的本地图片地址
     * @param response
     */
    public static void download(String filePath, HttpServletResponse response,String contentType,String fileName) {
        try {
            response.setContentType(contentType);
            response.setCharacterEncoding("utf-8");
            if (StringUtils.isNotBlank(filePath)) {
                 response.setHeader("Content-Disposition", "attachment;filename=" + System.currentTimeMillis()
                         +"."+ FileUtil.getSuffix(filePath));
            }

            writeBytes(filePath, response.getOutputStream());
        } catch (Exception e) {
            logger.error("download error!",e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 输出指定文件的byte数组
     *
     * @param filePath 文件路径
     * @param os 输出流
     * @return
     */
    private static void writeBytes(String filePath, OutputStream os) throws IOException
    {
        FileInputStream fis = null;
        try
        {
            File file = new File(filePath);
            if (!file.exists())
            {
                throw new FileNotFoundException(filePath);
            }
            fis = new FileInputStream(file);
            byte[] b = new byte[1024];
            int length;
            while ((length = fis.read(b)) > 0)
            {
                os.write(b, 0, length);
            }
        }
        catch (IOException e)
        {
            throw e;
        }
        finally
        {
            os.close();
            fis.close();
        }
    }
}
