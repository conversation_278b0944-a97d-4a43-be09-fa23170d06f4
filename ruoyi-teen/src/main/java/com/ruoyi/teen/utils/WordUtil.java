package com.ruoyi.teen.utils;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;

import java.io.*;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 使用freemarker模板生成word文档
 * 模板的生成方法：word->导出->xml
 * 变量的使用${}
 * 引用：
 * 		<dependency>
 * 			<groupId>org.freemarker</groupId>
 * 			<artifactId>freemarker</artifactId>
 * 			<version>2.3.31</version>
 * 		</dependency>
 */
public class WordUtil {

    private static Configuration configuration = null;

    static {
        configuration = new Configuration(new Version("2.3.31"));
        configuration.setDefaultEncoding("UTF-8");
        configuration.setLocale(Locale.CHINESE);
        //configuration.setDirectoryForTemplateLoading(new File(templateFolder));
        configuration.setClassForTemplateLoading(WordUtil.class,"/template");
    }

    public static File createDoc(Map<String, Object> dataMap, String templateFileName,String wordFileName) throws IOException {
        Template template = configuration.getTemplate(templateFileName,"UTF-8");

        //当数据模型值为null时，模板解析会报错, 所以把null转为空串
        dataMap.forEach((key, value) -> {
            if(value == null){
                dataMap.put(key,"");
            }
        });
        Template t = template;
        Writer writer = null;
        File file = new File(wordFileName);
        try {
            writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file),"UTF-8"));
            t.process(dataMap, writer);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }finally{
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return file;
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();
        map.put("visitNo", "20240502122302632");


        try {
            WordUtil.createDoc(map
                    ,"diagnosisForm2.ftl",
                    "D:/test/test222.doc");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
