package com.ruoyi.teen.utils;

import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;


/**
 * word文档转成pdf
 * 推荐使用
 * 引用包：
 * <dependency>
 * 			<groupId>com.aspose.words</groupId>
 * 			<artifactId>aspose-words</artifactId>
 * 			<version>15.8.0-jdk16</version>
 * 			<scope>system</scope>
 * 			<systemPath>${project.basedir}/lib/aspose-words-15.8.0-jdk16.jar</systemPath>
 * 		</dependency>
 * @author: zhaojf
 * @Description TODO
 * @date 2022/3/30
 */
public class PDFUtils {
    private static final Logger logger = LoggerFactory.getLogger(PDFUtils.class);
    private static String separator = File.separator;//文件夹路径分格符

    public static String wordToPdf(String docFilePath) {
        File file = new File(docFilePath);
        if(!file.exists()){
            logger.error("word文档不存在，docFilePath="+docFilePath);
            throw new RuntimeException("word文档不存在");
        }
        return wordToPdf(file);
    }

    public static String wordToPdf(File file) {
        Document document = null;
        try {
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            ByteArrayInputStream is = new ByteArrayInputStream(s.getBytes());
            License license = new License();
            license.setLicense(is);
//            市场局172环境字体目录 /usr/share/fontconfig/unif
//            星云平台环境字体目录 /usr/share/fonts/unif
//            FontSettings.setFontsFolder("/usr/share/fonts/unif", true);
            String fileName = file.getPath();
            System.out.println(fileName);
            document = new Document(fileName);
            String resName = fileName;
            resName = resName.replace(".docx", ".pdf");
            resName = resName.replace(".doc", ".pdf");
            document.save(resName, SaveFormat.PDF);
            return resName;
        } catch (Exception e) {
            logger.error("wordToPdf error！path="+file.getPath(), e);
            return null;
        }
    }

    public static void main(String[] args) {
//        wordToPdf(new File("D:/test/文档格式.docx"));
//        String res = wordToPdf("D:/test/文档格式.docx");
//        String res = wordToPdf("D:/test/test111.doc");
        String res = wordToPdf("D:/test/青少儿生长发育中心诊疗记录单.docx");
        System.out.println(res);
    }

}
