package com.ruoyi.teen.utils;

import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.util.List;

/**
 * 参数校验工具
 */
public class ValidateUtils {

	public static String checkString(String value,String title) throws RuntimeException{
		return checkString(value, title, null, null,null,null);
	}
	public static String checkString(String value,String title,String defaultValue) throws RuntimeException{
		return checkString(value, title, defaultValue, null,null,null);
	}
	public static String checkString(String value,String title,String defaultValue,List<String> valueScole) throws RuntimeException{
		return checkString(value, title, defaultValue, valueScole,null,null);
	}
	public static String checkString(String value,String title,String defaultValue,Integer valueLength) throws RuntimeException{
		return checkString(value, title, defaultValue, null,valueLength,null);
	}
	public static String checkString(String value,String title,String defaultValue,Integer valueLength,Integer maxLength) throws RuntimeException{
		return checkString(value, title, defaultValue, null,valueLength,maxLength);
	}

	public static BigDecimal checkBigDecimal(BigDecimal value, String title) throws RuntimeException {
		return checkBigDecimal(value, title, null, null, null, null);
	}

	public static BigDecimal checkBigDecimal(BigDecimal value, String title, BigDecimal defaultValue) throws RuntimeException {
		return checkBigDecimal(value, title, defaultValue, null, null, null);
	}

	public static BigDecimal checkBigDecimal(BigDecimal value, String title, BigDecimal defaultValue, List<BigDecimal> valueScole) throws RuntimeException {
		return checkBigDecimal(value, title, defaultValue, valueScole, null, null);
	}

	public static BigDecimal checkBigDecimal(BigDecimal value, String title, BigDecimal defaultValue, Integer scale) throws RuntimeException {
		return checkBigDecimal(value, title, defaultValue, null, scale, null);
	}

	public static BigDecimal checkBigDecimal(BigDecimal value, String title, BigDecimal defaultValue, Integer scale, BigDecimal maxValue) throws RuntimeException {
		return checkBigDecimal(value, title, defaultValue, null, scale, maxValue);
	}


	/**
	 * 检验字符串
	 * @param value			校验的值
	 * @param title			关键字
	 * @param defaultValue	默认值，如果为null，则是必需值
	 * @param valueScole	取值范围
	 * @param valueLength	值的长度
	 * @param
	 * @throws RuntimeException
	 */
	public static String checkString(String value,String title,String defaultValue
			,List<String> valueScole,Integer valueLength,Integer maxLength) throws RuntimeException{
		if(StringUtils.isBlank(value)){
			if(defaultValue == null){
				throw new RuntimeException(title+"不能为空");
			}else{
				return defaultValue;
			}
		}else{
			if(valueScole != null && !valueScole.contains(value)){
				throw new RuntimeException(title+"取值范围不正确");
			}
			if(valueLength != null && valueLength > 0 && value.length() != valueLength){
				throw new RuntimeException(title+"字符长度不正确");
			}
			if(maxLength != null && maxLength > 0 && value.length() > maxLength){
				throw new RuntimeException(title+"字符长度不正确,最长"+maxLength);
			}
		}
		return value;
	}

	/**
	 * 检验BigDecimal类型的值
	 * @param value         待校验的值
	 * @param title         关键字
	 * @param defaultValue  默认值，如果为null，则是必需值
	 * @param valueScole    取值范围
	 * @param scale         值的小数位数
	 * @param maxValue      值的最大值
	 * @throws RuntimeException
	 */
	public static BigDecimal checkBigDecimal(BigDecimal value, String title, BigDecimal defaultValue
			, List<BigDecimal> valueScole, Integer scale, BigDecimal maxValue) throws RuntimeException {
		if (value == null) {
			if (defaultValue == null) {
				throw new RuntimeException(title + "不能为空");
			} else {
				return defaultValue;
			}
		} else {
			if (valueScole != null && !valueScole.contains(value)) {
				throw new RuntimeException(title + "取值范围不正确");
			}
			if (scale != null && scale >= 0 && value.scale() != scale) {
				throw new RuntimeException(title + "小数位数不正确");
			}
			if (maxValue != null && value.compareTo(maxValue) > 0) {
				throw new RuntimeException(title + "数值过大，最大值为" + maxValue);
			}
		}
		return value;
	}

	public static Long checkLong(Long value,String title,Long defaultValue
			,Long betweenStart,Long betweenEnd) throws RuntimeException{
		if(value == null){
			if(defaultValue == null){
				throw new RuntimeException(title+"不能为空");
			}else{
				return defaultValue;
			}
		}
		return checkLong(String.valueOf(value),title,defaultValue,betweenStart,betweenEnd);
	}

	public static Long checkLong(Long value,String title) throws RuntimeException{
		return checkLong(value,title,null,null,null);
	}

	public static Long checkLong(String value,String title) throws RuntimeException{
		return checkLong(value,title,null,null,null);
	}

	/**
	 *
	 * @param value				校验的值
	 * @param title				关键字
	 * @param defaultValue		默认值，如果为null，则是必需值
	 * @param betweenStart		范围最小值（包含）
	 * @param betweenEnd		范围最大值（包含）
	 * @return
	 * @throws RuntimeException
	 */
	public static Long checkLong(String value,String title,Long defaultValue
			,Long betweenStart,Long betweenEnd) throws RuntimeException{
		if(StringUtils.isBlank(value)){
			if(defaultValue == null){
				throw new RuntimeException(title+"不能为空");
			}else{
				return defaultValue;
			}
		}else{
			long valueL = 0L;
			try {
				valueL = Long.valueOf(value);
			} catch (Exception e) {
				throw new RuntimeException(title+"不正确");
			}

			if(betweenStart != null && valueL < betweenStart){
				throw new RuntimeException(title+"不正确，不能小于"+betweenStart);
			}
			if(betweenEnd != null && valueL > betweenEnd){
				throw new RuntimeException(title+"不正确，不能大于"+betweenEnd);
			}
			return valueL;
		}
	}

	public static Integer checkInteger(Integer value,String title) throws RuntimeException{
		if(value == null){
			throw new RuntimeException(title+"不能为空");
		}
		return checkInteger(value.toString(),title,null,null,null);
	}


	/**
	 *
	 * @param value				校验的值
	 * @param title				关键字
	 * @param defaultValue		默认值，如果为null，则是必需值
	 * @param betweenStart		范围最小值（包含）
	 * @param betweenEnd		范围最大值（包含）
	 * @return
	 * @throws RuntimeException
	 */
	public static Integer checkInteger(Integer value,String title,Integer defaultValue
			,Integer betweenStart,Integer betweenEnd) throws RuntimeException{
		if(value == null){
			if(defaultValue == null){
				throw new RuntimeException(title+"不能为空");
			}else{
				return defaultValue;
			}
		}
		return checkInteger(value.toString(),title,defaultValue,betweenStart,betweenEnd);
	}

	public static Integer checkInteger(String value,String title) throws RuntimeException{
		return checkInteger(value,title,null,null,null);
	}

	/**
	 *
	 * @param value				校验的值
	 * @param title				关键字
	 * @param defaultValue		默认值，如果为null，则是必需值
	 * @param betweenStart		范围最小值（包含）
	 * @param betweenEnd		范围最大值（包含）
	 * @return
	 * @throws RuntimeException
	 */
	public static Integer checkInteger(String value,String title,Integer defaultValue
			,Integer betweenStart,Integer betweenEnd) throws RuntimeException{
		if(StringUtils.isBlank(value)){
			if(defaultValue == null){
				throw new RuntimeException(title+"不能为空");
			}else{
				return defaultValue;
			}
		}else{
			int valueL = 0;
			try {
				valueL = Integer.valueOf(value);
			} catch (Exception e) {
				throw new RuntimeException(title+"不正确");
			}

			if(betweenStart != null && valueL < betweenStart){
				throw new RuntimeException(title+"不正确，不能小于"+betweenStart);
			}
			if(betweenEnd != null && valueL > betweenEnd){
				throw new RuntimeException(title+"不正确，不能大于"+betweenStart);
			}
			return valueL;
		}
	}

	public static void checkObject(Object value,String title) throws RuntimeException{
		if(value == null){
			throw new RuntimeException(title);
		}
	}

	public static void main(String[] args) throws RuntimeException {
		Long transactionAmountL = ValidateUtils.checkLong("00012", "transaction_amount", 0L,null,null);
		System.out.println(transactionAmountL);
	}

}
