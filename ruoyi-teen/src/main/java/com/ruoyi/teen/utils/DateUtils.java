package com.ruoyi.teen.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtils extends com.ruoyi.common.utils.DateUtils {

    //判断当日期是否是在startDate和endDate日期范围内，是返回true,否返回false
    public static boolean betweenCurDate(Date startDate, Date endDate) {
        if(startDate == null || endDate == null){
            return false;
        }
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);
        startCal.set(Calendar.HOUR,0);
        startCal.set(Calendar.MINUTE,0);
        startCal.set(Calendar.SECOND,0);
        startCal.set(Calendar.MILLISECOND,0);
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);
        endCal.set(Calendar.HOUR,0);
        endCal.set(Calendar.MINUTE,0);
        endCal.set(Calendar.SECOND,0);
        endCal.set(Calendar.MILLISECOND,0);
        Calendar currentCal = Calendar.getInstance();
        currentCal.set(Calendar.HOUR,0);
        currentCal.set(Calendar.MINUTE,0);
        currentCal.set(Calendar.SECOND,0);
        currentCal.set(Calendar.MILLISECOND,0);
        return currentCal.compareTo(startCal) >= 0 && currentCal.compareTo(endCal) <=0;
    }


    //判断date1,date2日期的大小，date1>date2返回1，date1<date2返回-1，date1=date2返回0
    public static int betweenDate(Date date1, Date date2) {
        if(date1 == null || date2 == null){
            return 0;
        }
        Calendar date1Cal = Calendar.getInstance();
        date1Cal.setTime(date1);
        date1Cal.set(Calendar.HOUR,0);
        date1Cal.set(Calendar.MINUTE,0);
        date1Cal.set(Calendar.SECOND,0);
        date1Cal.set(Calendar.MILLISECOND,0);
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(date2);
        endCal.set(Calendar.HOUR,0);
        endCal.set(Calendar.MINUTE,0);
        endCal.set(Calendar.SECOND,0);
        endCal.set(Calendar.MILLISECOND,0);
        return date1Cal.compareTo(endCal);
    }

    public static String getDateString(Date date){
        if(date==null){
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");
        String curdate = format.format(date);
        return curdate;
    }

    public static String getDateString(Date date,String pattern){
        if(date==null){
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        String curdate = format.format(date);
        return curdate;
    }

    public static Date parseDate(String dateStr,String pattern){
        try{
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            return format.parse(dateStr);
        }catch (Exception e){
            return null;
        }
    }


    /**
     * 根据生日计算当前周岁数
     */
    public static int getCurrentAge(Date birthday) {
        // 当前时间
        Calendar curr = Calendar.getInstance();
        // 生日
        Calendar born = Calendar.getInstance();
        born.setTime(birthday);
        // 年龄 = 当前年 - 出生年
        int age = curr.get(Calendar.YEAR) - born.get(Calendar.YEAR);
        if (age <= 0) {
            return 0;
        }
        // 如果当前月份小于出生月份: age-1
        // 如果当前月份等于出生月份, 且当前日小于出生日: age-1
        int currMonth = curr.get(Calendar.MONTH);
        int currDay = curr.get(Calendar.DAY_OF_MONTH);
        int bornMonth = born.get(Calendar.MONTH);
        int bornDay = born.get(Calendar.DAY_OF_MONTH);
        if ((currMonth < bornMonth) || (currMonth == bornMonth && currDay <= bornDay)) {
            age--;
        }
        return age < 0 ? 0 : age;
    }
}
