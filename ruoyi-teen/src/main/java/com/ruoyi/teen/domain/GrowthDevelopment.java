package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 成长发育情况对象 growth_development
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public class GrowthDevelopment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 成长ID */
    private Long growth;

    /** 近两年长高 */
    @Excel(name = "近两年长高")
    private BigDecimal heightTwoYear;

    /** 近一年(半年)长高 */
    @Excel(name = "近一年(半年)长高")
    private BigDecimal heightOneYear;

    /** 通过半年治疗期望长高 */
    @Excel(name = "通过半年治疗期望长高")
    private BigDecimal expectedHeightSixMonth;

    /** 体毛 */
    @Excel(name = "体毛")
    private String bodyHair;

    /** 喉结 */
    @Excel(name = "喉结")
    private String adamApple;

    /** 变声 */
    @Excel(name = "变声")
    private String voiceChange;

    /** 月经 */
    @Excel(name = "月经")
    private String menstruation;

    /** 乳房 */
    @Excel(name = "乳房")
    private String breastDevelopment;

    /** 腋毛 */
    @Excel(name = "腋毛")
    private String underarmHair;

    /** 父母孩子关系ID */
    @Excel(name = "父母孩子关系ID")
    private Long parentTeenId;

    public void setGrowth(Long growth)
    {
        this.growth = growth;
    }

    public Long getGrowth()
    {
        return growth;
    }
    public void setHeightTwoYear(BigDecimal heightTwoYear)
    {
        this.heightTwoYear = heightTwoYear;
    }

    public BigDecimal getHeightTwoYear()
    {
        return heightTwoYear;
    }
    public void setHeightOneYear(BigDecimal heightOneYear)
    {
        this.heightOneYear = heightOneYear;
    }

    public BigDecimal getHeightOneYear()
    {
        return heightOneYear;
    }
    public void setExpectedHeightSixMonth(BigDecimal expectedHeightSixMonth)
    {
        this.expectedHeightSixMonth = expectedHeightSixMonth;
    }

    public BigDecimal getExpectedHeightSixMonth()
    {
        return expectedHeightSixMonth;
    }
    public void setBodyHair(String bodyHair)
    {
        this.bodyHair = bodyHair;
    }

    public String getBodyHair()
    {
        return bodyHair;
    }
    public void setAdamApple(String adamApple)
    {
        this.adamApple = adamApple;
    }

    public String getAdamApple()
    {
        return adamApple;
    }
    public void setVoiceChange(String voiceChange)
    {
        this.voiceChange = voiceChange;
    }

    public String getVoiceChange()
    {
        return voiceChange;
    }
    public void setMenstruation(String menstruation)
    {
        this.menstruation = menstruation;
    }

    public String getMenstruation()
    {
        return menstruation;
    }
    public void setBreastDevelopment(String breastDevelopment)
    {
        this.breastDevelopment = breastDevelopment;
    }

    public String getBreastDevelopment()
    {
        return breastDevelopment;
    }
    public void setUnderarmHair(String underarmHair)
    {
        this.underarmHair = underarmHair;
    }

    public String getUnderarmHair()
    {
        return underarmHair;
    }
    public void setParentTeenId(Long parentTeenId)
    {
        this.parentTeenId = parentTeenId;
    }

    public Long getParentTeenId()
    {
        return parentTeenId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("growth", getGrowth())
            .append("heightTwoYear", getHeightTwoYear())
            .append("heightOneYear", getHeightOneYear())
            .append("expectedHeightSixMonth", getExpectedHeightSixMonth())
            .append("bodyHair", getBodyHair())
            .append("adamApple", getAdamApple())
            .append("voiceChange", getVoiceChange())
            .append("menstruation", getMenstruation())
            .append("breastDevelopment", getBreastDevelopment())
            .append("underarmHair", getUnderarmHair())
            .append("parentTeenId", getParentTeenId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
