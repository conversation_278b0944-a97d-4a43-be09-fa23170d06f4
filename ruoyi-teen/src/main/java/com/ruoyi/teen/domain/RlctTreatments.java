package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目对象 rlct_treatments
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctTreatments extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 项目主键 */
    private Long treatmentId;

    /** 理疗项目的名称 */
    @Excel(name = "理疗项目的名称")
    private String treatmentName;

    /** 理疗项目的描述，包括治疗方法、效果等信息 */
    @Excel(name = "理疗项目的描述，包括治疗方法、效果等信息")
    private String treatmentDescription;

    /** 理疗项目的价格 */
    @Excel(name = "理疗项目的价格")
    private BigDecimal treatmentPrice;

    /** 理疗项目的图片 */
    @Excel(name = "理疗项目的图片")
    private String treatmentImageUrl;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setTreatmentId(Long treatmentId) 
    {
        this.treatmentId = treatmentId;
    }

    public Long getTreatmentId() 
    {
        return treatmentId;
    }
    public void setTreatmentName(String treatmentName) 
    {
        this.treatmentName = treatmentName;
    }

    public String getTreatmentName() 
    {
        return treatmentName;
    }
    public void setTreatmentDescription(String treatmentDescription) 
    {
        this.treatmentDescription = treatmentDescription;
    }

    public String getTreatmentDescription() 
    {
        return treatmentDescription;
    }
    public void setTreatmentPrice(BigDecimal treatmentPrice) 
    {
        this.treatmentPrice = treatmentPrice;
    }

    public BigDecimal getTreatmentPrice() 
    {
        return treatmentPrice;
    }
    public void setTreatmentImageUrl(String treatmentImageUrl) 
    {
        this.treatmentImageUrl = treatmentImageUrl;
    }

    public String getTreatmentImageUrl() 
    {
        return treatmentImageUrl;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("treatmentId", getTreatmentId())
            .append("treatmentName", getTreatmentName())
            .append("treatmentDescription", getTreatmentDescription())
            .append("treatmentPrice", getTreatmentPrice())
            .append("treatmentImageUrl", getTreatmentImageUrl())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
