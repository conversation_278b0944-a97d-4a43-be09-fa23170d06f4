package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.teen.vo.ipad.TreatmentVo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 就诊记录对象 rlct_visit_two
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public class RlctVisitTwo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，唯一标识每个就诊记录的ID */
    private Long visitId;

    /** 文本字段，用于存储就诊的详细信息，如诊断、处方等 */
    @Excel(name = "文本字段，用于存储就诊的详细信息，如诊断、处方等")
    private String visitDetails;

    /** 父母孩子关系id */
    @Excel(name = "父母孩子关系id")
    private Long parentTeenId;

    /** 治疗师id */
    @Excel(name = "治疗师id")
    private Long userId;

    /** 扣款账户(父母的id) */
    @Excel(name = "扣款账户(父母的id)")
    private String deductParentId;

    /** 用户套餐ID */
    @Excel(name = "用户套餐ID")
    private Long userPackageId;

    /** 套餐主键 */
    @Excel(name = "套餐主键")
    private Long packageId;

    /** 存储治疗前的图片上传的URL */
    @Excel(name = "存储治疗前的图片上传的URL")
    private String beforeTreatmentImageUrl;

    /** 存储治疗后的图片上传的URL */
    @Excel(name = "存储治疗后的图片上传的URL")
    private String afterTreatmentImageUrl;


    /** 状态(0治疗中1完成治疗)完成治疗后插入消费表 */
    @Excel(name = "状态(字典rlct_visit_status 0治疗中1完成治疗)完成治疗后插入消费表")
    private Long status;

    /** 完成治疗时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "完成治疗时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date healTime;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 编号 */
    @Excel(name = "编号")
    private String visitNo;

    /** 接诊时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "接诊时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date admissionTime;

    /** 存储治疗前身高 */
    @Excel(name = "存储治疗前身高")
    private BigDecimal heightBeforeTreatment;

    /** 存储治疗后身高 */
    @Excel(name = "存储治疗后身高")
    private BigDecimal heightAfterTreatment;

    /** 存储治疗前体重 */
    @Excel(name = "存储治疗前体重")
    private BigDecimal weightBeforeTreatment;

    /** 存储治疗后体重 */
    @Excel(name = "存储治疗后体重")
    private BigDecimal weightAfterTreatment;





    public void setVisitId(Long visitId)
    {
        this.visitId = visitId;
    }

    public Long getVisitId()
    {
        return visitId;
    }
    public void setVisitDetails(String visitDetails)
    {
        this.visitDetails = visitDetails;
    }

    public String getVisitDetails()
    {
        return visitDetails;
    }
    public void setParentTeenId(Long parentTeenId)
    {
        this.parentTeenId = parentTeenId;
    }

    public Long getParentTeenId()
    {
        return parentTeenId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setDeductParentId(String deductParentId)
    {
        this.deductParentId = deductParentId;
    }

    public String getDeductParentId()
    {
        return deductParentId;
    }
    public void setUserPackageId(Long userPackageId)
    {
        this.userPackageId = userPackageId;
    }

    public Long getUserPackageId()
    {
        return userPackageId;
    }
    public void setPackageId(Long packageId)
    {
        this.packageId = packageId;
    }

    public Long getPackageId()
    {
        return packageId;
    }
    public void setBeforeTreatmentImageUrl(String beforeTreatmentImageUrl)
    {
        this.beforeTreatmentImageUrl = beforeTreatmentImageUrl;
    }

    public String getBeforeTreatmentImageUrl()
    {
        return beforeTreatmentImageUrl;
    }
    public void setAfterTreatmentImageUrl(String afterTreatmentImageUrl)
    {
        this.afterTreatmentImageUrl = afterTreatmentImageUrl;
    }

    public String getAfterTreatmentImageUrl()
    {
        return afterTreatmentImageUrl;
    }
    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }
    public void setHealTime(Date healTime)
    {
        this.healTime = healTime;
    }

    public Date getHealTime()
    {
        return healTime;
    }
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }
    public void setVisitNo(String visitNo)
    {
        this.visitNo = visitNo;
    }

    public String getVisitNo()
    {
        return visitNo;
    }
    public void setAdmissionTime(Date admissionTime)
    {
        this.admissionTime = admissionTime;
    }

    public Date getAdmissionTime()
    {
        return admissionTime;
    }
    public void setHeightBeforeTreatment(BigDecimal heightBeforeTreatment)
    {
        this.heightBeforeTreatment = heightBeforeTreatment;
    }

    public BigDecimal getHeightBeforeTreatment()
    {
        return heightBeforeTreatment;
    }
    public void setHeightAfterTreatment(BigDecimal heightAfterTreatment)
    {
        this.heightAfterTreatment = heightAfterTreatment;
    }

    public BigDecimal getHeightAfterTreatment()
    {
        return heightAfterTreatment;
    }
    public void setWeightBeforeTreatment(BigDecimal weightBeforeTreatment)
    {
        this.weightBeforeTreatment = weightBeforeTreatment;
    }

    public BigDecimal getWeightBeforeTreatment()
    {
        return weightBeforeTreatment;
    }
    public void setWeightAfterTreatment(BigDecimal weightAfterTreatment)
    {
        this.weightAfterTreatment = weightAfterTreatment;
    }

    public BigDecimal getWeightAfterTreatment()
    {
        return weightAfterTreatment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("visitId", getVisitId())
            .append("visitDetails", getVisitDetails())
            .append("parentTeenId", getParentTeenId())
            .append("userId", getUserId())
            .append("deductParentId", getDeductParentId())
            .append("userPackageId", getUserPackageId())
            .append("packageId", getPackageId())
            .append("beforeTreatmentImageUrl", getBeforeTreatmentImageUrl())
            .append("afterTreatmentImageUrl", getAfterTreatmentImageUrl())
            .append("status", getStatus())
            .append("healTime", getHealTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("deptId", getDeptId())
            .append("visitNo", getVisitNo())
            .append("admissionTime", getAdmissionTime())
            .append("heightBeforeTreatment", getHeightBeforeTreatment())
            .append("heightAfterTreatment", getHeightAfterTreatment())
            .append("weightBeforeTreatment", getWeightBeforeTreatment())
            .append("weightAfterTreatment", getWeightAfterTreatment())
            .toString();
    }
}
