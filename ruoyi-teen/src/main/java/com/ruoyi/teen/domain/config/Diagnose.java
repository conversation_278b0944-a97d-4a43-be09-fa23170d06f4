package com.ruoyi.teen.domain.config;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class Diagnose {
    @NotNull(message = "用户套餐Id为空")
    private Long userPackageId; /** 用户套餐Id */
    @NotNull(message = "父母id为空")
    private Long patientId;///** 父母id */
    @NotNull(message = "患者id为空")
    private Long teenId;///**孩子id */

    @NotNull(message = "身高不能为空")
    private BigDecimal height;//身高
    @NotNull(message = "体重不能为空")
    private BigDecimal weight;//体重

    @NotNull(message = "睡眠习惯不能为空")
    private Long sleep;//睡眠习惯
    @NotNull(message = "睡眠时长不能为空")
    private Long duration;//睡眠时长
    @NotNull(message = "跳绳不能为空")
    private Long jump;//跳绳
    @NotNull(message = "摸高跳不能为空")
    private Long heightJump;//摸高跳
    @NotNull(message = "吊单杠不能为空")
    private Long bar;//吊单杠
    @NotNull(message = "奶类不能为空")
    private Long milk;//奶类
    @NotNull(message = "蛋类不能为空")
    private Long egg;//蛋类
    @NotNull(message = "蔬菜不能为空")
    private Long vegetable;//蔬菜
    @NotNull(message = "肉类不能为空")
    private Long raw;//肉类
    @NotNull(message = "五谷杂粮不能为空")
    private Long grain;//五谷杂粮
    @NotNull(message = "饮料零食不能为空")
    private Long snacks;//饮料零食
    @NotNull(message = "吃宵夜不能为空")
    private Long eatSnack;//吃宵夜
    @NotNull(message = "维生素A不能为空")
    private Long vAt;//维生素A
    @NotNull(message = "维生素D不能为空")
    private Long vDt;//维生素D
    @NotNull(message = "钙不能为空")
    private Long ca;//钙
    @NotNull(message = "综合维生素不能为空")
    private Long synthesis;//综合维生素
    @NotNull(message = "氨基酸不能为空")
    private Long acid;//氨基酸




    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public Long getTeenId() {
        return teenId;
    }

    public void setTeenId(Long teenId) {
        this.teenId = teenId;
    }

    public Long getUserPackageId() {
        return userPackageId;
    }

    public void setUserPackageId(Long userPackageId) {
        this.userPackageId = userPackageId;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public Long getSleep() {
        return sleep;
    }

    public void setSleep(Long sleep) {
        this.sleep = sleep;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Long getJump() {
        return jump;
    }

    public void setJump(Long jump) {
        this.jump = jump;
    }

    public Long getHeightJump() {
        return heightJump;
    }

    public void setHeightJump(Long heightJump) {
        this.heightJump = heightJump;
    }

    public Long getBar() {
        return bar;
    }

    public void setBar(Long bar) {
        this.bar = bar;
    }

    public Long getMilk() {
        return milk;
    }

    public void setMilk(Long milk) {
        this.milk = milk;
    }

    public Long getEgg() {
        return egg;
    }

    public void setEgg(Long egg) {
        this.egg = egg;
    }

    public Long getVegetable() {
        return vegetable;
    }

    public void setVegetable(Long vegetable) {
        this.vegetable = vegetable;
    }

    public Long getRaw() {
        return raw;
    }

    public void setRaw(Long raw) {
        this.raw = raw;
    }

    public Long getGrain() {
        return grain;
    }

    public void setGrain(Long grain) {
        this.grain = grain;
    }

    public Long getSnacks() {
        return snacks;
    }

    public void setSnacks(Long snacks) {
        this.snacks = snacks;
    }

    public Long getEatSnack() {
        return eatSnack;
    }

    public void setEatSnack(Long eatSnack) {
        this.eatSnack = eatSnack;
    }

    public Long getvAt() {
        return vAt;
    }

    public void setvAt(Long vAt) {
        this.vAt = vAt;
    }

    public Long getvDt() {
        return vDt;
    }

    public void setvDt(Long vDt) {
        this.vDt = vDt;
    }

    public Long getCa() {
        return ca;
    }

    public void setCa(Long ca) {
        this.ca = ca;
    }

    public Long getSynthesis() {
        return synthesis;
    }

    public void setSynthesis(Long synthesis) {
        this.synthesis = synthesis;
    }

    public Long getAcid() {
        return acid;
    }

    public void setAcid(Long acid) {
        this.acid = acid;
    }
}
