package com.ruoyi.teen.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 产品广告对象 rlct_product
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 产品id */
    private Long productId;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 产品图片 */
    @Excel(name = "产品图片")
    private String productImageUrl;

    /** 产品详情 */
    @Excel(name = "产品详情")
    private String productDetails;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }
    public void setProductImageUrl(String productImageUrl) 
    {
        this.productImageUrl = productImageUrl;
    }

    public String getProductImageUrl() 
    {
        return productImageUrl;
    }
    public void setProductDetails(String productDetails) 
    {
        this.productDetails = productDetails;
    }

    public String getProductDetails() 
    {
        return productDetails;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("productId", getProductId())
            .append("productName", getProductName())
            .append("productImageUrl", getProductImageUrl())
            .append("productDetails", getProductDetails())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
