package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.swing.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * 孩子对象 rlct_teen
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctTeen extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 孩子ID */
    private Long teenId;

    /** 第一次创建的患者的家长id */
    private Long parentId;

    /** 孩子姓名 */
    @Excel(name = "孩子姓名")
    private String teenName;
    @Excel(name = "孩子姓名首字母拼音")
    private String teenNameCh;

    /** 孩子年龄 */
    @Excel(name = "孩子年龄")
    private Long teenAge;

    /** 孩子头像 */
    @Excel(name = "孩子头像")
    private String teenAvatar;

    /** 孩子性别 */
    @Excel(name = "孩子性别")
    private String teenSex;

    /** 孩子出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "孩子出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date teenBirth;

    /** 孩子入睡时间分数 */
    @Excel(name = "孩子入睡时间分数")
    private BigDecimal teenSleepTime;

    /** 孩子睡眠时长分数 */
    @Excel(name = "孩子睡眠时长分数")
    private BigDecimal teenSleepDurationTime;

    /** 运动习惯分数 */
    @Excel(name = "运动习惯分数")
    private BigDecimal teenExerciseHabit;


    /** 营养素分数 */
    @Excel(name = "营养素分数")
    private BigDecimal teenNutrient;

    /** 饮食习惯分数 */
    @Excel(name = "饮食习惯分数")
    private BigDecimal teenEatHabit;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    private BigDecimal initHeight;//初诊身高
    private BigDecimal initWeight;//初诊体重
    private Date initTime;//初诊时间

    public String getTeenNameCh() {
        return teenNameCh;
    }

    public void setTeenNameCh(String teenNameCh) {
        this.teenNameCh = teenNameCh;
    }

    public Date getInitTime() {
        return initTime;
    }

    public void setInitTime(Date initTime) {
        this.initTime = initTime;
    }

    public BigDecimal getInitWeight() {
        return initWeight;
    }

    public void setInitWeight(BigDecimal initWeight) {
        this.initWeight = initWeight;
    }

    public BigDecimal getInitHeight() {
        return initHeight;
    }

    public void setInitHeight(BigDecimal initHeight) {
        this.initHeight = initHeight;
    }


    public void setTeenId(Long teenId)
    {
        this.teenId = teenId;
    }

    public Long getTeenId()
    {
        return teenId;
    }
    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }
    public void setTeenName(String teenName)
    {
        this.teenName = teenName;
    }

    public String getTeenName()
    {
        return teenName;
    }
    public void setTeenAge(Long teenAge)
    {
        this.teenAge = teenAge;
    }

    public Long getTeenAge()
    {
        return teenAge;
    }
    public void setTeenAvatar(String teenAvatar)
    {
        this.teenAvatar = teenAvatar;
    }

    public String getTeenAvatar()
    {
        return teenAvatar;
    }
    public void setTeenSex(String teenSex)
    {
        this.teenSex = teenSex;
    }

    public String getTeenSex()
    {
        return teenSex;
    }
    public void setTeenBirth(Date teenBirth)
    {
        this.teenBirth = teenBirth;
    }

    public Date getTeenBirth()
    {
        return teenBirth;
    }
    public void setTeenSleepTime(BigDecimal teenSleepTime)
    {
        this.teenSleepTime = teenSleepTime;
    }

    public BigDecimal getTeenSleepTime()
    {
        return teenSleepTime;
    }
    public void setTeenSleepDurationTime(BigDecimal teenSleepDurationTime)
    {
        this.teenSleepDurationTime = teenSleepDurationTime;
    }

    public BigDecimal getTeenSleepDurationTime()
    {
        return teenSleepDurationTime;
    }
    public void setTeenExerciseHabit(BigDecimal teenExerciseHabit)
    {
        this.teenExerciseHabit = teenExerciseHabit;
    }

    public BigDecimal getTeenExerciseHabit()
    {
        return teenExerciseHabit;
    }
    public void setTeenEatHabit(BigDecimal teenEatHabit)
    {
        this.teenEatHabit = teenEatHabit;
    }

    public BigDecimal getTeenEatHabit()
    {
        return teenEatHabit;
    }
    public void setTeenNutrient(BigDecimal teenNutrient)
    {
        this.teenNutrient = teenNutrient;
    }

    public BigDecimal getTeenNutrient()
    {
        return teenNutrient;
    }
    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("teenId", getTeenId())
            .append("parentId", getParentId())
            .append("teenName", getTeenName())
            .append("teenAge", getTeenAge())
            .append("teenAvatar", getTeenAvatar())
            .append("teenSex", getTeenSex())
            .append("teenBirth", getTeenBirth())
            .append("teenSleepTime", getTeenSleepTime())
            .append("teenSleepDurationTime", getTeenSleepDurationTime())
            .append("teenExerciseHabit", getTeenExerciseHabit())
            .append("teenEatHabit", getTeenEatHabit())
            .append("teenNutrient", getTeenNutrient())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
