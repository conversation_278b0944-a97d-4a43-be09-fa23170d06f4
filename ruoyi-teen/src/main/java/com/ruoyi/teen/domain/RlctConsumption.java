package com.ruoyi.teen.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 消费记录对象 rlct_consumption
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctConsumption extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，唯一标识每条消费记录的ID */
    private Long id;

    /** 标识消费的用户id */
    private Long userId;

    /** 标示患者id */
    private Long teenId;

    /** 标识该用户消费的套餐id */
    private Long userPackageId;

    /** 标识医生id */
    private Long dockerId;

    /** 标识消费的医馆 */
    private Long clinicId;

    /** 消费时间 */
    private Date consumeTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setTeenId(Long teenId) 
    {
        this.teenId = teenId;
    }

    public Long getTeenId() 
    {
        return teenId;
    }
    public void setUserPackageId(Long userPackageId) 
    {
        this.userPackageId = userPackageId;
    }

    public Long getUserPackageId() 
    {
        return userPackageId;
    }
    public void setDockerId(Long dockerId) 
    {
        this.dockerId = dockerId;
    }

    public Long getDockerId() 
    {
        return dockerId;
    }
    public void setClinicId(Long clinicId) 
    {
        this.clinicId = clinicId;
    }

    public Long getClinicId() 
    {
        return clinicId;
    }
    public void setConsumeTime(Date consumeTime) 
    {
        this.consumeTime = consumeTime;
    }

    public Date getConsumeTime() 
    {
        return consumeTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("teenId", getTeenId())
            .append("userPackageId", getUserPackageId())
            .append("dockerId", getDockerId())
            .append("clinicId", getClinicId())
            .append("consumeTime", getConsumeTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
