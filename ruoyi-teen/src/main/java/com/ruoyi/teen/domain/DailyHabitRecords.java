package com.ruoyi.teen.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 日常行为习惯登记对象 daily_habit_records
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public class DailyHabitRecords extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 行为习惯ID */
    private Long habitId;

    /** 父母孩子关系ID */
    @Excel(name = "父母孩子关系ID")
    private Long parentTeenId;

    /** 就诊id*/
    private Long visitId;
    /** 入睡时间 */
    @Excel(name = "入睡时间")
    private String bedtime;

    /** 睡眠时长 */
    @Excel(name = "睡眠时长")
    private String sleepDuration;

    /** 睡眠其它 */
    @Excel(name = "睡眠其它")
    private String sleepOther;

    /** 运动频率 */
    @Excel(name = "运动频率")
    private String exerciseFrequency;

    /** 运动时长 */
    @Excel(name = "运动时长")
    private String exerciseDuration;

    /** 运动其它 */
    @Excel(name = "运动其它")
    private String exerciseOther;

    /** 饮食习惯（字典：rlct_eating_habits,0 荤多菜少 1荤少菜多） */
    @Excel(name = "饮食习惯", readConverterExp = "字=典：rlct_eating_habits,0,荤=多菜少,1=荤少菜多")
    private String eatingHabits;

    /** 饮食其它 */
    @Excel(name = "饮食其它")
    private String eatingOther;

    /** 钙(字典rlct_have_nothave  Y是 N否    ) */
    @Excel(name = "钙(字典rlct_have_nothave  Y是 N否    )")
    private String calcium;

    /** 维生素A（字典rlct_have_nothave Y是 N否） */
    @Excel(name = "维生素A", readConverterExp = "字=典rlct_have_nothave,Y=是,N=否")
    private String vitaminA;

    /** 维生素D(字典rlct_have_nothave  Y是 N否    ) */
    @Excel(name = "维生素D(字典rlct_have_nothave  Y是 N否    )")
    private String vitaminD;

    /** 复合维生素(字典rlct_have_nothave  Y是 N否    ) */
    @Excel(name = "复合维生素(字典rlct_have_nothave  Y是 N否    )")
    private String multivitamin;

    /** 氨基丁酸(字典rlct_have_nothave  Y是 N否    ) */
    @Excel(name = "氨基丁酸(字典rlct_have_nothave  Y是 N否    )")
    private String gaba;

    /** 营养素其它 */
    @Excel(name = "营养素其它")
    private String nutritionOther;

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public void setHabitId(Long habitId)
    {
        this.habitId = habitId;
    }

    public Long getHabitId()
    {
        return habitId;
    }
    public void setParentTeenId(Long parentTeenId)
    {
        this.parentTeenId = parentTeenId;
    }

    public Long getParentTeenId()
    {
        return parentTeenId;
    }
    public void setBedtime(String bedtime)
    {
        this.bedtime = bedtime;
    }

    public String getBedtime()
    {
        return bedtime;
    }
    public void setSleepDuration(String sleepDuration)
    {
        this.sleepDuration = sleepDuration;
    }

    public String getSleepDuration()
    {
        return sleepDuration;
    }
    public void setSleepOther(String sleepOther)
    {
        this.sleepOther = sleepOther;
    }

    public String getSleepOther()
    {
        return sleepOther;
    }
    public void setExerciseFrequency(String exerciseFrequency)
    {
        this.exerciseFrequency = exerciseFrequency;
    }

    public String getExerciseFrequency()
    {
        return exerciseFrequency;
    }
    public void setExerciseDuration(String exerciseDuration)
    {
        this.exerciseDuration = exerciseDuration;
    }

    public String getExerciseDuration()
    {
        return exerciseDuration;
    }
    public void setExerciseOther(String exerciseOther)
    {
        this.exerciseOther = exerciseOther;
    }

    public String getExerciseOther()
    {
        return exerciseOther;
    }
    public void setEatingHabits(String eatingHabits)
    {
        this.eatingHabits = eatingHabits;
    }

    public String getEatingHabits()
    {
        return eatingHabits;
    }
    public void setEatingOther(String eatingOther)
    {
        this.eatingOther = eatingOther;
    }

    public String getEatingOther()
    {
        return eatingOther;
    }
    public void setCalcium(String calcium)
    {
        this.calcium = calcium;
    }

    public String getCalcium()
    {
        return calcium;
    }
    public void setVitaminA(String vitaminA)
    {
        this.vitaminA = vitaminA;
    }

    public String getVitaminA()
    {
        return vitaminA;
    }
    public void setVitaminD(String vitaminD)
    {
        this.vitaminD = vitaminD;
    }

    public String getVitaminD()
    {
        return vitaminD;
    }
    public void setMultivitamin(String multivitamin)
    {
        this.multivitamin = multivitamin;
    }

    public String getMultivitamin()
    {
        return multivitamin;
    }
    public void setGaba(String gaba)
    {
        this.gaba = gaba;
    }

    public String getGaba()
    {
        return gaba;
    }
    public void setNutritionOther(String nutritionOther)
    {
        this.nutritionOther = nutritionOther;
    }

    public String getNutritionOther()
    {
        return nutritionOther;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("habitId", getHabitId())
            .append("parentTeenId", getParentTeenId())
            .append("bedtime", getBedtime())
            .append("sleepDuration", getSleepDuration())
            .append("sleepOther", getSleepOther())
            .append("exerciseFrequency", getExerciseFrequency())
            .append("exerciseDuration", getExerciseDuration())
            .append("exerciseOther", getExerciseOther())
            .append("eatingHabits", getEatingHabits())
            .append("eatingOther", getEatingOther())
            .append("calcium", getCalcium())
            .append("vitaminA", getVitaminA())
            .append("vitaminD", getVitaminD())
            .append("multivitamin", getMultivitamin())
            .append("gaba", getGaba())
            .append("nutritionOther", getNutritionOther())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
