package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 就诊记录对象 rlct_visit
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctVisit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，唯一标识每个就诊记录的ID */
    @TableId(value = "visit_id", type = IdType.AUTO)
    private Long visitId;

    /** 文本字段，用于存储就诊的详细信息，如诊断、处方等 */
    @Excel(name = "文本字段，用于存储就诊的详细信息，如诊断、处方等")
    private String visitDetails;

    /** 父母孩子关系id */
    private Long parentTeenId;

    /** 治疗师id */
    private Long userId;

    /** 扣款账户(父母的id) */
    private String deductParentId;

    /*用户与套餐的关系主键*/
    private Long userPackageId;

    /** 套餐主键 */
    private Long packageId;

    /** 存储治疗前的图片上传的URL */
    @Excel(name = "存储治疗前的图片上传的URL")
    private String beforeTreatmentImageUrl;

    /** 存储治疗后的图片上传的URL */
    @Excel(name = "存储治疗后的图片上传的URL")
    private String afterTreatmentImageUrl;

    /** 状态(0治疗中1完成治疗)完成治疗后插入消费表 */
    @Excel(name = "状态(0治疗中1完成治疗2暂存记录)完成治疗后插入消费表")
    private Long status;

    /** 完成治疗时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成治疗时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date healTime;

    /**
     * 编号
     */
    private String visitNo;

    public String getVisitNo() {
        return visitNo;
    }

    public void setVisitNo(String visitNo) {
        this.visitNo = visitNo;
    }

    public Long getUserPackageId() {
        return userPackageId;
    }

    public void setUserPackageId(Long userPackageId) {
        this.userPackageId = userPackageId;
    }

    /**
     * 部门ID
     */
    private Long deptId;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public void setVisitId(Long visitId)
    {
        this.visitId = visitId;
    }

    public Long getVisitId()
    {
        return visitId;
    }
    public void setVisitDetails(String visitDetails)
    {
        this.visitDetails = visitDetails;
    }

    public String getVisitDetails()
    {
        return visitDetails;
    }
    public void setParentTeenId(Long parentTeenId)
    {
        this.parentTeenId = parentTeenId;
    }

    public Long getParentTeenId()
    {
        return parentTeenId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setDeductParentId(String deductParentId)
    {
        this.deductParentId = deductParentId;
    }

    public String getDeductParentId()
    {
        return deductParentId;
    }
    public void setBeforeTreatmentImageUrl(String beforeTreatmentImageUrl)
    {
        this.beforeTreatmentImageUrl = beforeTreatmentImageUrl;
    }

    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    public String getBeforeTreatmentImageUrl()
    {
        return beforeTreatmentImageUrl;
    }
    public void setAfterTreatmentImageUrl(String afterTreatmentImageUrl)
    {
        this.afterTreatmentImageUrl = afterTreatmentImageUrl;
    }

    public String getAfterTreatmentImageUrl()
    {
        return afterTreatmentImageUrl;
    }
    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }
    public void setHealTime(Date healTime)
    {
        this.healTime = healTime;
    }

    public Date getHealTime()
    {
        return healTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("visitId", getVisitId())
            .append("visitDetails", getVisitDetails())
            .append("parentTeenId", getParentTeenId())
            .append("userId", getUserId())
            .append("deductParentId", getDeductParentId())
            .append("beforeTreatmentImageUrl", getBeforeTreatmentImageUrl())
            .append("afterTreatmentImageUrl", getAfterTreatmentImageUrl())
            .append("status", getStatus())
            .append("healTime", getHealTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
