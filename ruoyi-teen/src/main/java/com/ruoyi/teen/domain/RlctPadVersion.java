package com.ruoyi.teen.domain;

import com.ruoyi.common.core.domain.BaseEntity;

public class RlctPadVersion extends BaseEntity {
    private Long versionId; //  '主键'
    private String versionNumber; //   版本号
    private String description; // 描述
    private String status ; // 发布状态
    private String versionUrl; // 版本地址
    private String delFlag; // 删除标志（0代表存在 2代表删除）

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVersionUrl() {
        return versionUrl;
    }

    public void setVersionUrl(String versionUrl) {
        this.versionUrl = versionUrl;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}
