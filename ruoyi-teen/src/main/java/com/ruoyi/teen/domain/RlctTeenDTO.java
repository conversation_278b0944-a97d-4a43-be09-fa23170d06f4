package com.ruoyi.teen.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

public class RlctTeenDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 孩子ID */
    private Long teenId;

    /** 第一次创建的患者的家长id */
    private Long parentId;

    /** 孩子姓名 */
    private String teenName;

    /** 孩子年龄 */
    private Long teenAge;

    /** 孩子性别 */
    private String teenSex;

    /** 孩子出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date teenBirth;

    /** 初诊身高 */
    private BigDecimal initHeight;

    /** 初诊体重 */
    private BigDecimal initWeight;

    /** 初诊时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初诊时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initTime;

    /** 孩子姓名首字母拼音 */
    @Excel(name = "孩子姓名首字母拼音")
    private String teenNameCh;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 父亲身高 */
    @Excel(name = "父亲身高")
    private BigDecimal fatherHeight;

    /** 母亲身高 */
    @Excel(name = "母亲身高")
    private BigDecimal motherHeight;

    /** 既往病史 */
    @Excel(name = "既往病史")
    private String medicalHistory;

    /** 就诊目标 */
    @Excel(name = "就诊目标")
    private String visitGoal;

    /** 家庭地址 */
    @Excel(name = "家庭地址")
    private String teenAddress;

    /** 遗传身高 */
    @Excel(name = "遗传身高")
    private BigDecimal geneticHeight;

    /** 骨龄 */
    @Excel(name = "骨龄")
    private BigDecimal boneAge;

    /** 过敏史 */
    @Excel(name = "过敏史")
    private String allergyHistory;


    public Long getTeenId() {
        return teenId;
    }

    public void setTeenId(Long teenId) {
        this.teenId = teenId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public Long getTeenAge() {
        return teenAge;
    }

    public void setTeenAge(Long teenAge) {
        this.teenAge = teenAge;
    }

    public String getTeenSex() {
        return teenSex;
    }

    public void setTeenSex(String teenSex) {
        this.teenSex = teenSex;
    }

    public Date getTeenBirth() {
        return teenBirth;
    }

    public void setTeenBirth(Date teenBirth) {
        this.teenBirth = teenBirth;
    }

    public BigDecimal getInitHeight() {
        return initHeight;
    }

    public void setInitHeight(BigDecimal initHeight) {
        this.initHeight = initHeight;
    }

    public BigDecimal getInitWeight() {
        return initWeight;
    }

    public void setInitWeight(BigDecimal initWeight) {
        this.initWeight = initWeight;
    }

    public Date getInitTime() {
        return initTime;
    }

    public void setInitTime(Date initTime) {
        this.initTime = initTime;
    }

    public String getTeenNameCh() {
        return teenNameCh;
    }

    public void setTeenNameCh(String teenNameCh) {
        this.teenNameCh = teenNameCh;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public BigDecimal getFatherHeight() {
        return fatherHeight;
    }

    public void setFatherHeight(BigDecimal fatherHeight) {
        this.fatherHeight = fatherHeight;
    }

    public BigDecimal getMotherHeight() {
        return motherHeight;
    }

    public void setMotherHeight(BigDecimal motherHeight) {
        this.motherHeight = motherHeight;
    }

    public String getMedicalHistory() {
        return medicalHistory;
    }

    public void setMedicalHistory(String medicalHistory) {
        this.medicalHistory = medicalHistory;
    }

    public String getVisitGoal() {
        return visitGoal;
    }

    public void setVisitGoal(String visitGoal) {
        this.visitGoal = visitGoal;
    }

    public String getTeenAddress() {
        return teenAddress;
    }

    public void setTeenAddress(String teenAddress) {
        this.teenAddress = teenAddress;
    }

    public BigDecimal getGeneticHeight() {
        return geneticHeight;
    }

    public void setGeneticHeight(BigDecimal geneticHeight) {
        this.geneticHeight = geneticHeight;
    }

    public BigDecimal getBoneAge() {
        return boneAge;
    }

    public void setBoneAge(BigDecimal boneAge) {
        this.boneAge = boneAge;
    }

    public String getAllergyHistory() {
        return allergyHistory;
    }

    public void setAllergyHistory(String allergyHistory) {
        this.allergyHistory = allergyHistory;
    }
}
