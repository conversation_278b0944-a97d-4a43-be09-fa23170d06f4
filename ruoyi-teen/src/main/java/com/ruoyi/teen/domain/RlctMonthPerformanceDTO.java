package com.ruoyi.teen.domain;

import java.math.BigDecimal;

public class RlctMonthPerformanceDTO {
    private Long treatmentType;       // 治疗类型id
    private String treatmentTypeName;   // 治疗类型名称
    private BigDecimal currentCount;   // 本月次数
    private BigDecimal lastCount;      // 上月次数
    private BigDecimal difference;             // 差值数量
    private BigDecimal growthRate;     // 增长率（%）


    public Long getTreatmentType() {
        return treatmentType;
    }

    public void setTreatmentType(Long treatmentType) {
        this.treatmentType = treatmentType;
    }

    public String getTreatmentTypeName() {
        return treatmentTypeName;
    }

    public void setTreatmentTypeName(String treatmentTypeName) {
        this.treatmentTypeName = treatmentTypeName;
    }

    public BigDecimal getCurrentCount() {
        return currentCount;
    }

    public void setCurrentCount(BigDecimal currentCount) {
        this.currentCount = currentCount;
    }

    public BigDecimal getLastCount() {
        return lastCount;
    }

    public void setLastCount(BigDecimal lastCount) {
        this.lastCount = lastCount;
    }

    public BigDecimal getDifference() {
        return difference;
    }

    public void setDifference(BigDecimal difference) {
        this.difference = difference;
    }

    public BigDecimal getGrowthRate() {
        return growthRate;
    }

    public void setGrowthRate(BigDecimal growthRate) {
        this.growthRate = growthRate;
    }
}
