package com.ruoyi.teen.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

public class RlctDoctorVisic {
    private Long doctorVisicId;               // 治疗工作ID主键
    private Long visitId;          // 就诊记录ID
    private Long doctorId;         // 医师ID
    private String treatmentType;  // 治疗项目类型(0关节松解1指针点穴2鼻炎刮痧3专项训练4艾灸)
    private String reviewStatus;   // 审核状态(0待审核 1审核通过)
    private String delFlag; //删除标记
    private Long reviewId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date treatmentTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelReviewTime;  // 取消审核时间

    private String createBy; // 创建者
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime; // 创建时间

    private Long deleteBy;// 删除者

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deleteTime;// 删除时间


    public Long getDeleteBy() {
        return deleteBy;
    }

    public void setDeleteBy(Long deleteBy) {
        this.deleteBy = deleteBy;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getCancelReviewTime() {
        return cancelReviewTime;
    }

    public void setCancelReviewTime(Date cancelReviewTime) {
        this.cancelReviewTime = cancelReviewTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public Date getTreatmentTime() {
        return treatmentTime;
    }

    public void setTreatmentTime(Date treatmentTime) {
        this.treatmentTime = treatmentTime;
    }

    public Long getDoctorVisicId() {
        return doctorVisicId;
    }

    public void setDoctorVisicId(Long doctorVisicId) {
        this.doctorVisicId = doctorVisicId;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public String getTreatmentType() {
        return treatmentType;
    }

    public void setTreatmentType(String treatmentType) {
        this.treatmentType = treatmentType;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
}
