package com.ruoyi.teen.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

public class RlctBoneageDTO extends BaseEntity {

    private Long boneAgeRecordId; // '骨龄拍摄记录ID'
    private String boneAge; //'骨龄'
    private Long parentTeenId; // ''父母孩子关系ID''
    private String predictedHeight; // '预测身高'

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date captureTime; // '拍摄时间'

    private String boneAgeReport; // '骨龄报告'
    private Long teenAge; // ''拍摄骨龄时候孩子的年龄''

    public Long getBoneAgeRecordId() {
        return boneAgeRecordId;
    }

    public void setBoneAgeRecordId(Long boneAgeRecordId) {
        this.boneAgeRecordId = boneAgeRecordId;
    }

    public String getBoneAge() {
        return boneAge;
    }

    public void setBoneAge(String boneAge) {
        this.boneAge = boneAge;
    }

    public Long getParentTeenId() {
        return parentTeenId;
    }

    public void setParentTeenId(Long parentTeenId) {
        this.parentTeenId = parentTeenId;
    }

    public String getPredictedHeight() {
        return predictedHeight;
    }

    public void setPredictedHeight(String predictedHeight) {
        this.predictedHeight = predictedHeight;
    }

    public Date getCaptureTime() {
        return captureTime;
    }

    public void setCaptureTime(Date captureTime) {
        this.captureTime = captureTime;
    }

    public String getBoneAgeReport() {
        return boneAgeReport;
    }

    public void setBoneAgeReport(String boneAgeReport) {
        this.boneAgeReport = boneAgeReport;
    }

    public Long getTeenAge() {
        return teenAge;
    }

    public void setTeenAge(Long teenAge) {
        this.teenAge = teenAge;
    }
}
