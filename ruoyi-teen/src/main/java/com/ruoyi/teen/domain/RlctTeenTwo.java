package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 孩子对象 rlct_teen_two
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public class RlctTeenTwo extends RlctTeen
{


    /** 初诊身高 */
    private BigDecimal initHeight;

    /** 初诊体重 */
    private BigDecimal initWeight;

    /** 初诊时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初诊时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initTime;

    /** 孩子姓名首字母拼音 */
    @Excel(name = "孩子姓名首字母拼音")
    private String teenNameCh;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 父亲身高 */
    @Excel(name = "父亲身高")
    private BigDecimal fatherHeight;

    /** 母亲身高 */
    @Excel(name = "母亲身高")
    private BigDecimal motherHeight;

    /** 既往病史 */
    @Excel(name = "既往病史")
    private String medicalHistory;

    /** 就诊目标 */
    @Excel(name = "就诊目标")
    private String visitGoal;

    /** 家庭地址 */
    @Excel(name = "家庭地址")
    private String teenAddress;

    /** 遗传身高 */
    @Excel(name = "遗传身高")
    private BigDecimal geneticHeight;

    /** 骨龄 */
    @Excel(name = "骨龄")
    private BigDecimal boneAge;

    /** 过敏史 */
    @Excel(name = "过敏史")
    private String allergyHistory;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phonenumber;

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public void setInitHeight(BigDecimal initHeight)
    {
        this.initHeight = initHeight;
    }

    public BigDecimal getInitHeight()
    {
        return initHeight;
    }
    public void setInitWeight(BigDecimal initWeight)
    {
        this.initWeight = initWeight;
    }

    public BigDecimal getInitWeight()
    {
        return initWeight;
    }
    public void setInitTime(Date initTime)
    {
        this.initTime = initTime;
    }

    public Date getInitTime()
    {
        return initTime;
    }
    public void setTeenNameCh(String teenNameCh)
    {
        this.teenNameCh = teenNameCh;
    }

    public String getTeenNameCh()
    {
        return teenNameCh;
    }
    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }
    public void setFatherHeight(BigDecimal fatherHeight)
    {
        this.fatherHeight = fatherHeight;
    }

    public BigDecimal getFatherHeight()
    {
        return fatherHeight;
    }
    public void setMotherHeight(BigDecimal motherHeight)
    {
        this.motherHeight = motherHeight;
    }

    public BigDecimal getMotherHeight()
    {
        return motherHeight;
    }
    public void setMedicalHistory(String medicalHistory)
    {
        this.medicalHistory = medicalHistory;
    }

    public String getMedicalHistory()
    {
        return medicalHistory;
    }
    public void setVisitGoal(String visitGoal)
    {
        this.visitGoal = visitGoal;
    }

    public String getVisitGoal()
    {
        return visitGoal;
    }

    public String getTeenAddress() {
        return teenAddress;
    }

    public void setTeenAddress(String teenAddress) {
        this.teenAddress = teenAddress;
    }

    public void setGeneticHeight(BigDecimal geneticHeight)
    {
        this.geneticHeight = geneticHeight;
    }

    public BigDecimal getGeneticHeight()
    {
        return geneticHeight;
    }
    public void setBoneAge(BigDecimal boneAge)
    {
        this.boneAge = boneAge;
    }

    public BigDecimal getBoneAge()
    {
        return boneAge;
    }
    public void setAllergyHistory(String allergyHistory)
    {
        this.allergyHistory = allergyHistory;
    }

    public String getAllergyHistory()
    {
        return allergyHistory;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("teenId", getTeenId())
            .append("parentId", getParentId())
            .append("teenName", getTeenName())
            .append("teenAge", getTeenAge())
            .append("teenSex", getTeenSex())
            .append("teenBirth", getTeenBirth())
            .append("initHeight", getInitHeight())
            .append("initWeight", getInitWeight())
            .append("initTime", getInitTime())
            .append("teenNameCh", getTeenNameCh())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("fatherHeight", getFatherHeight())
            .append("motherHeight", getMotherHeight())
            .append("medicalHistory", getMedicalHistory())
            .append("visitGoal", getVisitGoal())
            .append("teenAddress", getTeenAddress())
            .append("geneticHeight", getGeneticHeight())
            .append("boneAge", getBoneAge())
            .append("allergyHistory", getAllergyHistory())
            .toString();
    }
}
