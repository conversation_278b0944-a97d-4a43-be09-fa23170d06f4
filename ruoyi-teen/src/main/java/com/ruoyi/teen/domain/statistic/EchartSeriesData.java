package com.ruoyi.teen.domain.statistic;

import java.util.List;

public class EchartSeriesData {
    private List<String> legend;
    private List<String> xAxisData;

    private List<SeriesData> seriesDataList;

    private List<RadarTitle> indicator;

    public List<RadarTitle> getIndicator() {
        return indicator;
    }

    public void setIndicator(List<RadarTitle> indicator) {
        this.indicator = indicator;
    }

    public List<String> getLegend() {
        return legend;
    }

    public void setLegend(List<String> legend) {
        this.legend = legend;
    }

    public List<String> getxAxisData() {
        return xAxisData;
    }

    public void setxAxisData(List<String> xAxisData) {
        this.xAxisData = xAxisData;
    }

    public List<SeriesData> getSeriesDataList() {
        return seriesDataList;
    }

    public void setSeriesDataList(List<SeriesData> seriesDataList) {
        this.seriesDataList = seriesDataList;
    }
}
