package com.ruoyi.teen.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class RlctBoneAgeRecord {
    private Long boneAgeRecordId;       // 骨龄拍摄记录ID
    private String boneAge;             // 骨龄
    private Long parentTeenId;          // 父母孩子关系ID
    /*孩子年龄*/
    private String teenAge;
    private String predictedHeight;     // 预测身高
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date captureTime;           // 拍摄时间
    private String createBy;            // 创建者
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;            // 创建时间
    private String updateBy;            // 更新者
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;            // 更新时间
    private String boneAgeReport;       // 骨龄报告

    public String getTeenAge() {
        return teenAge;
    }

    public void setTeenAge(String teenAge) {
        this.teenAge = teenAge;
    }

    public Long getBoneAgeRecordId() {
        return boneAgeRecordId;
    }

    public void setBoneAgeRecordId(Long boneAgeRecordId) {
        this.boneAgeRecordId = boneAgeRecordId;
    }

    public String getBoneAge() {
        return boneAge;
    }

    public void setBoneAge(String boneAge) {
        this.boneAge = boneAge;
    }

    public Long getParentTeenId() {
        return parentTeenId;
    }

    public void setParentTeenId(Long parentTeenId) {
        this.parentTeenId = parentTeenId;
    }

    public String getPredictedHeight() {
        return predictedHeight;
    }

    public void setPredictedHeight(String predictedHeight) {
        this.predictedHeight = predictedHeight;
    }

    public Date getCaptureTime() {
        return captureTime;
    }

    public void setCaptureTime(Date captureTime) {
        this.captureTime = captureTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBoneAgeReport() {
        return boneAgeReport;
    }

    public void setBoneAgeReport(String boneAgeReport) {
        this.boneAgeReport = boneAgeReport;
    }
}
