package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 套餐对象 rlct_packages
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctPackages extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 套餐的名称 */
    @Excel(name = "套餐的名称")
    @NotNull(message = "套餐名称不能为空")
    @Size(min = 0,max = 255,message = "套餐名称长度不能超过255个字符串")
    private String packageName;

    /** 主键，唯一标识每个套餐的ID */
    private Long packageId;

    /** 套餐的原价 */
    @Excel(name = "套餐的原价")
    private BigDecimal packagePrice;

    /** 套餐的会员价 */
    @Excel(name = "套餐的会员价")
    private BigDecimal packageMemberPrice;

    /** 套餐的描述，包括包含的理疗项目等信息 */
    @Excel(name = "套餐的描述，包括包含的理疗项目等信息")
    private String packageDescription;

    /** 套餐图片 */
    @Excel(name = "套餐图片")
    private String packageImageUrl;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
    /*套餐次数*/
    private Long packageNumber;

    public Long getPackageNumber() {
        return packageNumber;
    }

    public void setPackageNumber(Long packageNumber) {
        this.packageNumber = packageNumber;
    }

    public void setPackageName(String packageName)
    {
        this.packageName = packageName;
    }

    public String getPackageName()
    {
        return packageName;
    }
    public void setPackageId(Long packageId)
    {
        this.packageId = packageId;
    }

    public Long getPackageId()
    {
        return packageId;
    }
    public void setPackagePrice(BigDecimal packagePrice)
    {
        this.packagePrice = packagePrice;
    }

    public BigDecimal getPackagePrice()
    {
        return packagePrice;
    }
    public void setPackageMemberPrice(BigDecimal packageMemberPrice)
    {
        this.packageMemberPrice = packageMemberPrice;
    }

    public BigDecimal getPackageMemberPrice()
    {
        return packageMemberPrice;
    }
    public void setPackageDescription(String packageDescription)
    {
        this.packageDescription = packageDescription;
    }

    public String getPackageDescription()
    {
        return packageDescription;
    }
    public void setPackageImageUrl(String packageImageUrl)
    {
        this.packageImageUrl = packageImageUrl;
    }

    public String getPackageImageUrl()
    {
        return packageImageUrl;
    }
    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("packageName", getPackageName())
            .append("packageId", getPackageId())
            .append("packagePrice", getPackagePrice())
            .append("packageMemberPrice", getPackageMemberPrice())
            .append("packageDescription", getPackageDescription())
            .append("packageImageUrl", getPackageImageUrl())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
