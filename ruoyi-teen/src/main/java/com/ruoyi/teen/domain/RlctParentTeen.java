package com.ruoyi.teen.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 家长和孩子的关系对象 rlct_parent_teen
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctParentTeen extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    private Long id;

    /** 父母id */
    private Long parentId;

    /** 孩子id */
    private Long teenId;

    // 多条/单条记录获取类型
    private char type;

    public char getType() {
        return type;
    }

    public void setType(char type) {
        this.type = type;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }
    public void setTeenId(Long teenId)
    {
        this.teenId = teenId;
    }

    public Long getTeenId()
    {
        return teenId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("parentId", getParentId())
            .append("teenId", getTeenId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
