package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户和套餐关系对象 rlct_user_package
 *
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctUserPackage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 家长主键 */
    private Long userId;

    /** 套餐主键 */
    private Long packageId;

    /** 套餐的总次数 */
    @Excel(name = "套餐的总次数")
    private Long packageTotalTimes;
    /** 套餐消费中的次数*/
    private Long packageDuringConsumption;
    /** 套餐的购买次数 */
    @Excel(name = "套餐的购买次数")
    private Long packageBuyTimes;

    /** 套餐的赠送次数 */
    @Excel(name = "套餐的赠送次数")
    private Long packageGiftTimes;

    /** 套餐的消费次数 */
    @Excel(name = "套餐的消费次数")
    private Long packageNumberTimes;

    /** 家长购买套餐时的原价 */
    @Excel(name = "家长购买套餐时的原价")
    private BigDecimal packageThenPrice;

    /** 家长购买套餐时候的价格 */
    @Excel(name = "家长购买套餐时候的价格")
    private BigDecimal packagePrice;

    /** 退的多少钱 */
    @Excel(name = "退的多少钱")
    private BigDecimal packageReturnPrice;

    /** 退费时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退费时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date returnTime;

    /** 套餐退费的状态(0正常1退费完成) */
    @Excel(name = "套餐退费的状态(字典:rlct_user_package_status0正常1退费完成)")
    private Long packageReturnStatus;

    private Long packageAuthorizationer;
    private Long packageReturnUser;//退费者

    public Long getPackageReturnUser() {
        return packageReturnUser;
    }

    public void setPackageReturnUser(Long packageReturnUser) {
        this.packageReturnUser = packageReturnUser;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setPackageId(Long packageId)
    {
        this.packageId = packageId;
    }

    public Long getPackageId()
    {
        return packageId;
    }
    public void setPackageTotalTimes(Long packageTotalTimes)
    {
        this.packageTotalTimes = packageTotalTimes;
    }

    public Long getPackageTotalTimes()
    {
        return packageTotalTimes;
    }
    public void setPackageBuyTimes(Long packageBuyTimes)
    {
        this.packageBuyTimes = packageBuyTimes;
    }

    public Long getPackageDuringConsumption() {
        return packageDuringConsumption;
    }

    public void setPackageDuringConsumption(Long packageDuringConsumption) {
        this.packageDuringConsumption = packageDuringConsumption;
    }

    public Long getPackageBuyTimes()
    {
        return packageBuyTimes;
    }
    public void setPackageGiftTimes(Long packageGiftTimes)
    {
        this.packageGiftTimes = packageGiftTimes;
    }

    public Long getPackageGiftTimes()
    {
        return packageGiftTimes;
    }
    public void setPackageNumberTimes(Long packageNumberTimes)
    {
        this.packageNumberTimes = packageNumberTimes;
    }

    public Long getPackageNumberTimes()
    {
        return packageNumberTimes;
    }
    public void setPackageThenPrice(BigDecimal packageThenPrice)
    {
        this.packageThenPrice = packageThenPrice;
    }

    public BigDecimal getPackageThenPrice()
    {
        return packageThenPrice;
    }
    public void setPackagePrice(BigDecimal packagePrice)
    {
        this.packagePrice = packagePrice;
    }

    public BigDecimal getPackagePrice()
    {
        return packagePrice;
    }
    public void setPackageReturnPrice(BigDecimal packageReturnPrice)
    {
        this.packageReturnPrice = packageReturnPrice;
    }

    public BigDecimal getPackageReturnPrice()
    {
        return packageReturnPrice;
    }
    public void setReturnTime(Date returnTime)
    {
        this.returnTime = returnTime;
    }

    public Date getReturnTime()
    {
        return returnTime;
    }
    public void setPackageReturnStatus(Long packageReturnStatus)
    {
        this.packageReturnStatus = packageReturnStatus;
    }

    public Long getPackageReturnStatus()
    {
        return packageReturnStatus;
    }
    public void setPackageAuthorizationer(Long packageAuthorizationer)
    {
        this.packageAuthorizationer = packageAuthorizationer;
    }

    public Long getPackageAuthorizationer()
    {
        return packageAuthorizationer;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("packageId", getPackageId())
            .append("packageTotalTimes", getPackageTotalTimes())
            .append("packageDuringConsumption",getPackageDuringConsumption())
            .append("packageBuyTimes", getPackageBuyTimes())
            .append("packageGiftTimes", getPackageGiftTimes())
            .append("packageNumberTimes", getPackageNumberTimes())
            .append("packageThenPrice", getPackageThenPrice())
            .append("packagePrice", getPackagePrice())
            .append("packageReturnPrice", getPackageReturnPrice())
            .append("returnTime", getReturnTime())
            .append("packageReturnStatus", getPackageReturnStatus())
            .append("packageAuthorizationer", getPackageAuthorizationer())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
