package com.ruoyi.teen.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 医馆信息对象 rlct_clinics
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctClinics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，唯一标识每家医馆的ID */
    private Long clinicId;

    /** 医馆的名称 */

    @NotNull(message = "医馆名称不能为空")
    @Size(min = 0,max = 100,message = "医馆名称长度不能超过100个字符串")
    @Excel(name = "医馆的名称")
    private String clinicName;

    /** 医馆的地址 */
    @Excel(name = "医馆的地址")
    @NotNull(message = "医馆地址不能为空")
    @Size(min = 0,max = 200,message = "医馆名称长度不能超过200个字符串")
    private String clinicAddress;

    /** 医馆的联系电话号码 */
    @Excel(name = "医馆的联系电话号码")
    @Size(min = 0,max = 50,message = "医馆名称长度不能超过50个字符串")
    private String clinicPhoneNumber;

    /** 医馆图片 */
    @Excel(name = "医馆图片")
    private String clinicImageUrl;

    /** 医馆的描述，包括服务、设施等信息 */
    @Excel(name = "医馆的描述，包括服务、设施等信息")
    @Size(min = 0,max = 500,message = "医馆名称长度不能超过500个字符串")
    private String clinicDescription;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setClinicId(Long clinicId) 
    {
        this.clinicId = clinicId;
    }

    public Long getClinicId() 
    {
        return clinicId;
    }
    public void setClinicName(String clinicName) 
    {
        this.clinicName = clinicName;
    }

    public String getClinicName() 
    {
        return clinicName;
    }
    public void setClinicAddress(String clinicAddress) 
    {
        this.clinicAddress = clinicAddress;
    }

    public String getClinicAddress() 
    {
        return clinicAddress;
    }
    public void setClinicPhoneNumber(String clinicPhoneNumber) 
    {
        this.clinicPhoneNumber = clinicPhoneNumber;
    }

    public String getClinicPhoneNumber() 
    {
        return clinicPhoneNumber;
    }
    public void setClinicImageUrl(String clinicImageUrl) 
    {
        this.clinicImageUrl = clinicImageUrl;
    }

    public String getClinicImageUrl() 
    {
        return clinicImageUrl;
    }
    public void setClinicDescription(String clinicDescription) 
    {
        this.clinicDescription = clinicDescription;
    }

    public String getClinicDescription() 
    {
        return clinicDescription;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("clinicId", getClinicId())
            .append("clinicName", getClinicName())
            .append("clinicAddress", getClinicAddress())
            .append("clinicPhoneNumber", getClinicPhoneNumber())
            .append("clinicImageUrl", getClinicImageUrl())
            .append("clinicDescription", getClinicDescription())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
