package com.ruoyi.teen.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.teen.vo.ipad.PadtreatmentTypeListVo;

import java.util.Date;
import java.util.List;

public class RlctDoctorVisicDTO {

    private String nickName; // 医师的昵称
    private String teenName;  // 患者名字
    private List<PadtreatmentTypeListVo> treatmentTypeList;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date treatmentTime; // 治疗时间
    private Date reviewTime;  //审核时间
    private Long userPackageId; // 用户与套餐关系id
    private Long visitId;  // 就诊id
    private Long doctorId; // 医生id


    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public List<PadtreatmentTypeListVo> getTreatmentTypeList() {
        return treatmentTypeList;
    }

    public void setTreatmentTypeList(List<PadtreatmentTypeListVo> treatmentTypeList) {
        this.treatmentTypeList = treatmentTypeList;
    }


    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getTeenName() {
        return teenName;
    }

    public void setTeenName(String teenName) {
        this.teenName = teenName;
    }

    public Date getTreatmentTime() {
        return treatmentTime;
    }

    public void setTreatmentTime(Date treatmentTime) {
        this.treatmentTime = treatmentTime;
    }



    public Long getUserPackageId() {
        return userPackageId;
    }

    public void setUserPackageId(Long userPackageId) {
        this.userPackageId = userPackageId;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

}
