package com.ruoyi.teen.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/*产品使用方案*/
public class ProductUse  {
    private String productUseName;   // 产品使用方案 (varbinary)
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public String getProductUseName() {
        return productUseName;
    }

    public void setProductUseName(String productUseName) {
        this.productUseName = productUseName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
