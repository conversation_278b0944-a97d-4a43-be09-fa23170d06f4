package com.ruoyi.teen.domain.config;

import java.util.List;

public class AttributeListBean {
    private List<AttributeDetails> sleepList;//睡眠习惯
    private List<AttributeDetails> durationList;//睡眠时长
    private List<AttributeDetails> jumpList;//跳绳
    private List<AttributeDetails> heightJumpList;//摸高跳
    private List<AttributeDetails> barList;//吊单杠
    private List<AttributeDetails> milkList;//奶类
    private List<AttributeDetails> eggList;//蛋类
    private List<AttributeDetails> vegetableList;//蔬菜
    private List<AttributeDetails> rawList;//蔬菜
    private List<AttributeDetails> grainList;//五谷杂粮
    private List<AttributeDetails> snacksList;//饮料零食
    private List<AttributeDetails> eatSnackList;//吃宵夜
    private List<AttributeDetails> vAtList;//维生素A
    private List<AttributeDetails> vDtList;//维生素D
    private List<AttributeDetails> caList;//钙
    private List<AttributeDetails> synthesisList;//综合维生素
    private List<AttributeDetails> acidList;//氨基酸

    public List<AttributeDetails> getRawList() {
        return rawList;
    }

    public void setRawList(List<AttributeDetails> rawList) {
        this.rawList = rawList;
    }

    public List<AttributeDetails> getSleepList() {
        return sleepList;
    }

    public void setSleepList(List<AttributeDetails> sleepList) {
        this.sleepList = sleepList;
    }

    public List<AttributeDetails> getDurationList() {
        return durationList;
    }

    public void setDurationList(List<AttributeDetails> durationList) {
        this.durationList = durationList;
    }

    public List<AttributeDetails> getJumpList() {
        return jumpList;
    }

    public void setJumpList(List<AttributeDetails> jumpList) {
        this.jumpList = jumpList;
    }

    public List<AttributeDetails> getHeightJumpList() {
        return heightJumpList;
    }

    public void setHeightJumpList(List<AttributeDetails> heightJumpList) {
        this.heightJumpList = heightJumpList;
    }

    public List<AttributeDetails> getBarList() {
        return barList;
    }

    public void setBarList(List<AttributeDetails> barList) {
        this.barList = barList;
    }

    public List<AttributeDetails> getMilkList() {
        return milkList;
    }

    public void setMilkList(List<AttributeDetails> milkList) {
        this.milkList = milkList;
    }

    public List<AttributeDetails> getEggList() {
        return eggList;
    }

    public void setEggList(List<AttributeDetails> eggList) {
        this.eggList = eggList;
    }

    public List<AttributeDetails> getVegetableList() {
        return vegetableList;
    }

    public void setVegetableList(List<AttributeDetails> vegetableList) {
        this.vegetableList = vegetableList;
    }

    public List<AttributeDetails> getGrainList() {
        return grainList;
    }

    public void setGrainList(List<AttributeDetails> grainList) {
        this.grainList = grainList;
    }

    public List<AttributeDetails> getSnacksList() {
        return snacksList;
    }

    public void setSnacksList(List<AttributeDetails> snacksList) {
        this.snacksList = snacksList;
    }

    public List<AttributeDetails> getEatSnackList() {
        return eatSnackList;
    }

    public void setEatSnackList(List<AttributeDetails> eatSnackList) {
        this.eatSnackList = eatSnackList;
    }

    public List<AttributeDetails> getvAtList() {
        return vAtList;
    }

    public void setvAtList(List<AttributeDetails> vAtList) {
        this.vAtList = vAtList;
    }

    public List<AttributeDetails> getvDtList() {
        return vDtList;
    }

    public void setvDtList(List<AttributeDetails> vDtList) {
        this.vDtList = vDtList;
    }

    public List<AttributeDetails> getCaList() {
        return caList;
    }

    public void setCaList(List<AttributeDetails> caList) {
        this.caList = caList;
    }

    public List<AttributeDetails> getSynthesisList() {
        return synthesisList;
    }

    public void setSynthesisList(List<AttributeDetails> synthesisList) {
        this.synthesisList = synthesisList;
    }

    public List<AttributeDetails> getAcidList() {
        return acidList;
    }

    public void setAcidList(List<AttributeDetails> acidList) {
        this.acidList = acidList;
    }
}
