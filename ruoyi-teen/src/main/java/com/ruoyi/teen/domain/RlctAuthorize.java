package com.ruoyi.teen.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 授权对象 rlct_authorize
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public class RlctAuthorize extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /** Id */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 授权码 */
    @Excel(name = "授权码")
    private String authorizeCode;

    /** 创建者 */
    @Excel(name = "创建者")
    private String creater;

    /** 更新者 */
    @Excel(name = "更新者")
    private String updater;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setAuthorizeCode(String authorizeCode)
    {
        this.authorizeCode = authorizeCode;
    }

    public String getAuthorizeCode()
    {
        return authorizeCode;
    }
    public void setCreater(String creater)
    {
        this.creater = creater;
    }

    public String getCreater()
    {
        return creater;
    }
    public void setUpdater(String updater)
    {
        this.updater = updater;
    }

    public String getUpdater()
    {
        return updater;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("authorizeCode", getAuthorizeCode())
            .append("createTime", getCreateTime())
            .append("creater", getCreater())
            .append("updateTime", getUpdateTime())
            .append("updater", getUpdater())
            .toString();
    }
}
