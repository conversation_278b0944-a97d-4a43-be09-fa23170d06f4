package com.ruoyi.teen.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

public class RlctVisitVTwoDTO extends RlctVisit{

    /*接诊时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date admissionTime;

    /*存储治疗前身高*/
    private BigDecimal heightBeforeTreatment;
    /*存储治疗后身高*/
    private BigDecimal heightAfterTreatment;
    private BigDecimal weightBeforeTreatment;
    private BigDecimal weightAfterTreatment;

    /*导购人员id*/
    private Long salesId;

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public Date getAdmissionTime() {
        return admissionTime;
    }

    public void setAdmissionTime(Date admissionTime) {
        this.admissionTime = admissionTime;
    }

    public BigDecimal getHeightBeforeTreatment() {
        return heightBeforeTreatment;
    }

    public void setHeightBeforeTreatment(BigDecimal heightBeforeTreatment) {
        this.heightBeforeTreatment = heightBeforeTreatment;
    }

    public BigDecimal getHeightAfterTreatment() {
        return heightAfterTreatment;
    }

    public void setHeightAfterTreatment(BigDecimal heightAfterTreatment) {
        this.heightAfterTreatment = heightAfterTreatment;
    }

    public BigDecimal getWeightBeforeTreatment() {
        return weightBeforeTreatment;
    }

    public void setWeightBeforeTreatment(BigDecimal weightBeforeTreatment) {
        this.weightBeforeTreatment = weightBeforeTreatment;
    }

    public BigDecimal getWeightAfterTreatment() {
        return weightAfterTreatment;
    }

    public void setWeightAfterTreatment(BigDecimal weightAfterTreatment) {
        this.weightAfterTreatment = weightAfterTreatment;
    }

}
