package com.ruoyi.teen.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class RlcttreatmentTypeListDTO {
    private Long doctorId;
    private Long visitId;
    private Long doctorVisicId;
    private Long treatmentType;

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public Long getVisitId() {
        return visitId;
    }

    public void setVisitId(Long visitId) {
        this.visitId = visitId;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date  reviewTime;


    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public Long getDoctorVisicId() {
        return doctorVisicId;
    }

    public void setDoctorVisicId(Long doctorVisicId) {
        this.doctorVisicId = doctorVisicId;
    }

    public Long getTreatmentType() {
        return treatmentType;
    }

    public void setTreatmentType(Long treatmentType) {
        this.treatmentType = treatmentType;
    }
}
