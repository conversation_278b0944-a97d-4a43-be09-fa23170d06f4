package com.ruoyi.teen.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 配置对象 rlct_config
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置表主键id */
    private Long id;

    /** 登记属性 */
    @Excel(name = "登记属性")
    private String attribute;

    /** 登记参数 */
    @Excel(name = "登记参数")
    private String param;

    /** 分数 */
    @Excel(name = "分数")
    private BigDecimal score;

    /** 排序值 */
    @Excel(name = "排序值")
    private Long sort;

    /** 配置类型，字典：rlct_config_type */
    @Excel(name = "配置类型，字典：rlct_config_type")
    private String configType;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAttribute(String attribute) 
    {
        this.attribute = attribute;
    }

    public String getAttribute() 
    {
        return attribute;
    }
    public void setParam(String param) 
    {
        this.param = param;
    }

    public String getParam() 
    {
        return param;
    }
    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }
    public void setSort(Long sort) 
    {
        this.sort = sort;
    }

    public Long getSort() 
    {
        return sort;
    }
    public void setConfigType(String configType) 
    {
        this.configType = configType;
    }

    public String getConfigType() 
    {
        return configType;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("attribute", getAttribute())
            .append("param", getParam())
            .append("score", getScore())
            .append("sort", getSort())
            .append("configType", getConfigType())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
