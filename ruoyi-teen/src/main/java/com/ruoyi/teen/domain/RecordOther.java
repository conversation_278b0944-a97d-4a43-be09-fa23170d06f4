package com.ruoyi.teen.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 档案其它对象 record_other
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public class RecordOther extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 档案其它ID */
    private Long recordOtherId;

    /** 就诊渠道(字典 rlct_visit_scource)00网络宣传   11门诊宣传   22朋友推荐) */
    @Excel(name = "就诊渠道(字典 rlct_visit_scource)00网络宣传   11门诊宣传   22朋友推荐)")
    private String visitSource;

    /** 推荐人 */
    @Excel(name = "推荐人")
    private String referee;

    /** 信息推送(字典sys_yes_no Y是 N否0是愿意接收群发信息 1不愿意接收群发信息) */
    @Excel(name = "信息推送(字典sys_yes_no Y是 N否0是愿意接收群发信息 1不愿意接收群发信息)")
    private String messagePush;

    /** 父母孩子关系ID */
    @Excel(name = "父母孩子关系ID")
    private Long parentTeenId;

    public void setRecordOtherId(Long recordOtherId)
    {
        this.recordOtherId = recordOtherId;
    }

    public Long getRecordOtherId()
    {
        return recordOtherId;
    }
    public void setVisitSource(String visitSource)
    {
        this.visitSource = visitSource;
    }

    public String getVisitSource()
    {
        return visitSource;
    }
    public void setReferee(String referee)
    {
        this.referee = referee;
    }

    public String getReferee()
    {
        return referee;
    }
    public void setMessagePush(String messagePush)
    {
        this.messagePush = messagePush;
    }

    public String getMessagePush()
    {
        return messagePush;
    }
    public void setParentTeenId(Long parentTeenId)
    {
        this.parentTeenId = parentTeenId;
    }

    public Long getParentTeenId()
    {
        return parentTeenId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordOtherId", getRecordOtherId())
            .append("visitSource", getVisitSource())
            .append("referee", getReferee())
            .append("messagePush", getMessagePush())
            .append("parentTeenId", getParentTeenId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
