package com.ruoyi.teen.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 就诊历史记录对象 rlct_visit_history
 * 
 * <AUTHOR>
 * @date 2023-12-04
 */
public class RlctVisitHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 就诊历史记录主键id */
    private Long id;

    /** 就诊记录id */
    private Long visitId;

    /** 治疗前改前图片 */
    @Excel(name = "治疗前改前图片")
    private String beforeTreatmentImageUrl;

    /** 治疗前改后图片 */
    @Excel(name = "治疗前改后图片")
    private String visitBeforeUpdateImageUrl;

    /** 治疗后改前图片 */
    @Excel(name = "治疗后改前图片")
    private String afterTreatmentImageUrl;

    /** 治疗后改后图片 */
    @Excel(name = "治疗后改后图片")
    private String visitAfterUpdateImageUrl;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setVisitId(Long visitId) 
    {
        this.visitId = visitId;
    }

    public Long getVisitId() 
    {
        return visitId;
    }
    public void setBeforeTreatmentImageUrl(String beforeTreatmentImageUrl) 
    {
        this.beforeTreatmentImageUrl = beforeTreatmentImageUrl;
    }

    public String getBeforeTreatmentImageUrl() 
    {
        return beforeTreatmentImageUrl;
    }
    public void setVisitBeforeUpdateImageUrl(String visitBeforeUpdateImageUrl) 
    {
        this.visitBeforeUpdateImageUrl = visitBeforeUpdateImageUrl;
    }

    public String getVisitBeforeUpdateImageUrl() 
    {
        return visitBeforeUpdateImageUrl;
    }
    public void setAfterTreatmentImageUrl(String afterTreatmentImageUrl) 
    {
        this.afterTreatmentImageUrl = afterTreatmentImageUrl;
    }

    public String getAfterTreatmentImageUrl() 
    {
        return afterTreatmentImageUrl;
    }
    public void setVisitAfterUpdateImageUrl(String visitAfterUpdateImageUrl) 
    {
        this.visitAfterUpdateImageUrl = visitAfterUpdateImageUrl;
    }

    public String getVisitAfterUpdateImageUrl() 
    {
        return visitAfterUpdateImageUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("visitId", getVisitId())
            .append("beforeTreatmentImageUrl", getBeforeTreatmentImageUrl())
            .append("visitBeforeUpdateImageUrl", getVisitBeforeUpdateImageUrl())
            .append("afterTreatmentImageUrl", getAfterTreatmentImageUrl())
            .append("visitAfterUpdateImageUrl", getVisitAfterUpdateImageUrl())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
