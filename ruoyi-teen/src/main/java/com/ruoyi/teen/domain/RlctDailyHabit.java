package com.ruoyi.teen.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 患者记录日常习惯对象 rlct_daily_habit
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public class RlctDailyHabit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 患者和家长关系的id */
    private Long parentTeenId;

    /** 配置表id */
    private Long configId;

    public RlctDailyHabit(){}
    public RlctDailyHabit(Long parentTeenId, Date createTime, Long configId){
        this.parentTeenId = parentTeenId;
        this.configId = configId;
        this.setCreateTime(createTime);
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setParentTeenId(Long parentTeenId)
    {
        this.parentTeenId = parentTeenId;
    }

    public Long getParentTeenId()
    {
        return parentTeenId;
    }
    public void setConfigId(Long configId)
    {
        this.configId = configId;
    }

    public Long getConfigId()
    {
        return configId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("parentTeenId", getParentTeenId())
            .append("configId", getConfigId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
