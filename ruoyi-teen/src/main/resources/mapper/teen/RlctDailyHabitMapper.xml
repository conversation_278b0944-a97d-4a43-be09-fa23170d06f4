<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctDailyHabitMapper">

    <resultMap type="RlctDailyHabit" id="RlctDailyHabitResult">
        <result property="id"    column="id"    />
        <result property="parentTeenId"    column="parent_teen_id"    />
        <result property="configId"    column="config_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRlctDailyHabitVo">
        select id, parent_teen_id, config_id, create_time from rlct_daily_habit
    </sql>

    <select id="selectRlctDailyHabitList" parameterType="RlctDailyHabit" resultMap="RlctDailyHabitResult">
        <include refid="selectRlctDailyHabitVo"/>

    </select>

    <select id="selectRlctDailyHabitById" parameterType="Long" resultMap="RlctDailyHabitResult">
        <include refid="selectRlctDailyHabitVo"/>
        where id = #{id}
    </select>

    <insert id="insertRlctDailyHabit" parameterType="RlctDailyHabit">
        insert into rlct_daily_habit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="configId != null">config_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="configId != null">#{configId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRlctDailyHabit" parameterType="RlctDailyHabit">
        update rlct_daily_habit
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentTeenId != null">parent_teen_id = #{parentTeenId},</if>
            <if test="configId != null">config_id = #{configId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlctDailyHabitById" parameterType="Long">
        delete from rlct_daily_habit where id = #{id}
    </delete>

    <delete id="deleteRlctDailyHabitByIds" parameterType="String">
        delete from rlct_daily_habit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectLastDailyHabit" parameterType="java.lang.Long" resultMap="RlctDailyHabitResult">
        select t.id, t.parent_teen_id, t.config_id, t.create_time from rlct_daily_habit t
        where  t.parent_teen_id = #{parentTeenId}
          and  t.create_time = (SELECT MAX(b.create_time) from rlct_daily_habit b where b.parent_teen_id = #{parentTeenId})
    </select>
</mapper>
