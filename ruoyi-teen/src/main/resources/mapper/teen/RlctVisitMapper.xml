<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctVisitMapper">

    <resultMap type="com.ruoyi.teen.domain.RlctVisit" id="RlctVisitResult">
        <result property="visitId"    column="visit_id"    />
        <result property="visitDetails"    column="visit_details"    />
        <result property="parentTeenId"    column="parent_teen_id"    />
        <result property="userId"    column="user_id"    />
        <result property="deductParentId"    column="deduct_parent_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="beforeTreatmentImageUrl"    column="before_treatment_image_url"    />
        <result property="afterTreatmentImageUrl"    column="after_treatment_image_url"    />
        <result property="status"    column="status"    />
        <result property="healTime"    column="heal_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="visitNo"    column="visit_no"    />
        <result property="userPackageId"    column="user_package_id"    />
    </resultMap>
    <resultMap type="com.ruoyi.teen.vo.RlctVisitVo" id="RlctVisitVoResult">
        <result property="visitId"    column="visit_id"    />
        <result property="visitDetails"    column="visit_details"    />
        <result property="parentTeenId"    column="parent_teen_id"    />
        <result property="userId"    column="user_id"    />
        <result property="deductParentId"    column="deduct_parent_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="beforeTreatmentImageUrl"    column="before_treatment_image_url"    />
        <result property="afterTreatmentImageUrl"    column="after_treatment_image_url"    />
        <result property="status"    column="status"    />
        <result property="healTime"    column="heal_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="packageName"    column="package_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="phonenumber"    column="phonenumber"    />
    </resultMap>

    <sql id="selectRlctVisitVo" >
        select visit_id, visit_details, parent_teen_id, user_id, deduct_parent_id,package_id,
               before_treatment_image_url, after_treatment_image_url, status, heal_time,
               create_by, create_time, update_by, update_time, remark,visit_no,user_package_id
        from rlct_visit
    </sql>
    <sql id="selectRlctVisitBean" >
        u.visit_id, u.visit_details, u.parent_teen_id, u.user_id, u.deduct_parent_id,u.package_id,
               u.before_treatment_image_url, u.after_treatment_image_url, u.status, u.heal_time,
               u.create_by, u.create_time, u.update_by, u.update_time, u.remark

    </sql>

    <select id="selectRlctVisitList" parameterType="com.ruoyi.teen.vo.RlctVisitVo" resultType="com.ruoyi.teen.vo.RlctVisitVo">
        select
        <include refid="selectRlctVisitBean"/>,
        ps.phonenumber,ps.nick_name,rt.teen_name,rp.package_name
        from rlct_visit u
        left join rlct_parent_teen rpt on rpt.id = u.parent_teen_id
        left join sys_user ps on ps.user_id = rpt.parent_id
        left join rlct_teen rt on rt.teen_id = rpt.teen_id
        left join rlct_packages rp on rp.package_id = u.package_id
        <where>
            <if test="visitDetails != null  and visitDetails != ''"> and visit_details like concat('%', #{visitDetails}, '%')</if>
            <if test="status != null "> and u.status = #{status}</if>
            <if test="healTime != null "> and heal_time = #{healTime}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="phonenumber != null  and phonenumber != ''"> and ps.phonenumber like concat('%', #{phonenumber}, '%')</if>
            <if test="teenName != null  and teenName != ''"> and rt.teen_name like concat('%', #{teenName}, '%')</if>
        </where>
        order by u.visit_id desc
    </select>

    <select id="selectRlctVisitByVisitId" parameterType="Long" resultMap="RlctVisitResult">
        <include refid="selectRlctVisitVo"/>
        where visit_id = #{visitId}
    </select>

    <insert id="insertRlctVisit" parameterType="RlctVisit" useGeneratedKeys="true" keyProperty="visitId">
        insert into rlct_visit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="visitDetails != null">visit_details,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="deductParentId != null">deduct_parent_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="beforeTreatmentImageUrl != null">before_treatment_image_url,</if>
            <if test="afterTreatmentImageUrl != null">after_treatment_image_url,</if>
            <if test="status != null">status,</if>
            <if test="healTime != null">heal_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="visitDetails != null">#{visitDetails},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deductParentId != null">#{deductParentId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="beforeTreatmentImageUrl != null">#{beforeTreatmentImageUrl},</if>
            <if test="afterTreatmentImageUrl != null">#{afterTreatmentImageUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="healTime != null">#{healTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    <insert id="insertRecordVisit">
        insert into rlct_visit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageId != null">package_id,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="deductParentId != null">deduct_parent_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageId != null">#{packageId},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="deductParentId != null">#{deductParentId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <insert id="insertRlctVisitVTwo" parameterType="RlctVisitVTwoDTO" useGeneratedKeys="true" keyProperty="visitId" keyColumn="visit_id">
        insert into rlct_visit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="visitDetails != null">visit_details,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="deductParentId != null">deduct_parent_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="beforeTreatmentImageUrl != null">before_treatment_image_url,</if>
            <if test="afterTreatmentImageUrl != null">after_treatment_image_url,</if>
            <if test="status != null">status,</if>
            <if test="healTime != null">heal_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="admissionTime !=null">admission_time,</if>
            <if test="heightBeforeTreatment !=null">height_before_treatment,</if>
            <if test="heightAfterTreatment !=null">height_after_treatment,</if>
            <if test="weightBeforeTreatment !=null">weight_before_treatment,</if>
            <if test="weightAfterTreatment !=null">weight_after_treatment,</if>
            <if test="userPackageId != null">user_package_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="salesId != null">sales_id</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="visitDetails != null">#{visitDetails},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deductParentId != null">#{deductParentId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="beforeTreatmentImageUrl != null">#{beforeTreatmentImageUrl},</if>
            <if test="afterTreatmentImageUrl != null">#{afterTreatmentImageUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="healTime != null">#{healTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="admissionTime !=null">#{admissionTime},</if>
            <if test="heightBeforeTreatment !=null">#{heightBeforeTreatment},</if>
            <if test="heightAfterTreatment !=null">#{heightAfterTreatment},</if>
            <if test="weightBeforeTreatment !=null">#{weightBeforeTreatment},</if>
            <if test="weightAfterTreatment !=null">#{weightAfterTreatment},</if>
            <if test="userPackageId != null">#{userPackageId,jdbcType=BIGINT},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="salesId != null">#{salesId}</if>
        </trim>
    </insert>

    <insert id="insertRLctProductUse" parameterType="com.ruoyi.teen.vo.ipad.ProductUseVo" useGeneratedKeys="true" keyProperty="userProductId">
        insert into rlct_product_use
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productUseName != null">product_use_name,</if>
            <if test="doctorId != null">doctor_id,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="visitId != null">visit_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productUseName != null">#{productUseName},</if>
            <if test="doctorId != null">#{doctorId},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="visitId != null">#{visitId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <insert id="insertRlctDoctorVisic" useGeneratedKeys="true" keyProperty="doctorVisicId">
        INSERT INTO rlct_doctor_visic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list != null and !list.isEmpty()">
                <!-- 统一使用item引用 -->
                <if test="list[0].doctorId != null">doctor_id,</if>
                <if test="list[0].treatmentType != null">treatment_type,</if>
                <if test="list[0].treatmentTime != null">treatment_time,</if>
                <if test="list[0].visitId != null">visit_id,</if>
                <if test="list[0].reviewStatus != null">review_status,</if>
                <if test="list[0].delFlag != null">del_flag,</if>
                <if test="list[0].createBy != null">create_by,</if>
                <if test="list[0].createTime != null">create_time,</if>
            </if>
        </trim>
        VALUES
        <foreach item="item" collection="list" separator=",">
            (
            <if test="item.doctorId != null">#{item.doctorId},</if>
            <if test="item.treatmentType != null">#{item.treatmentType},</if>
            <if test="item.treatmentTime != null">#{item.treatmentTime}</if>
            <if test="item.visitId != null">,#{item.visitId}</if>
            <if test="item.reviewStatus != null">,#{item.reviewStatus}</if>
            <if test="item.delFlag != null">,#{item.delFlag}</if>
            <if test="item.createBy != null">,#{item.createBy}</if>
            <if test="item.createTime != null">,#{item.createTime}</if>
            )
        </foreach>
    </insert>

    <insert id="insertsubmitProduct">
        INSERT INTO rlct_product_use
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productUseName != null">product_use_name,</if>
            <if test="doctorId != null">doctor_id,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productUseName != null">#{productUseName},</if>
            <if test="doctorId != null">#{doctorId},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateRlctVisit" parameterType="RlctVisitVTwoDTO">
        update rlct_visit
        <trim prefix="SET" suffixOverrides=",">
            <if test="visitDetails != null">visit_details = #{visitDetails},</if>
            <if test="parentTeenId != null">parent_teen_id = #{parentTeenId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deductParentId != null">deduct_parent_id = #{deductParentId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="userPackageId != null">user_package_id = #{userPackageId},</if>
            <if test="beforeTreatmentImageUrl != null">before_treatment_image_url = #{beforeTreatmentImageUrl},</if>
            <if test="afterTreatmentImageUrl != null">after_treatment_image_url = #{afterTreatmentImageUrl},</if>
            <if test="salesId != null">sales_id = #{salesId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="healTime != null">heal_time = #{healTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="admissionTime !=null"> admission_time = #{admissionTime},</if>
            <if test="heightBeforeTreatment !=null">height_before_treatment = #{heightBeforeTreatment},</if>
            <if test="heightAfterTreatment !=null">height_after_treatment = #{heightAfterTreatment},</if>
            <if test="weightBeforeTreatment !=null">weight_before_treatment = #{weightBeforeTreatment},</if>
            <if test="weightAfterTreatment !=null">weight_after_treatment = #{weightAfterTreatment},</if>
        </trim>
        where visit_id = #{visitId}
    </update>

    <delete id="deleteRlctVisitByVisitId" parameterType="Long">
        delete from rlct_visit where visit_id = #{visitId}
    </delete>

    <delete id="deleteRlctVisitByVisitIds" parameterType="String">
        delete from rlct_visit where visit_id in
        <foreach item="visitId" collection="array" open="(" separator="," close=")">
            #{visitId}
        </foreach>
    </delete>

    <select id="getVisitList" resultType="com.ruoyi.teen.vo.RlctVisitVo">
        select rp.package_name,su_1.nick_name,su_2.phonenumber,rv.*
        from rlct_visit rv
                 left join rlct_parent_teen rpt on rv.parent_teen_id = rpt.id
                 left join rlct_packages rp on rv.package_id = rp.package_id
                 left join sys_user su_1 on su_1.user_id = rv.user_id
                 left join sys_user su_2 on su_2.user_id = rv.deduct_parent_id
        where rpt.teen_id = #{teenId}
        <if test="status != null">
            and rv.status = #{status}
        </if>

        order by rv.visit_id desc
    </select>
    <select id="selectRLctProductUseByVisitId" resultType="java.lang.Long">
        select user_product_id from rlct_product_use where visit_id = #{visitId}
    </select>

    <!-- 根据就诊ID和医生ID查询治疗记录 -->
    <select id="selectByVisitAndDoctor" resultType="com.ruoyi.teen.domain.RlctDoctorVisic">
        SELECT doctor_visic_id,doctor_id,treatment_type,treatment_time
        FROM rlct_doctor_visic
        WHERE visit_id = #{visitId}
          AND doctor_id = #{doctorId}
          AND del_flag = '0'
        AND review_status =  '0'
    </select>

    <!--获取该就诊单下所有未审核完成的医生数量-->
    <select id="countPendingDoctorsByVisit" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT rdv.doctor_id)
        FROM rlct_doctor_visic rdv
        WHERE visit_id = #{visitId}
          AND review_status = 0
          AND del_flag = '0'
    </select>
    <select id="selectByVisit" resultType="com.ruoyi.teen.domain.RlctDoctorVisic">
        SELECT doctor_visic_id,doctor_id,treatment_type,treatment_time
        FROM rlct_doctor_visic
        WHERE visit_id = #{visitId}
          AND del_flag = '0'
        AND review_status =  '0'
    </select>


    <update id="upIpadVist" parameterType="com.ruoyi.teen.vo.ipad.VisitVo">
        update rlct_visit
        <trim prefix="SET" suffixOverrides=",">
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="parentTeenId != null">parent_teen_id = #{parentTeenId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deductParentId != null">deduct_parent_id = #{deductParentId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where visit_id = #{visitId}
    </update>

    <update id="updateBeforeTreatmentImageUrl" parameterType="RlctVisit">
        update rlct_visit
        set before_treatment_image_url = #{beforeTreatmentImageUrl}
        where visit_id = #{visitId}
    </update>
    <update id="updateAfterTreatmentImageUrl" parameterType="RlctVisit">
        update rlct_visit
        set after_treatment_image_url = #{afterTreatmentImageUrl}
        where visit_id = #{visitId}
    </update>
    <update id="updateRLctProductUse" parameterType="com.ruoyi.teen.vo.ipad.ProductUseVo">
        update rlct_product_use
        <trim prefix="SET" suffixOverrides=",">
            <if test="productUseName != null">product_use_name = #{productUseName},</if>
            <if test="doctorId != null">doctor_id = #{doctorId},</if>
            <if test="parentTeenId != null">parent_teen_id = #{parentTeenId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_product_id = #{userProductId}
    </update>




    <update id="updateRlctDoctorVisic">
        UPDATE rlct_doctor_visic
        <set>
            <if test="doctorId != null">doctor_id = #{doctorId},</if>
            <if test="treatmentType != null">treatment_type = #{treatmentType},</if>
            <if test="treatmentTime != null">treatment_time = #{treatmentTime},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
        </set>
        WHERE visit_id = #{visitId}
    </update>

    <!-- 单条伪删除-->
    <update id="updateRlctDoctorVisicByVisitId" parameterType="com.ruoyi.teen.domain.RlctDoctorVisic">
        UPDATE rlct_doctor_visic
        SET del_flag = '2'
        <if test="record.deleteBy != null">,delete_by = #{record.deleteBy}</if>
        <if test="record.deleteTime != null">,delete_time = #{record.deleteTime}</if>
        WHERE doctor_visic_id = #{record.doctorVisicId}
    </update>

    <!-- 批量伪删除 -->
    <update id="batchSoftDelete">
        UPDATE rlct_doctor_visic
        SET del_flag = '2',
        delete_by = #{currentUserId},
        delete_time = #{now}
        WHERE doctor_visic_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateProductUseByuserProductId">
        UPDATE rlct_product_use
        <set>
            <if test="productUseName != null">product_use_name = #{productUseName},</if>
            <if test="doctorId != null">doctor_id = #{doctorId},</if>
            <if test="parentTeenId != null">parent_teen_id = #{parentTeenId},</if>
            <if test="visitId !=null ">visit_id = #{visitId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE user_product_id = #{userProductId}
    </update>
    <update id="updateRlctDoctorVisicByDoctorVisicId">
        UPDATE rlct_doctor_visic
        <set>
            <if test="treatmentTime != null">treatment_time = #{treatmentTime},</if>
        </set>
        WHERE doctor_visic_id = #{doctorVisicId}
    </update>

</mapper>
