<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.PCRlctTeenVTWOMapper">

    <resultMap type="com.ruoyi.teen.vo.pc.RlctTeenBoneAgeVo" id="RlctTeenResult">
        <result property="teenId"    column="teen_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="teenName"    column="teen_name"    />
        <result property="teenAge"    column="teen_age"    />
        <result property="teenSex"    column="teen_sex"    />
        <result property="teenBirth"    column="teen_birth"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="initHeight"    column="init_height"    />
        <result property="initWeight"    column="init_weight"    />
        <result property="initTime"    column="init_time"    />
        <result property="teenNameCh"    column="teen_name_ch"    />
        <result property="boneAgeReport" column="bone_age_report" />
    </resultMap>

    <resultMap id="RlctBoneAgeRecordMap" type="RlctBoneageDTO">
        <result property="boneAgeRecordId"    column="bone_age_record_id"    />
        <result property="parentTeenId"    column="parent_teen_id"    />
        <result property="teenAge"    column="teen_age"    />
        <result property="boneAge"    column="bone_age"    />
        <result property="predictedHeight"    column="predicted_height"    />
        <result property="captureTime"    column="capture_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />

    </resultMap>
    <insert id="insertRlctBoneAgeRecord" parameterType="RlctBoneageDTO" useGeneratedKeys="true" keyProperty="boneAgeRecordId">
        insert into rlct_bone_age_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="boneAge != null">bone_age,</if>
            <if test="parentTeenId !=null">parent_teen_id,</if>
            <if test="predictedHeight != null">predicted_height,</if>
            <if test="captureTime != null">capture_time,</if>
            <if test="boneAgeReport != null">bone_age_report,</if>
            <if test="teenAge != null">teen_age,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="boneAge != null">#{boneAge},</if>
            <if test="parentTeenId !=null">#{parentTeenId},</if>
            <if test="predictedHeight != null">#{predictedHeight},</if>
            <if test="captureTime != null">#{captureTime},</if>
            <if test="boneAgeReport != null">#{boneAgeReport},</if>
            <if test="teenAge != null">#{teenAge},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateRlctTeen" parameterType="com.ruoyi.teen.vo.pc.RlctTeenBoneAgeVo">
        update rlct_teen
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="teenName != null">teen_name = #{teenName},</if>
            <if test="teenAge != null">teen_age = #{teenAge},</if>
            <if test="teenAvatar != null">teen_avatar = #{teenAvatar},</if>
            <if test="teenSex != null">teen_sex = #{teenSex},</if>
            <if test="teenBirth != null">teen_birth = #{teenBirth},</if>
            <if test="teenSleepTime != null">teen_sleep_time = #{teenSleepTime},</if>
            <if test="teenSleepDurationTime != null">teen_sleep_duration_time = #{teenSleepDurationTime},</if>
            <if test="teenExerciseHabit != null">teen_exercise_habit = #{teenExerciseHabit},</if>
            <if test="teenEatHabit != null">teen_eat_habit = #{teenEatHabit},</if>
            <if test="teenNutrient != null">teen_nutrient = #{teenNutrient},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="initHeight != null">init_height = #{initHeight},</if>
            <if test="initWeight != null">init_weight = #{initWeight},</if>
            <if test="initTime != null">init_time = #{initTime},</if>
            <if test="teenNameCh != null">teen_name_ch = #{teenNameCh},</if>
        </trim>
        where teen_id = #{teenId}
    </update>

    <update id="updateRlctBoneAgeRecord">
        update  rlct_bone_age_record
        <trim prefix="set" suffixOverrides=",">
            <if test="boneAgeReport != null">bone_age_report = #{boneAgeReport},</if>
            <if test="boneAge != null">bone_age = #{boneAge},</if>
            <if test="teenAge != null">teen_age = #{teenAge},</if>
            <if test="predictedHeight != null">predicted_height = #{predictedHeight},</if>
            <if test="captureTime != null">capture_time = #{captureTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where bone_age_record_id = #{boneAgeRecordId}
    </update>
    <delete id="deleteRlctBoneAgeRecordByboneAgeRecordId">
        delete from rlct_bone_age_record where bone_age_record_id = #{boneAgeRecordId}
    </delete>


    <select id="selectRlctTeenList" parameterType="RlctTeen" resultMap="RlctTeenResult">
        SELECT t.teen_id, t.teen_name, t.teen_age, teen_sex, teen_birth, bone_age_report
        FROM rlct_teen t
        LEFT JOIN  rlct_parent_teen pt on t.teen_id = pt.teen_id
        LEFT JOIN teen.rlct_bone_age_record rbar on t.teen_id = rbar.parent_teen_id
        <where>
            t.del_flag = '0'
            <if test="teenName != null  and teenName != ''"> and t.teen_name like concat('%', #{teenName}, '%')</if>
            <if test="teenAge != null "> and t.teen_age = #{teenAge}</if>
            <if test="teenSex != null "> and t.teen_sex = #{teenSex}</if>
            <if test="teenBirth != null "> and t.teen_birth = #{teenBirth}</if>
        </where>
    </select>
    <select id="selectRlctBoneAgeRecordList" resultType="com.ruoyi.teen.domain.RlctBoneageDTO">
        SELECT bone_age_record_id,bone_age,predicted_height,capture_time,bone_age_report,rbar.teen_age
        FROM rlct_bone_age_record rbar
            LEFT JOIN rlct_parent_teen on rlct_parent_teen.id = rbar.parent_teen_id
        WHERE rlct_parent_teen.teen_id = #{teenId}
    </select>
    <select id="selectRlctBoneAgeRecordByboneAgeRecordId" resultType="com.ruoyi.teen.domain.RlctBoneageDTO">
        SELECT bone_age_record_id,bone_age,predicted_height,capture_time,bone_age_report,rbar.teen_age AS teenAge
        FROM rlct_bone_age_record rbar
        WHERE rbar.bone_age_record_id = #{boneAgeRecordId}
    </select>

</mapper>
