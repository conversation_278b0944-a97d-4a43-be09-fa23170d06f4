<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctConfigMapper">

    <resultMap type="RlctConfig" id="RlctConfigResult">
        <result property="id"    column="id"    />
        <result property="attribute"    column="attribute"    />
        <result property="param"    column="param"    />
        <result property="score"    column="score"    />
        <result property="sort"    column="sort"    />
        <result property="configType"    column="config_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRlctConfigVo">
        select id, attribute, param, score, sort, config_type, del_flag, create_by, create_time, update_by, update_time from rlct_config
    </sql>

    <select id="selectRlctConfigList" parameterType="RlctConfig" resultMap="RlctConfigResult">
        <include refid="selectRlctConfigVo"/>
        <where>
            <if test="configType != null  and configType != ''"> and config_type = #{configType}</if>
        </where>
        order by config_type,attribute,sort
    </select>

    <select id="selectRlctConfigById" parameterType="Long" resultMap="RlctConfigResult">
        <include refid="selectRlctConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertRlctConfig" parameterType="RlctConfig" useGeneratedKeys="true" keyProperty="id">
        insert into rlct_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="attribute != null">attribute,</if>
            <if test="param != null">param,</if>
            <if test="score != null">score,</if>
            <if test="sort != null">sort,</if>
            <if test="configType != null">config_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="attribute != null">#{attribute},</if>
            <if test="param != null">#{param},</if>
            <if test="score != null">#{score},</if>
            <if test="sort != null">#{sort},</if>
            <if test="configType != null">#{configType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRlctConfig" parameterType="RlctConfig">
        update rlct_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="attribute != null">attribute = #{attribute},</if>
            <if test="param != null">param = #{param},</if>
            <if test="score != null">score = #{score},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="configType != null">config_type = #{configType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlctConfigById" parameterType="Long">
        delete from rlct_config where id = #{id}
    </delete>

    <delete id="deleteRlctConfigByIds" parameterType="String">
        delete from rlct_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
