<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.teen.mapper.pc.PCRlctVersionMapper">

    <resultMap id="RlctPadVersionRequest" type="RlctPadVersion">
        <result property="versionId" column="version_id"/>
        <result property="versionNumber" column="version_number"/>
        <result property="versionUrl" column="version_url"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <insert id="insertRlctVersion"  parameterType="RlctPadVersion" useGeneratedKeys="true" keyProperty="versionId">
        insert into rlct_version_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="versionNumber != null">version_number,</if>
            <if test="versionUrl !=null">version_url,</if>
            <if test="description != null">description,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="versionNumber != null">#{versionNumber},</if>
            <if test="versionUrl !=null">#{versionUrl},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <update id="updateRlctVersion">
        update rlct_version_history
        <set>
            <if test="versionNumber != null">version_number = #{versionNumber},</if>
            <if test="versionUrl !=null">version_url = #{versionUrl},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy !=null">create_by = #{createBy},</if>
            <if test="createTime !=null">create_time = #{createTime},</if>
            <if test="updateBy !=null">update_by = #{updateBy},</if>
            <if test="updateTime !=null">update_time = #{updateTime},</if>
        </set>
        where version_id = #{versionId}
    </update>
    <delete id="deleteRlctVersionByVersionIds">
        delete from rlct_version_history where version_id in
        <foreach item="versionId" collection="array" open="(" separator="," close=")">
            #{versionId}
        </foreach>
    </delete>
    <select id="selectRlctVersionList" resultType="com.ruoyi.teen.domain.RlctPadVersion">
        select version_id,version_number,description,status,version_url,del_flag,create_by,create_time,update_by,update_time
        from rlct_version_history
        <where>
            <if test="versionNumber != null and versionNumber != ''">and version_number like concat('%', #{versionNumber}, '%')</if>
            <if test="description != null and description != ''">and description like concat('%', #{description}, '%')</if>
            <if test="status != null and status != ''">and status = #{status}</if>
        </where>
    </select>
    <select id="selectRlctVersionByVersionId" resultType="com.ruoyi.teen.domain.RlctPadVersion">
        select version_id,version_number,description,status,version_url,del_flag,create_by,create_time,update_by,update_time
        from rlct_version_history
        where version_id = #{versionId}
    </select>
</mapper>
