<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctTeenWightHeightMapper">

    <resultMap type="com.ruoyi.teen.domain.RlctTeenWightHeight" id="RlctTeenWightHeightResult">
        <result property="id"    column="id"    />
        <result property="parentTeenId"    column="parent_teen_id"    />
        <result property="weight"    column="weight"    />
        <result property="height"    column="height"    />
        <result property="correctHeight" column="correct_height" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRlctTeenWightHeightVo">
        select id, parent_teen_id, weight, height, create_by, create_time from rlct_teen_wight_height
    </sql>

    <select id="selectRlctTeenWightHeightList" parameterType="RlctTeenWightHeight" resultMap="RlctTeenWightHeightResult">
        <include refid="selectRlctTeenWightHeightVo"/>
        <where>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="height != null "> and height = #{height}</if>
        </where>
    </select>

    <select id="selectRlctTeenWightHeightById" parameterType="Long" resultMap="RlctTeenWightHeightResult">
        <include refid="selectRlctTeenWightHeightVo"/>
        where id = #{id}
    </select>
    <select id="getLastRlctTeenWightHeight" parameterType="Long" resultMap="RlctTeenWightHeightResult">
        select id, parent_teen_id, weight, height, create_by, create_time
        from rlct_teen_wight_height
        where parent_teen_id = #{parentTeenId}
        order by id desc LIMIT 1
    </select>

    <insert id="insertRlctTeenWightHeight" parameterType="RlctTeenWightHeight" useGeneratedKeys="true" keyProperty="id">
        insert into rlct_teen_wight_height
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="weight != null">weight,</if>
            <if test="height != null">height,</if>
            <if test="correctHeight != null">correct_Height,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="weight != null">#{weight},</if>
            <if test="height != null">#{height},</if>
            <if test="correctHeight != null">#{correctHeight},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRlctTeenWightHeight" parameterType="RlctTeenWightHeight">
        update rlct_teen_wight_height
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentTeenId != null">parent_teen_id = #{parentTeenId},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="height != null">height = #{height},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlctTeenWightHeightById" parameterType="Long">
        delete from rlct_teen_wight_height where id = #{id}
    </delete>

    <delete id="deleteRlctTeenWightHeightByIds" parameterType="String">
        delete from rlct_teen_wight_height where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getBodyInfo" parameterType="RlctParentTeen" resultMap="RlctTeenWightHeightResult">
        select rtwh.*
        from rlct_parent_teen rpt
                 right join rlct_teen_wight_height rtwh on rtwh.parent_teen_id = rpt.id
        where rpt.parent_id = #{parentId} and rpt.teen_id = #{teenId}
        <choose>
            <when test="type == '1'">
                <!-- 初诊的身高体重 -->
                order by rtwh.create_time asc limit 1
            </when>
            <otherwise>
                <!-- 成长报告的身高体重 -->
                order by rtwh.create_time desc limit 10
            </otherwise>
        </choose>
    </select>

<!--    <select id="BodyInfo" parameterType="RlctParentTeen" resultMap="RlctTeenWightHeightResult">-->
<!--        select rtwh.*-->
<!--        from rlct_parent_teen rpt-->
<!--        right join rlct_teen_wight_height rtwh on rtwh.parent_teen_id = rpt.id-->
<!--        where rpt.parent_id = #{parentId} and rpt.teen_id = #{teenId}-->
<!--        <choose>-->
<!--            <when test="type == '1'">-->
<!--                &lt;!&ndash; 初诊的身高体重 &ndash;&gt;-->
<!--                order by rtwh.create_time asc limit 1-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                &lt;!&ndash; 成长报告的身高体重 &ndash;&gt;-->
<!--                order by rtwh.create_time desc limit 10-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--    </select>-->
    <select id="getHeight" resultMap="RlctTeenWightHeightResult" parameterType="Long">
        SELECT
            rtw.parent_teen_id,
            rtw.height,
            rtw.weight,
            rtw.create_time
        FROM
            rlct_teen_wight_height AS rtw
        WHERE rtw.parent_teen_id=#{parentTeenId}
        ORDER BY rtw.id desc LIMIT 0,#{n}
    </select>

    <select id="countByParentTeenId" resultType="java.lang.Integer" parameterType="Long">
        SELECT
            count(1)
        FROM
            rlct_teen_wight_height t
        WHERE t.parent_teen_id=#{parentTeenId}
    </select>
</mapper>
