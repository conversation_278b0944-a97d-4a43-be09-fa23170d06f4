<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctAuthorizeMapper">

    <resultMap type="RlctAuthorize" id="RlctAuthorizeResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="authorizeCode"    column="authorize_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="creater"    column="creater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
    </resultMap>

    <sql id="selectRlctAuthorizeVo">
        select id, user_id, authorize_code, create_time, creater, update_time, updater from rlct_authorize
    </sql>

    <select id="selectRlctAuthorizeList" parameterType="RlctAuthorize" resultMap="RlctAuthorizeResult">
        <include refid="selectRlctAuthorizeVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="authorizeCode != null  and authorizeCode != ''"> and authorize_code = #{authorizeCode}</if>
            <if test="creater != null  and creater != ''"> and creater = #{creater}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>

    <select id="selectRlctAuthorizeById" parameterType="Long" resultMap="RlctAuthorizeResult">
        <include refid="selectRlctAuthorizeVo"/>
        where id = #{id}
    </select>
    <select id="selectByUserId" parameterType="Long" resultMap="RlctAuthorizeResult">
        <include refid="selectRlctAuthorizeVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectByCode" parameterType="java.lang.String" resultMap="RlctAuthorizeResult">
        <include refid="selectRlctAuthorizeVo"/>
        where authorize_code = #{code}
    </select>

    <insert id="insertRlctAuthorize" parameterType="RlctAuthorize" useGeneratedKeys="true" keyProperty="id">
        insert into rlct_authorize
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="authorizeCode != null and authorizeCode != ''">authorize_code,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creater != null and creater != ''">creater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null and updater != ''">updater,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="authorizeCode != null and authorizeCode != ''">#{authorizeCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creater != null and creater != ''">#{creater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null and updater != ''">#{updater},</if>
         </trim>
    </insert>

    <update id="updateRlctAuthorize" parameterType="RlctAuthorize">
        update rlct_authorize
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="authorizeCode != null and authorizeCode != ''">authorize_code = #{authorizeCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creater != null and creater != ''">creater = #{creater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
        </trim>
        where id = #{id}
    </update>


    <update id="updateByUserId" parameterType="RlctAuthorize">
        update rlct_authorize
        <trim prefix="SET" suffixOverrides=",">
            <if test="authorizeCode != null and authorizeCode != ''">authorize_code = #{authorizeCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creater != null and creater != ''">creater = #{creater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteRlctAuthorizeById" parameterType="Long">
        delete from rlct_authorize where id = #{id}
    </delete>

    <delete id="deleteRlctAuthorizeByIds" parameterType="String">
        delete from rlct_authorize where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
