<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctVisitRecordsMapper">

    <resultMap type="com.ruoyi.teen.vo.ipad.VisitRecordsVo" id="VisitRecordsVoResult">
        <result column="parent_teen_id" property="parentTeenId"></result>
        <result column="teen_name" property="teenName"/>
        <result column="admission_time" property="admissionTime"/>
        <result column="height_before_treatment" property="heightBeforeTreatment"/>
        <result column="height_after_treatment" property="heightAfterTreatment"/>
        <result column="weight_after_treatment" property="weightAfterTreatment"/>
        <result column="init_time" property="initTime"/>
        <result column="init_height" property="initHeight"/>
        <result column="package_number_times" property="packageNumberTimes"/>
        <result column="remaining_times" property="remainingTimes"/>
        <result column="height_growth" property="heightGrowth"/>
    </resultMap>

    <select id="selectRlctVisitRecordsList" resultType="java.lang.Long">
        select visit_id from rlct_visit
        <where>
            (status = 0 OR status = 1)
            <if test="startDate != null">
                AND admission_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND admission_time &lt; #{endDate}
            </if>
        </where>
        ORDER BY
        admission_time DESC;
    </select>
    <select id="selectByVisitId" resultMap="VisitRecordsVoResult">
        SELECT
        rt.teen_name,
        rv.parent_teen_id,
        rv.admission_time,
        rv.height_before_treatment,
        rv.height_after_treatment,
        rv.weight_after_treatment,
        rt.init_time,
        rt.init_height,
        rup.package_number_times,
        (rup.package_total_times - rup.package_number_times) AS remaining_times,
        (rv.height_after_treatment - rv.height_before_treatment) AS height_growth
        FROM
        rlct_visit rv
        LEFT JOIN rlct_parent_teen rpt ON rpt.id = rv.parent_teen_id
        LEFT JOIN rlct_teen rt ON rpt.teen_id = rt.teen_id
        LEFT JOIN rlct_user_package rup ON rv.user_package_id = rup.id
        <where>
            <if test="visitIdList != null and !visitIdList.isEmpty()">
                rv.visit_id IN
                <foreach item="id" collection="visitIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="visitIdList == null or visitIdList.isEmpty()">
                1=0 <!-- 当没有ID时返回空结果 -->
            </if>
        -- 添加其他条件
        AND del_flag =  '0'
        </where>
        ORDER BY rv.admission_time DESC
    </select>
</mapper>
