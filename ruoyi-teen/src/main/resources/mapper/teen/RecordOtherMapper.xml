<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RecordOtherMapper">

    <resultMap type="RecordOther" id="RecordOtherResult">
        <result property="recordOtherId"    column="record_other_id"    />
        <result property="visitSource"    column="visit_source"    />
        <result property="referee"    column="referee"    />
        <result property="messagePush"    column="message_push"    />
        <result property="parentTeenId"    column="parent_teen_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>


    <insert id="insertRecordOther" parameterType="RecordOther">
        insert into rlct_record_other
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="visitSource != null">visit_source,</if>
            <if test="referee != null">referee,</if>
            <if test="messagePush != null">message_push,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordOtherId != null">#{recordOtherId},</if>
            <if test="visitSource != null">#{visitSource},</if>
            <if test="referee != null">#{referee},</if>
            <if test="messagePush != null">#{messagePush},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

</mapper>
