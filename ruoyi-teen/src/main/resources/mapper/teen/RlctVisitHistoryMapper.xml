<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctVisitHistoryMapper">

    <resultMap type="RlctVisitHistory" id="RlctVisitHistoryResult">
        <result property="id"    column="id"    />
        <result property="visitId"    column="visit_id"    />
        <result property="beforeTreatmentImageUrl"    column="before_treatment_image_url"    />
        <result property="visitBeforeUpdateImageUrl"    column="visit_before_update_image_url"    />
        <result property="afterTreatmentImageUrl"    column="after_treatment_image_url"    />
        <result property="visitAfterUpdateImageUrl"    column="visit_after_update_image_url"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRlctVisitHistoryVo">
        select id, visit_id, before_treatment_image_url, visit_before_update_image_url, after_treatment_image_url, visit_after_update_image_url, update_by, update_time, remark from rlct_visit_history
    </sql>

    <select id="selectRlctVisitHistoryList" parameterType="RlctVisitHistory" resultMap="RlctVisitHistoryResult">
        <include refid="selectRlctVisitHistoryVo"/>
        <where>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="visitId != null "> and visit_id = #{visitId}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectRlctVisitHistoryById" parameterType="Long" resultMap="RlctVisitHistoryResult">
        <include refid="selectRlctVisitHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertRlctVisitHistory" parameterType="RlctVisitHistory" useGeneratedKeys="true" keyProperty="id">
        insert into rlct_visit_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="visitId != null">visit_id,</if>
            <if test="beforeTreatmentImageUrl != null">before_treatment_image_url,</if>
            <if test="visitBeforeUpdateImageUrl != null">visit_before_update_image_url,</if>
            <if test="afterTreatmentImageUrl != null">after_treatment_image_url,</if>
            <if test="visitAfterUpdateImageUrl != null">visit_after_update_image_url,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="visitId != null">#{visitId},</if>
            <if test="beforeTreatmentImageUrl != null">#{beforeTreatmentImageUrl},</if>
            <if test="visitBeforeUpdateImageUrl != null">#{visitBeforeUpdateImageUrl},</if>
            <if test="afterTreatmentImageUrl != null">#{afterTreatmentImageUrl},</if>
            <if test="visitAfterUpdateImageUrl != null">#{visitAfterUpdateImageUrl},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRlctVisitHistory" parameterType="RlctVisitHistory">
        update rlct_visit_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="visitId != null">visit_id = #{visitId},</if>
            <if test="beforeTreatmentImageUrl != null">before_treatment_image_url = #{beforeTreatmentImageUrl},</if>
            <if test="visitBeforeUpdateImageUrl != null">visit_before_update_image_url = #{visitBeforeUpdateImageUrl},</if>
            <if test="afterTreatmentImageUrl != null">after_treatment_image_url = #{afterTreatmentImageUrl},</if>
            <if test="visitAfterUpdateImageUrl != null">visit_after_update_image_url = #{visitAfterUpdateImageUrl},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
<!--    <insert id="finishVist" parameterType="Long" useGeneratedKeys="true" keyProperty="id">-->
<!--        insert into rlct_visit_history-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            <if test="visitId != null">visit_id,</if>-->
<!--        </trim>-->
<!--        <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--            <if test="visitId != null">#{visitId},</if>-->
<!--        </trim>-->
<!--    </insert>-->

    <delete id="deleteRlctVisitHistoryById" parameterType="Long">
        delete from rlct_visit_history where id = #{id}
    </delete>

    <delete id="deleteRlctVisitHistoryByIds" parameterType="String">
        delete from rlct_visit_history where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
