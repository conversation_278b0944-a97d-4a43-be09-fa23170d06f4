<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctDoctorVisicMapper">
    <resultMap id="RlctDoctorVisicResult" type="RlctDoctorVisic">
        <result property="doctorVisicId" column="doctor_visic_id" />
        <result property="visitId" column="visit_id" />
        <result property="doctorId" column="doctor_id" />
        <result property="treatmentType" column="treatment_type"/>
        <result property="reviewStatus" column="review_status"/>
    </resultMap>
    <resultMap id="RlctDoctorVisicVoResult" type="com.ruoyi.teen.vo.ipad.RlctDoctorVisicVo">
        <result property="doctorVisicId" column="doctor_visic_id" />
        <result property="nickName" column="nick_name" />
        <result property="teenName" column="teen_name" />
        <result property="treatmentTime" column="treatment_time"/>
        <result property="reviewStatus" column="review_status"/>
        <result property="userPackageId" column="user_package_id"/>
        <result property="healTime" column="heal_time"/>
    </resultMap>
    <resultMap id="RlcttreatmentTypeListDTO" type="com.ruoyi.teen.domain.RlcttreatmentTypeListDTO">
        <result property="doctorVisicId" column="doctor_visic_id" />
        <result property="treatmentType" column="treatment_type" />
    </resultMap>
    <resultMap id="RlctDoctorVisicListResult" type="com.ruoyi.teen.vo.ipad.RlctDoctorVisicVo">
        <result property="doctorVisicId" column="doctor_visic_id" />
        <result property="visitId" column="visit_id" />
        <result property="doctorId" column="doctor_id" />
        <result property="nickName" column="nick_name" />
        <result property="teenName" column="teen_name" />
        <result property="treatmentTime" column="treatment_time"/>
        <result property="userPackageId" column="user_package_id"/>
        <result property="reviewTime" column="review_time"/>
    </resultMap>


    <update id="updateRlctDoctorVisicStatus">
        update rlct_doctor_visic
        <trim prefix="SET" suffixOverrides=",">
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="healTime != null">heal_time = #{healTime},</if>
        </trim>
        where doctor_visic_id = #{doctorVisicId}
    </update>

    <!--根据关系id记录删除单记录-->
    <update id="updateRlctDoctorVisic">
        update rlct_doctor_visic
        SET del_flag = '2',
        delete_by = #{currentUserId},
        delete_time = #{now}
        where doctor_visic_id = #{doctorVisicId}
    </update>

    <!-- 根据id批量审核通过-->
    <update id="batchUpdateRlctDoctorVisicStatus">
        UPDATE rlct_doctor_visic
        <set>
            <if test="cancelReviewTime != null">cancel_review_time = #{cancelReviewTime},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="currentUserId != null">review_id = #{currentUserId},</if>
            <if test="cancelReviewTime != null">cancel_review_time = #{cancelReviewTime},</if>
        </set>
        WHERE
        doctor_visic_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--显示基本信息-->
    <select id="selectRlctDoctorVisicNoCheckList" resultMap="RlctDoctorVisicListResult">
        SELECT
            dv.visit_id,
            dv.doctor_id,
            u.nick_name,
            t.teen_name,
            rv.user_package_id,
            dv.treatment_time,
            dv.review_time
        FROM
            rlct_doctor_visic dv
                JOIN
            sys_user u ON dv.doctor_id = u.user_id
                JOIN
            teen.rlct_visit rv ON dv.visit_id = rv.visit_id
                JOIN
            rlct_parent_teen pt ON rv.parent_teen_id = pt.id
                JOIN
            rlct_teen t ON pt.teen_id = t.teen_id
        WHERE
            dv.del_flag = '0'
          AND rv.status IN (0, 1)
          AND dv.review_status = #{auditStatus}
        GROUP BY dv.visit_id, dv.doctor_id, u.nick_name, t.teen_name,
                 rv.user_package_id, rv.heal_time, dv.treatment_time, dv.review_time
        ORDER BY
            dv.treatment_time DESC
    </select>
    <select id="selectRlctDoctorVisicTreatmentType" resultType="com.ruoyi.teen.domain.RlcttreatmentTypeListDTO">
        SELECT
        doctor_visic_id,
        treatment_type
        FROM
        rlct_doctor_visic
        <where>
            del_flag = '0'
            <if test="doctorId !=null">
                AND doctor_id = #{doctorId}
            </if>
            <if test="visitId != null">
                AND visit_id = #{visitId}
            </if>
        </where>
    </select>
    <select id="selectAllTreatmentsForVisits" resultType="com.ruoyi.teen.domain.RlcttreatmentTypeListDTO">
        SELECT
        visit_id,
        doctor_id,
        doctor_visic_id,
        treatment_type,
        review_time
        FROM
        rlct_doctor_visic
        WHERE
        del_flag = '0'
        AND visit_id IN
        <foreach collection="visitIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND doctor_id IN
        <foreach collection="doctorIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>
