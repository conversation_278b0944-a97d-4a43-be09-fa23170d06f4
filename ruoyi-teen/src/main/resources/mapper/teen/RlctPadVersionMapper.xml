<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctPadVersionMapper">

    <resultMap id="RlctPadVersionResult" type="RlctPadVersion">
        <result property="versionId" column="version_id"/>
        <result property="versionNumber" column="version_number"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="versionUrl" column="version_url"/>
        <result property="searchValue" column="search_value"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="selectLatestVersion" resultMap="RlctPadVersionResult">
        SELECT version_id,  version_number, description, status, version_url, del_flag, create_by, create_time, update_by, update_time
        FROM rlct_version_history
        WHERE
            version_number = #{versionNumber}
          AND del_flag = '0'
          AND status = '1'
    </select>
    <select id="selectNewestVersion" resultType="com.ruoyi.teen.domain.RlctPadVersion">
        SELECT version_id,  version_number, description, status, version_url, del_flag, create_by, create_time, update_by, update_time
        FROM rlct_version_history
        WHERE
            del_flag = '0'
          AND status = '1'
        ORDER BY create_time
        DESC LIMIT 1
    </select>
</mapper>
