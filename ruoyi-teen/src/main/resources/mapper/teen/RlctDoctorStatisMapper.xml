<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctDoctorStatisMapper">

    <resultMap id="RlctPersonalInfoResult" type="com.ruoyi.teen.vo.patient.RlctPersonalInfoVo">
        <result property="nickName" column="nick_name" />
        <result property="sex" column="sex" />
        <result property="phonenumber" column="phonenumber" />
        <result property="avatar" column="avatar" />
        <result property="clinicName" column="dept_name" />
    </resultMap>

    <resultMap id="RlctTodayPerformanceResult" type="com.ruoyi.teen.domain.RlctTodayPerformanceDTO">
<!--        <result property="receptionNumber" column="reception_number" />
        <result property="aircure" column="aircure" />
        <result property="specialTraining" column="special_training" />
        <result property="noseScratch" column="nose_scratch" />
        <result property="pointerAcupoints" column="pointer_acupoints" />
        <result property="jointRelease" column="joint_release" />-->
        <result property="treatmentType" column="treatment_type" />
        <result property="count" column="count" />
    </resultMap>

    <select id="selectDoctorPersonalInfo" resultMap="RlctPersonalInfoResult">
        SELECT
            nick_name,
            sex,
            phonenumber,
            avatar,
            dept_name
        FROM
            sys_user su
                LEFT JOIN
            sys_dept sd ON su.dept_id = sd.dept_id
        WHERE
            su.status = '0' AND
            su.del_flag = '0' AND
            su.user_id = #{userId}
    </select>

    <select id="selectPerformance" resultMap="RlctTodayPerformanceResult">
        SELECT
            treatment_type,
            COUNT(*) AS count
        FROM
            rlct_doctor_visic rdv
            LEFT JOIN
            sys_user su ON su.user_id = rdv.doctor_id
            LEFT JOIN
            rlct_visit rv ON rv.visit_id = rdv.visit_id
        WHERE
            su.user_id = #{userId}
          AND su.status = '0'
          AND su.del_flag = '0'
          AND rdv.review_status = '1'
          AND rv.heal_time &gt;= #{startDate}
          AND rv.heal_time &lt; #{endDate}
        GROUP BY
            treatment_type
        ORDER BY
            treatment_type;
    </select>


    <select id="selectPeoplePerformance" resultType="BigDecimal">
        SELECT
            COUNT(*) AS peopleCount
        FROM
            rlct_visit rv
        WHERE
        rv.sales_id = #{userId}
        AND rv.heal_time &gt;= #{startDate}
        AND rv.heal_time &lt; #{endDate}
        AND rv.status = 1
    </select>
</mapper>
