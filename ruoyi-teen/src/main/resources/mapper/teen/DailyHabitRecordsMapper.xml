<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.DailyHabitRecordsMapper">

    <resultMap type="com.ruoyi.teen.vo.ipad.DailyHabitRecordsVo" id="DailyHabitRecordsVoResult">
        <result property="bedtime"    column="bedtime"    />
        <result property="sleepDuration"    column="sleep_duration"    />
        <result property="sleepOther"    column="sleep_other"    />
        <result property="exerciseFrequency"    column="exercise_frequency"    />
        <result property="exerciseDuration"    column="exercise_duration"    />
        <result property="exerciseOther"    column="exercise_other"    />
        <result property="eatingHabits"    column="eating_habits"    />
        <result property="eatingOther"    column="eating_other"    />
        <result property="calcium"    column="calcium"    />
        <result property="vitaminA"    column="vitamin_a"    />
        <result property="vitaminD"    column="vitamin_d"    />
        <result property="multivitamin"    column="multivitamin"    />
        <result property="gaba"    column="gaba"    />
        <result property="nutritionOther"    column="nutrition_other"    />
    </resultMap>


    <update id="updateDailyHabitRecordsByhabitId" parameterType="DailyHabitRecords">
        UPDATE rlct_daily_habit_records
        <set>
            <if test="parentTeenId != null">parent_teen_id = #{parentTeenId},</if>
            <if test="bedtime != null">bedtime = #{bedtime},</if>
            <if test="sleepDuration != null">sleep_duration = #{sleepDuration},</if>
            <if test="sleepOther != null">sleep_other = #{sleepOther},</if>
            <if test="exerciseFrequency != null">exercise_frequency = #{exerciseFrequency},</if>
            <if test="exerciseDuration != null">exercise_duration = #{exerciseDuration},</if>
            <if test="exerciseOther != null">exercise_other = #{exerciseOther},</if>
            <if test="eatingHabits != null">eating_habits = #{eatingHabits},</if>
            <if test="eatingOther != null">eating_other = #{eatingOther},</if>
            <if test="calcium != null">calcium = #{calcium},</if>
            <if test="vitaminA != null">vitamin_a = #{vitaminA},</if>
            <if test="vitaminD != null">vitamin_d = #{vitaminD},</if>
            <if test="multivitamin != null">multivitamin = #{multivitamin},</if>
            <if test="gaba != null">gaba = #{gaba},</if>
            <if test="nutritionOther != null">nutrition_other = #{nutritionOther},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE habit_id = #{habitId}
    </update>



    <!--
        新增就诊表 获取日常行为记录
    -->
    <select id="selectDailyHabitRecordsParentTeenIdList" resultMap="DailyHabitRecordsVoResult">
        SELECT
        habit_id,
        parent_teen_id,
        bedtime,
        sleep_duration,
        sleep_other,
        exercise_frequency,
        exercise_duration,
        exercise_other,
        eating_habits,
        eating_other,
        calcium,
        vitamin_a,
        vitamin_d,
        multivitamin,
        gaba,
        nutrition_other
        FROM rlct_daily_habit_records
        WHERE
            parent_teen_id = #{parent_teen_id}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="selectDailyHabitRecordsByVisitId" resultMap="DailyHabitRecordsVoResult">
        SELECT
            habit_id,
            parent_teen_id,
            bedtime,
            sleep_duration,
            sleep_other,
            exercise_frequency,
            exercise_duration,
            exercise_other,
            eating_habits,
            eating_other,
            calcium,
            vitamin_a,
            vitamin_d,
            multivitamin,
            gaba,
            nutrition_other
        FROM rlct_daily_habit_records
        WHERE
            visit_id = #{visitId}
    </select>
    <select id="selectDailyHabitRecordsHabitIdByVisitId" resultType="java.lang.Long">
        SELECT habit_id FROM rlct_daily_habit_records WHERE visit_id = #{visitId}
    </select>

    <insert id="insertDailyHabitRecords" parameterType="DailyHabitRecords" useGeneratedKeys="true" keyProperty="habitId">
        insert into rlct_daily_habit_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="visitId != null">visit_id,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="bedtime != null">bedtime,</if>
            <if test="sleepDuration != null">sleep_duration,</if>
            <if test="sleepOther != null">sleep_other,</if>
            <if test="exerciseFrequency != null">exercise_frequency,</if>
            <if test="exerciseDuration != null">exercise_duration,</if>
            <if test="exerciseOther != null">exercise_other,</if>
            <if test="eatingHabits != null">eating_habits,</if>
            <if test="eatingOther != null">eating_other,</if>
            <if test="calcium != null">calcium,</if>
            <if test="vitaminA != null">vitamin_a,</if>
            <if test="vitaminD != null">vitamin_d,</if>
            <if test="multivitamin != null">multivitamin,</if>
            <if test="gaba != null">gaba,</if>
            <if test="nutritionOther != null">nutrition_other,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="visitId != null">#{visitId},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="bedtime != null">#{bedtime},</if>
            <if test="sleepDuration != null">#{sleepDuration},</if>
            <if test="sleepOther != null">#{sleepOther},</if>
            <if test="exerciseFrequency != null">#{exerciseFrequency},</if>
            <if test="exerciseDuration != null">#{exerciseDuration},</if>
            <if test="exerciseOther != null">#{exerciseOther},</if>
            <if test="eatingHabits != null">#{eatingHabits},</if>
            <if test="eatingOther != null">#{eatingOther},</if>
            <if test="calcium != null">#{calcium},</if>
            <if test="vitaminA != null">#{vitaminA},</if>
            <if test="vitaminD != null">#{vitaminD},</if>
            <if test="multivitamin != null">#{multivitamin},</if>
            <if test="gaba != null">#{gaba},</if>
            <if test="nutritionOther != null">#{nutritionOther},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

</mapper>
