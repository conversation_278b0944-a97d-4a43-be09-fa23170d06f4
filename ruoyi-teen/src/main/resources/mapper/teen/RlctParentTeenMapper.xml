<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctParentTeenMapper">

    <resultMap type="RlctParentTeen" id="RlctParentTeenResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="teenId"    column="teen_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRlctParentTeenVo">
        select id, parent_id, teen_id, create_time from rlct_parent_teen
    </sql>

    <select id="selectRlctParentTeenList" parameterType="RlctParentTeen" resultMap="RlctParentTeenResult">
        <include refid="selectRlctParentTeenVo"/>
    </select>

    <select id="selectByParentIdAndTeenId"  resultMap="RlctParentTeenResult">
        <include refid="selectRlctParentTeenVo"/>
        where parent_id = #{parentId} and teen_id = #{teenId}
        order by id asc
        limit 1
    </select>

    <select id="selectRlctParentTeenById" parameterType="Long" resultMap="RlctParentTeenResult">
        <include refid="selectRlctParentTeenVo"/>
        where id = #{id}
    </select>
    <select id="selectByparentTeenId" resultType="java.lang.Long">
        select parent_id
        from rlct_parent_teen
                 JOIN sys_user on rlct_parent_teen.parent_id = sys_user.user_id
        where id = #{parentTeenId}
          AND sys_user.status = 0
          AND  sys_user.del_flag = 0
    </select>

    <insert id="insertRlctParentTeen" parameterType="RlctParentTeen">
        insert into rlct_parent_teen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="teenId != null">teen_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="teenId != null">#{teenId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRlctParentTeen" parameterType="RlctParentTeen">
        update rlct_parent_teen
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="teenId != null">teen_id = #{teenId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlctParentTeenById" parameterType="Long">
        delete from rlct_parent_teen where id = #{id}
    </delete>

    <delete id="deleteRlctParentTeenByIds" parameterType="String">
        delete from rlct_parent_teen where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
