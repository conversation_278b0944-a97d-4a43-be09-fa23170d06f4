<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.TeenStatisticMapper">



    <select id="statisticVisitTimes" parameterType="java.lang.String" resultType="com.ruoyi.teen.domain.statistic.TeenStatisticBean">
        select rt.teen_name name,DATE_FORMAT(rv.create_time,'%Y-%m') month,count(1) count
        from rlct_visit rv
        inner join rlct_parent_teen rp on rp.id = rv.parent_teen_id
        inner join rlct_teen rt on rt.teen_id = rp.teen_id
        inner join sys_user su on su.user_id = rp.parent_id
        where su.phonenumber = #{tel}
        group by rt.teen_id,rt.teen_name,DATE_FORMAT(rv.create_time,'%Y-%m')

    </select>

    <select id="statisticGroupUp" parameterType="java.lang.String" resultType="com.ruoyi.teen.domain.statistic.TeenStatisticHeightBean">
        select rt.teen_name name,DATE_FORMAT(t.create_time,'%Y-%m') month,MAX(t.correct_height) height from rlct_teen_wight_height t
        inner join rlct_parent_teen rp on rp.id = t.parent_teen_id
        inner join rlct_teen rt on rt.teen_id = rp.teen_id
        inner join sys_user su on su.user_id = rp.parent_id
        where su.phonenumber = #{tel}
        group by rt.teen_id,rt.teen_name,DATE_FORMAT(t.create_time,'%Y-%m')

    </select>

    <select id="statisticHeightWightByTeenId" parameterType="java.lang.Long" resultType="com.ruoyi.teen.domain.statistic.HeightWightBean">
        select rp.teen_id teenId,t.correct_height height,t.weight,t.create_time createTime
        from rlct_teen_wight_height t
        inner join rlct_parent_teen rp on rp.id = t.parent_teen_id
        where rp.teen_id = #{teenId}
        order by t.create_time desc
            limit 10
    </select>

    <select id="statisticVisitTimesByTeenId" parameterType="java.lang.Long" resultType="com.ruoyi.teen.domain.statistic.TeenStatisticBean">
        select DATE_FORMAT(rv.create_time,'%Y-%m') month,count(1) count from rlct_visit rv
        inner join rlct_parent_teen rp on rp.id = rv.parent_teen_id
        where rp.teen_id = #{teenId}
        group by DATE_FORMAT(rv.create_time,'%Y-%m')
        order by DATE_FORMAT(rv.create_time,'%Y-%m') desc
            limit 10
    </select>


    <select id="statisticPrice" resultType="com.ruoyi.teen.domain.statistic.TeenStatisticPriceBean">
        select  DATE_FORMAT(t.create_time,'%m-%d') month,IFNULL(sum(package_price),0) price
        from rlct_user_package t
        where DATE_FORMAT(NOW(),'%Y-%m') = DATE_FORMAT(t.create_time,'%Y-%m')
        group by DATE_FORMAT(t.create_time,'%m-%d')
        order by DATE_FORMAT(t.create_time,'%m-%d') desc

    </select>

    <!--总服务人数 -->
    <select id="countTotalService" resultType="java.lang.Integer">
        select count(1) from rlct_visit t
    </select>
    <!--今日服务人数 -->
    <select id="countTodayService" resultType="java.lang.Integer">
        select count(1) from rlct_visit t where DATE_FORMAT(NOW(),'%Y-%m-%d') = DATE_FORMAT(t.create_time,'%Y-%m-%d')
    </select>

    <!--总建档人数 -->
    <select id="countTotalArchives" resultType="java.lang.Integer">
        select count(1) from sys_user t where user_type = '22'
    </select>
    <!--今日建档人数 -->
    <select id="countTodayArchives" resultType="java.lang.Integer">
        select count(1) from sys_user t where user_type = '22' and DATE_FORMAT(NOW(),'%Y-%m-%d') = DATE_FORMAT(t.create_time,'%Y-%m-%d')
    </select>
    <!--根据id查总金额-->
    <select id="sumPackagePrice" resultType="java.lang.Long">
        SELECT SUM(rup.package_buy_times * rup.package_price) AS totalPrice
        FROM sys_user t
        JOIN rlct_user_package rup ON rup.sales_id = t.user_id
        JOIN rlct_packages rp ON rup.package_id = rp.package_id
        WHERE t.status = '0'
        AND rup.package_return_status = '0'
        AND t.user_id = #{doctorId}
        <if test="startTime != null">
            AND rup.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND rup.create_time &lt;  #{endTime}
        </if>
    </select>
    <!--孩子数-->
    <select id="countTotalArchivesByDoctorId" resultType="java.lang.Integer">
        SELECT COUNT(*) AS newTeenCount
        FROM rlct_teen t
        WHERE t.create_by = #{doctorId}
        AND  t.del_flag ='0'
        <if test="startTime != null">
        AND t.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
        AND t.create_time &lt;  #{endTime}
        </if>
    </select>
    <select id="selectDailyServiceRecords" resultType="com.ruoyi.teen.vo.ipad.UserServiceRecordVo">
        SELECT
        DATE(create_time) AS time,
        COUNT(*) AS totalArchives,
        NULL AS totalPrice
        FROM rlct_teen
        WHERE create_by = #{doctorId}
        AND del_flag = '0'
        AND create_time >=  #{startTime}
        AND create_time &lt;  #{endTime}
        GROUP BY DATE(create_time)
        ORDER BY time
    </select>
    <select id="selectDailyServicePrice" resultType="com.ruoyi.teen.vo.ipad.UserServiceRecordVo">
        SELECT
        DATE(rup.create_time) AS time,
        NULL AS totalArchives,
        SUM(package_price) AS totalPrice
        FROM rlct_user_package rup
        JOIN sys_user ON rup.sales_id = sys_user.user_id
        WHERE  sys_user.status = '0'
        AND rup.package_return_status = '0'
        AND sales_id = #{doctorId}
        AND package_return_status = '0'
        AND rup.create_time >=  #{startTime}
        AND rup.create_time  &lt;  #{endTime}
        GROUP BY DATE(rup.create_time)
        ORDER BY time
    </select>

</mapper>
