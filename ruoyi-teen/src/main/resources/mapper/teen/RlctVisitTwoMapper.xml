<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctVisitTwoMapper">

    <resultMap type="RlctVisitTwo" id="RlctVisitTwoResult">
        <result property="visitId"    column="visit_id"    />
        <result property="visitDetails"    column="visit_details"    />
        <result property="parentTeenId"    column="parent_teen_id"    />
        <result property="userId"    column="user_id"    />
        <result property="deductParentId"    column="deduct_parent_id"    />
        <result property="userPackageId"    column="user_package_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="beforeTreatmentImageUrl"    column="before_treatment_image_url"    />
        <result property="afterTreatmentImageUrl"    column="after_treatment_image_url"    />
        <result property="status"    column="status"    />
        <result property="healTime"    column="heal_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="visitNo"    column="visit_no"    />
        <result property="admissionTime"    column="admission_time"    />
        <result property="heightBeforeTreatment"    column="height_before_treatment"    />
        <result property="heightAfterTreatment"    column="height_after_treatment"    />
        <result property="weightBeforeTreatment"    column="weight_before_treatment"    />
        <result property="weightAfterTreatment"    column="weight_after_treatment"    />
    </resultMap>

    <resultMap id="RlctVisitTwoMap" type="RlctVisitTwo">
        <!-- 就诊信息 -->
        <result property="heightAfterTreatment" column="height_after_treatment"/>
        <result property="weightAfterTreatment" column="weight_after_treatment"/>
        <result property="visitDetails" column="visit_details"/>
    </resultMap>
    <resultMap id="RlctHistoryVisitTwoMap" type="com.ruoyi.teen.vo.ipad.RlctVisitVo">
        <!-- 历史就诊信息 -->
        <result property="visitId" column="visit_id"/>
        <result property="heightAfterTreatment" column="height_after_treatment"/>
        <result property="heightBeforeTreatment" column="height_before_treatment"/>
        <result property="weightAfterTreatment" column="weight_after_treatment"/>
        <result property="weightBeforeTreatment" column="weight_before_treatment"/>
        <result property="beforeTreatmentImageUrl" column="before_treatment_image_url"/>
        <result property="afterTreatmentImageUrl" column="after_treatment_image_url"/>
        <result property="admissionTime" column="admission_time"/>
        <result property="visitDetails" column="visit_details"/>
    </resultMap>
    <!--历史就诊信息{治疗项目}-->
    <resultMap id="TreatmentMap" type="com.ruoyi.teen.domain.TreatmentDTO">
        <result property="dictTreatmentType" column="treatment_type"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="nickName" column="nick_name" />
    </resultMap>


    <select id="selectRlctVisitTwoLastTime" resultMap="RlctVisitTwoMap">
        SELECT
            v.visit_details,
            v.height_after_treatment,
            v.weight_after_treatment
        FROM
            rlct_visit v
                JOIN
            rlct_parent_teen pt ON v.parent_teen_id = pt.id
        WHERE
            pt.teen_id = #{teen_id}  -- 替换为实际的孩子ID
          AND v.status = 1       -- 只查询已完成治疗的记录
        ORDER BY
            v.heal_time DESC       -- 按治疗时间降序排序
            LIMIT 1;                  -- 只取最新的一条记录
    </select>
    <select id="selectVisitsByParentTeenId" resultMap="RlctHistoryVisitTwoMap">
        SELECT
            v.visit_id,
            v.visit_details,
            v.height_after_treatment,
            v.height_before_treatment,
            v.weight_after_treatment,
            v.weight_before_treatment,
            v.before_treatment_image_url,
            v.after_treatment_image_url,
            v.admission_time,
            u.phonenumber,
            p.package_name
        FROM
            rlct_visit v
                JOIN
            rlct_parent_teen pt ON v.parent_teen_id = pt.id
                JOIN
            sys_user u ON pt.parent_id = u.user_id
                JOIN
            rlct_packages p ON v.package_id = p.package_id
        WHERE
            pt.teen_id = #{teen_id}  -- 替换为实际的孩子ID
          AND v.status = 1       -- 只查询已完成治疗的记录
        ORDER BY
            v.heal_time DESC;
    </select>
    <select id="selectTreatmentsByVisitId" resultMap="TreatmentMap">
        SELECT
            treatment_type,
            doctor_id,
            nick_name
        FROM
            rlct_doctor_visic
        JOIN
            sys_user u ON rlct_doctor_visic.doctor_id = u.user_id
        WHERE
            rlct_doctor_visic.del_flag = '0'
            AND visit_id = #{visitId}
    </select>
<!--    <select id="selectRlctVisitTwoByStatus" resultType="java.lang.Long">
        SELECT
            v.visit_id
        FROM
            rlct_visit v
        WHERE
            v.parent_teen_id = #{parent_teen_id}  &#45;&#45; 替换为实际的孩子ID
          AND v.status = #{number}       &#45;&#45; 只查询治疗中或者暂存行为治疗的记录
        ORDER BY
            v.heal_time DESC
            LIMIT 1;                  &#45;&#45; 只取最新的一条记录
    </select>-->
    <select id="selectRlctThisTimeVisitTwoByVisitId" resultType="com.ruoyi.teen.vo.ipad.RlctThisTimeVisitVo">
        SELECT
            v.visit_id As visitId,
            v.visit_details,
            v.height_after_treatment,
            v.height_before_treatment,
            v.weight_after_treatment,
            v.weight_before_treatment,
            v.before_treatment_image_url,
            v.after_treatment_image_url,
            v.admission_time,
            u.phonenumber,
            p.package_name,
            pu.product_use_name
        FROM
            rlct_visit v
                LEFT JOIN
            rlct_parent_teen pt ON v.parent_teen_id = pt.id
                LEFT JOIN
            sys_user u ON pt.parent_id = u.user_id
                LEFT JOIN
            rlct_packages p ON v.package_id = p.package_id
                LEFT JOIN
            rlct_product_use pu ON v.visit_id = pu.visit_id
        WHERE
            v.visit_id = #{visitId}   -- 提供当前就诊记录ID
        ORDER BY
            v.heal_time DESC;
    </select>

    <select id="selectTreatmentsByVisitIdAndUserId" resultType="com.ruoyi.teen.vo.ipad.TreatmentVo">
        SELECT
            treatment_type AS dictTreatmentType,
            doctor_id,
            nick_name
        FROM
            rlct_doctor_visic rdv
        JOIN
            sys_user u ON rdv.doctor_id = u.user_id
        WHERE
            visit_id = #{visitId}
            AND user_id = #{userId}
            AND rdv.del_flag = '0'
    </select>

    <select id="selectRlctVisitByStatus" resultType="com.ruoyi.teen.domain.RlctVisit">
        SELECT
            v.visit_id,
            v.user_package_id
        FROM
            rlct_visit v
        WHERE
            v.parent_teen_id = #{parent_teen_id}  -- 替换为实际的孩子ID
          AND v.status = #{number}       -- 只查询治疗中或者暂存行为治疗的记录
        ORDER BY
            v.heal_time DESC
            LIMIT 1;                  -- 只取最新的一条记录
    </select>

    <select id="selectVisitIdByDoctorAndType" resultType="java.lang.Long">
        SELECT doctor_visic_id
        FROM rlct_doctor_visic
        WHERE del_flag = '0'
        <if test="visitId != null">
            AND visit_id = #{visitId}
        </if>
        <if test="doctorId != null">
            AND doctor_id = #{doctorId}
        </if>
        <if test="treatmentType != null and treatmentType != ''">
            AND treatment_type = #{treatmentType}
        </if>
        LIMIT 1
    </select>
    <select id="selectProductUseByVisitId" resultType="RlctProductUse">
        select product_use_name,  user_product_id
        from rlct_product_use
        where visit_id = #{visitId}
        ORDER BY
            create_time DESC       -- 按治疗时间降序排序
        LIMIT 1;                  -- 只取最新的一条记录
    </select>
    <select id="selectVisitIdByDoctorAndTypes" resultType="com.ruoyi.teen.domain.RlctDoctorVisic">
        SELECT doctor_visic_id  , treatment_time

        FROM rlct_doctor_visic
        WHERE del_flag = '0'
        <if test="visitId != null">
            AND visit_id = #{visitId}
        </if>
        <if test="doctorId != null">
            AND doctor_id = #{doctorId}
        </if>
        <if test="treatmentType != null and treatmentType != ''">
            AND treatment_type = #{treatmentType}
        </if>
        LIMIT 1
    </select>


</mapper>
