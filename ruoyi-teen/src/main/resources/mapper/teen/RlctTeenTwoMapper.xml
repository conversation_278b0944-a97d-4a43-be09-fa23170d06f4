<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctTeenTwoMapper">


    <resultMap type="RlctTeenTwo" id="RlctTeenTwoResult">
        <result property="teenId"    column="teen_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="teenName"    column="teen_name"    />
        <result property="teenAge"    column="teen_age"    />
        <result property="teenSex"    column="teen_sex"    />
        <result property="teenBirth"    column="teen_birth"    />
        <result property="initHeight"    column="init_height"    />
        <result property="initWeight"    column="init_weight"    />
        <result property="initTime"    column="init_time"    />
        <result property="teenNameCh"    column="teen_name_ch"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="fatherHeight"    column="father_height"    />
        <result property="motherHeight"    column="mother_height"    />
        <result property="medicalHistory"    column="medical_history"    />
        <result property="visitGoal"    column="visit_goal"    />
        <result property="teenAddress"    column="teen_adress"    />
        <result property="geneticHeight"    column="genetic_height"    />
        <result property="boneAge"    column="bone_age"    />
        <result property="allergyHistory"    column="allergy_history"    />
        <result property="phonenumber"    column="phonenumber"    />
    </resultMap>

    <resultMap id="recordListVoMap" type="com.ruoyi.teen.vo.ipad.RecordListVo">
        <result property="parentTeenId"    column="id"    />
        <result column="teen_name" property="teenName" />
        <result column="teen_birth" property="teenBirth" />
        <result column="height_after_treatment" property="heightAfterTreatment" />
        <result column="weight_after_treatment" property="weightAfterTreatment" />
    </resultMap>

    <resultMap id="RlctTeenTwoVoMap" type="com.ruoyi.teen.vo.ipad.RlctTeenTwoVo">
        <!-- 孩子基本信息 -->
        <result property="teenName" column="teen_name"/>
        <result property="teenSex" column="teen_sex"/>
        <result property="teenAge" column="teen_age"/>
        <result property="teenBirth" column="teen_birth"/>
        <result property="allergyHistory" column="allergy_history"/>
        <result property="fatherHeight" column="father_height"/>
        <result property="motherHeight" column="mother_height"/>
        <result property="geneticHeight" column="genetic_height"/>
        <!-- 家长信息 -->
        <result property="phonenumber" column="phonenumber"/>
    </resultMap>
    <resultMap type="RlctUserPackageVTwo" id="RlctUserPackageVTwoMap">
        <!-- 剩余套餐次数 -->
        <result property="remainingTimes" column="remainingTimes"/>
    </resultMap>
    <!--骨龄拍摄记录-->
    <resultMap id="BaseResultMap" type="RlctBoneAgeRecord">
        <result column="bone_age" property="boneAge" />
        <result column="predicted_height" property="predictedHeight" />
        <result column="capture_time" property="captureTime" />
    </resultMap>

    <sql id="selectRlctTeenTwoVo">
        select teen_id, parent_id, teen_name, teen_age, teen_sex, teen_birth, init_height, init_weight, init_time, teen_name_ch, del_flag, create_by, create_time, update_by, update_time, remark, father_height, mother_height, medical_history, visit_goal, teen_adress, genetic_height, bone_age, allergy_history from rlct_teen_two
    </sql>


<!--    /*患者信息*/-->
    <select id="selectRlctTeenTwoByTeenId" parameterType="Long" resultMap="RlctTeenTwoResult" >
        SELECT
            t.teen_id,
            t.teen_name,
            t.teen_sex,
            t.teen_age,
            t.teen_birth,
            t.init_height,
            t.init_weight,
            t.allergy_history,
            t.father_height,
            t.mother_height,
            t.genetic_height,
            u.phonenumber
        FROM
            rlct_parent_teen pt
                JOIN rlct_teen t ON pt.teen_id = t.teen_id
                JOIN sys_user u ON pt.parent_id = u.user_id
        WHERE
            pt.id = #{parent_teen_id}
          AND t.del_flag = '0'
    </select>


    <!--产品使用记录-->
    <select id="selectProductUseList" resultType="com.ruoyi.teen.domain.ProductUse">
        SELECT
            p.product_use_name,
            p.create_time
        FROM
            rlct_product_use p
        WHERE
            p.parent_teen_id = #{parent_teen_id}
        ORDER BY
            p.create_time DESC
    </select>
    <select id="selectRlctUserPackage" resultMap="RlctUserPackageVTwoMap">
        SELECT
            SUM(p.package_total_times) - SUM(p.package_number_times) AS remainingTimes
        FROM
            teen.rlct_user_package p
                JOIN
            teen.rlct_parent_teen pt ON p.user_id = pt.parent_id
        WHERE
            pt.id = #{parent_teen_id}
          AND p.package_return_status = 0
        GROUP BY
            p.user_id;
    </select>
    <select id="selectBoneAgeRecordByParentTeenId" resultType="com.ruoyi.teen.domain.RlctBoneAgeRecord">
        SELECT
            bone_age,
            predicted_height,
            teen_age,
            bone_age_report,
            capture_time
        FROM
            rlct_bone_age_record
        WHERE
            parent_teen_id = #{parentTeenId}
        ORDER BY
            capture_time DESC
    </select>
    <select id="selectRlctTeenTwoInitByTeenId" resultType="com.ruoyi.teen.domain.RlctTeenTwo">
        SELECT
            t.teen_id,
            init_height,
            init_weight,
            init_time
        FROM
            rlct_parent_teen pt
                JOIN rlct_teen t ON pt.teen_id = t.teen_id
        WHERE
            pt.id = #{parent_teen_id}
          AND t.del_flag = '0'
    </select>
    <!-- 查询符合条件的孩子ID -->
    <select id="selectDistinctTeenIds" resultType="com.ruoyi.teen.vo.ipad.RecordListVo">
        SELECT p.id AS parentTeenId,t.teen_name AS teenName,t.teen_birth AS teenBirth,u.phonenumber AS phoneNumber
        FROM rlct_teen t
        LEFT JOIN rlct_parent_teen p ON t.teen_id = p.teen_id
        LEFT JOIN sys_user u ON p.parent_id = u.user_id
        <where>
            t.del_flag = '0'
            AND u.del_flag = '0'
            AND u.status = '0'
            <if test="phonenumber != null">
                AND u.phonenumber LIKE CONCAT('%', #{phonenumber}, '%')
            </if>
            <if test="nameCh != null">
                AND t.teen_name_ch LIKE CONCAT('%', #{nameCh}, '%')
            </if>
            <if test="keyword != null">
                AND (u.phonenumber LIKE CONCAT('%', #{keyword}, '%')
                OR t.teen_name_ch LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
    </select>

    <!-- 查询就诊信息-->
    <select id="selectLatestVisitByTeenId" resultType="com.ruoyi.teen.vo.ipad.RecordListVo">
        SELECT
            v.parent_teen_id AS parentTeenId,
            v.height_after_treatment AS heightAfterTreatment,
            v.weight_after_treatment AS weightAfterTreatment
        FROM rlct_visit v
        WHERE v.parent_teen_id = #{parentTeenId}
        ORDER BY v.heal_time DESC
            LIMIT 1  -- 每个 parent_teen_id 只取最新的一条记录
    </select>


    <insert id="insertRlctTeenTwo" parameterType="rlctTeenDTO" useGeneratedKeys="true" keyProperty="teenId"  keyColumn="teen_id">
        insert into rlct_teen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="teenName != null">teen_name,</if>
            <if test="teenAge != null">teen_age,</if>
            <if test="teenSex != null">teen_sex,</if>
            <if test="teenBirth != null">teen_birth,</if>
            <if test="initHeight != null">init_height,</if>
            <if test="initWeight != null">init_weight,</if>
            <if test="initTime != null">init_time,</if>
            <if test="teenNameCh != null">teen_name_ch,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="fatherHeight != null">father_height,</if>
            <if test="motherHeight != null">mother_height,</if>
            <if test="medicalHistory != null">medical_history,</if>
            <if test="visitGoal != null">visit_goal,</if>
            <if test="teenAddress != null">teen_address,</if>
            <if test="geneticHeight != null">genetic_height,</if>
            <if test="boneAge != null">bone_age,</if>
            <if test="allergyHistory != null">allergy_history,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="teenName != null">#{teenName},</if>
            <if test="teenAge != null">#{teenAge},</if>
            <if test="teenSex != null">#{teenSex},</if>
            <if test="teenBirth != null">#{teenBirth},</if>
            <if test="initHeight != null">#{initHeight},</if>
            <if test="initWeight != null">#{initWeight},</if>
            <if test="initTime != null">#{initTime},</if>
            <if test="teenNameCh != null">#{teenNameCh},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fatherHeight != null">#{fatherHeight},</if>
            <if test="motherHeight != null">#{motherHeight},</if>
            <if test="medicalHistory != null">#{medicalHistory},</if>
            <if test="visitGoal != null">#{visitGoal},</if>
            <if test="teenAddress != null">#{teenAddress},</if>
            <if test="geneticHeight != null">#{geneticHeight},</if>
            <if test="boneAge != null">#{boneAge},</if>
            <if test="allergyHistory != null">#{allergyHistory},</if>
         </trim>
    </insert>
    <insert id="insertRlctParentTeen" useGeneratedKeys="true" keyProperty="id" keyColumn="id" >
        insert into rlct_parent_teen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="teenId != null">teen_id,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="teenId != null">#{teenId},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>


</mapper>
