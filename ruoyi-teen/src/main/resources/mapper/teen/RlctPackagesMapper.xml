<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctPackagesMapper">

    <resultMap type="RlctPackages" id="RlctPackagesResult">
        <result property="packageName"    column="package_name"    />
        <result property="packageId"    column="package_id"    />
        <result property="packagePrice"    column="package_price"    />
        <result property="packageMemberPrice"    column="package_member_price"    />
        <result property="packageDescription"    column="package_description"    />
        <result property="packageImageUrl"    column="package_image_url"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="packageNumber" column="package_number" />
    </resultMap>

    <resultMap type="com.ruoyi.teen.vo.ipad.RlctPackagesVo" id="RlctPackagesdetailResult">
        <result property="packageId" column="package_id" />
        <result property="packageName"    column="package_name"    />
        <result property="packagePrice"    column="package_price"    />
        <result property="packageMemberPrice"    column="package_member_price"    />
        <result property="packageNumber" column="package_number" />
    </resultMap>

    <!--已购套餐列表-->
    <resultMap id="RlctPackagesBuyTimesListResult" type="com.ruoyi.teen.vo.ipad.PackageBuyTimesVo">
        <result property="packageName" column="package_name" />
        <result property="packageTotalTimes" column="package_total_times" />
        <result property="packageNumberTimes" column="package_number_times" />
    </resultMap>

    <sql id="selectRlctPackagesVo">
        select package_name, package_id, package_price, package_member_price, package_description, package_image_url, del_flag, create_by, create_time, update_by, update_time, remark,package_number from rlct_packages
    </sql>

    <select id="selectRlctPackagesList" parameterType="RlctPackages" resultMap="RlctPackagesResult">
        <include refid="selectRlctPackagesVo"/>
        <where>
            <if test="packageName != null  and packageName != ''"> and package_name like concat('%', #{packageName}, '%')</if>
            <if test="packagePrice != null "> and package_price = #{packagePrice}</if>
            <if test="packageDescription != null  and packageDescription != ''"> and package_description like concat('%', #{packageDescription}, '%')</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="selectRlctPackagesByPackageId" parameterType="Long" resultMap="RlctPackagesResult">
        <include refid="selectRlctPackagesVo"/>
        where package_id = #{packageId}
    </select>
    <select id="selectPackageList" resultMap="RlctPackagesdetailResult">
        select     package_id,package_name,package_price,package_member_price,package_number from rlct_packages where del_flag = '0'
    </select>
    <select id="selectPackageBuyTimes" resultMap="RlctPackagesBuyTimesListResult">
        SELECT
        p.package_name,
        up.package_number_times,
        up.package_total_times
        FROM
        rlct_parent_teen pt
        LEFT JOIN rlct_user_package up ON pt.parent_id = up.user_id
        AND up.package_return_status = 0  <!-- 添加状态过滤 -->
        LEFT JOIN rlct_packages p ON up.package_id = p.package_id
        WHERE
        pt.id = #{parentTeenId}
        ORDER BY up.create_time DESC;  <!-- 按需排序 -->
    </select>
    <select id="selectPackageBuyListTimes" resultMap="RlctPackagesBuyTimesListResult">
        SELECT
        p.package_name,
        up.package_number_times,
        up.package_total_times
        FROM
        rlct_parent_teen pt
        LEFT JOIN rlct_user_package up ON pt.parent_id = up.user_id
        AND up.package_return_status = 0  <!-- 添加状态过滤 -->
        LEFT JOIN rlct_packages p ON up.package_id = p.package_id
        WHERE
        pt.id = #{parentTeenId}
        ORDER BY up.create_time DESC;  <!-- 按需排序 -->
    </select>
    <select id="selectRlctParentTeenByParentTeenId" resultType="java.lang.Long">
        select parent_id  from rlct_parent_teen where id = #{parentTeenId}
    </select>

    <insert id="insertRlctPackages" parameterType="RlctPackages" useGeneratedKeys="true" keyProperty="packageId">
        insert into rlct_packages
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageName != null">package_name,</if>
            <if test="packagePrice != null">package_price,</if>
            <if test="packageMemberPrice != null">package_member_price,</if>
            <if test="packageDescription != null">package_description,</if>
            <if test="packageImageUrl != null">package_image_url,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="packageNumber != null">package_number,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageName != null">#{packageName},</if>
            <if test="packagePrice != null">#{packagePrice},</if>
            <if test="packageMemberPrice != null">#{packageMemberPrice},</if>
            <if test="packageDescription != null">#{packageDescription},</if>
            <if test="packageImageUrl != null">#{packageImageUrl},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="packageNumber != null">#{packageNumber},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRlctPackages" parameterType="RlctPackages">
        update rlct_packages
        <trim prefix="SET" suffixOverrides=",">
            <if test="packageName != null">package_name = #{packageName},</if>
            <if test="packagePrice != null">package_price = #{packagePrice},</if>
            <if test="packageMemberPrice != null">package_member_price = #{packageMemberPrice},</if>
            <if test="packageDescription != null">package_description = #{packageDescription},</if>
            <if test="packageImageUrl != null">package_image_url = #{packageImageUrl},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="packageNumber != null">package_number = #{packageNumber},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where package_id = #{packageId}
    </update>

    <delete id="deleteRlctPackagesByPackageId" parameterType="Long">
        delete from rlct_packages where package_id = #{packageId}
    </delete>

    <delete id="deleteRlctPackagesByPackageIds" parameterType="String">
        delete from rlct_packages where package_id in
        <foreach item="packageId" collection="array" open="(" separator="," close=")">
            #{packageId}
        </foreach>
    </delete>
</mapper>
