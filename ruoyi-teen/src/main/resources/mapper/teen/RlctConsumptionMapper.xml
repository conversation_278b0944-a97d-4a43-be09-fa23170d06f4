<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctConsumptionMapper">
    
    <resultMap type="RlctConsumption" id="RlctConsumptionResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="teenId"    column="teen_id"    />
        <result property="userPackageId"    column="user_package_id"    />
        <result property="dockerId"    column="docker_id"    />
        <result property="clinicId"    column="clinic_id"    />
        <result property="consumeTime"    column="consume_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap id="RlctConsumptionResultVo" type="com.ruoyi.teen.vo.pc.RlctConsumptionVo">
        <result property="userName" column="user_name"/>
        <result property="teenName" column="teen_name"/>
        <result property="packageName" column="package_name"/>
        <result property="doctorName" column="docrot_name"/>
        <result property="clinicName" column="clinic_name"/>
        <result property="consumeTime" column="consume_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectRlctConsumptionVo">
        select id, user_id, teen_id, user_package_id, docker_id, clinic_id, consume_time, create_by, create_time, update_by, update_time, remark from rlct_consumption
    </sql>

    <sql id="selectRlctConsumptionNameVo">
        select su.nick_name user_name,rtn.teen_name,rpgs.package_name,sd.nick_name docrot_name,rcs.clinic_name,rcst.consume_time,rcst.remark
        from rlct_consumption rcst
        LEFT JOIN sys_user su ON rcst.user_id = su.user_id
        LEFT JOIN rlct_teen rtn ON rcst.teen_id = rtn.teen_id
        LEFT JOIN rlct_user_package rupg ON rcst.user_package_id = rupg.id
        LEFT JOIN rlct_packages rpgs ON rupg.package_id = rpgs.package_id
        LEFT JOIN sys_user sd ON rcst.docker_id = sd.user_id
        LEFT JOIN rlct_clinics rcs ON rcst.clinic_id = rcs.clinic_id
    </sql>

    <select id="selectRlctConsumptionList" parameterType="RlctConsumption" resultMap="RlctConsumptionResult">
        <include refid="selectRlctConsumptionVo"/>
        <where>  
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectRlctConsumptionById" parameterType="Long" resultMap="RlctConsumptionResult">
        <include refid="selectRlctConsumptionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRlctConsumption" parameterType="RlctConsumption" useGeneratedKeys="true" keyProperty="id">
        insert into rlct_consumption
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="teenId != null">teen_id,</if>
            <if test="userPackageId != null">user_package_id,</if>
            <if test="dockerId != null">docker_id,</if>
            <if test="clinicId != null">clinic_id,</if>
            <if test="consumeTime != null">consume_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="teenId != null">#{teenId},</if>
            <if test="userPackageId != null">#{userPackageId},</if>
            <if test="dockerId != null">#{dockerId},</if>
            <if test="clinicId != null">#{clinicId},</if>
            <if test="consumeTime != null">#{consumeTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRlctConsumption" parameterType="RlctConsumption">
        update rlct_consumption
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="teenId != null">teen_id = #{teenId},</if>
            <if test="userPackageId != null">user_package_id = #{userPackageId},</if>
            <if test="dockerId != null">docker_id = #{dockerId},</if>
            <if test="clinicId != null">clinic_id = #{clinicId},</if>
            <if test="consumeTime != null">consume_time = #{consumeTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRlctConsumptionById" parameterType="Long">
        delete from rlct_consumption where id = #{id}
    </delete>

    <delete id="deleteRlctConsumptionByIds" parameterType="String">
        delete from rlct_consumption where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectRlctConsumptionVoList" resultMap="RlctConsumptionResultVo" parameterType="com.ruoyi.teen.vo.pc.RlctConsumptionVo">
        <include refid="selectRlctConsumptionNameVo"/>
        <where>
            <if test="remark != null  and remark != ''"> and rcst.remark LIKE CONCAT('%', #{remark}, '%')</if>
            <if test="userName != null  and userName != ''"> and su.nick_name LIKE CONCAT('%', #{userName}, '%')</if>
            <if test="teenName != null  and teenName != ''"> and rtn.teen_name LIKE CONCAT('%', #{teenName}, '%')</if>
            <if test="packageName != null  and packageName != ''"> and rpgs.package_name LIKE CONCAT('%', #{packageName}, '%')</if>
            <if test="doctorName != null  and doctorName != ''"> and sd.nick_name LIKE CONCAT('%', #{doctorName}, '%')</if>
            <if test="clinicName != null  and clinicName != ''"> and rcs.clinic_name LIKE CONCAT('%', #{clinicName}, '%')</if>
            <if test="consumeTime != null"> and DATE(rcst.consume_time) = DATE(#{consumeTime, jdbcType=TIMESTAMP, javaType=java.util.Date})</if>
        </where>
    </select>
</mapper>