<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctTeenMapper">

    <resultMap type="com.ruoyi.teen.domain.RlctTeen" id="RlctTeenResult">
        <result property="teenId"    column="teen_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="teenName"    column="teen_name"    />
        <result property="teenAge"    column="teen_age"    />
        <result property="teenAvatar"    column="teen_avatar"    />
        <result property="teenSex"    column="teen_sex"    />
        <result property="teenBirth"    column="teen_birth"    />
        <result property="teenSleepTime"    column="teen_sleep_time"    />
        <result property="teenSleepDurationTime"    column="teen_sleep_duration_time"    />
        <result property="teenExerciseHabit"    column="teen_exercise_habit"    />
        <result property="teenEatHabit"    column="teen_eat_habit"    />
        <result property="teenNutrient"    column="teen_nutrient"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="initHeight"    column="init_height"    />
        <result property="initWeight"    column="init_weight"    />
        <result property="initTime"    column="init_time"    />
        <result property="teenNameCh"    column="teen_name_ch"    />
    </resultMap>

    <resultMap type="com.ruoyi.teen.vo.physician.RlctTeenVo" id="RlctTeenVoResult">
        <result property="teenId"    column="teen_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="phoneNumber"    column="phonenumber"    />
        <result property="teenName"    column="teen_name"    />
        <result property="teenAge"    column="teen_age"    />
        <result property="teenAvatar"    column="teen_avatar"    />
        <result property="teenSex"    column="teen_sex"    />
        <result property="teenBirth"    column="teen_birth"    />
        <result property="teenSleepTime"    column="teen_sleep_time"    />
        <result property="teenSleepDurationTime"    column="teen_sleep_duration_time"    />
        <result property="teenExerciseHabit"    column="teen_exercise_habit"    />
        <result property="teenEatHabit"    column="teen_eat_habit"    />
        <result property="teenNutrient"    column="teen_nutrient"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="initHeight"    column="init_height"    />
        <result property="initWeight"    column="init_weight"    />
        <result property="initTime"    column="init_time"    />
        <result property="teenNameCh"    column="teen_name_ch"    />
    </resultMap>

    <resultMap type="com.ruoyi.teen.vo.ipad.TeenVo" id="TeenVoResult">
        <result property="id"    column="id"    />
        <result property="teenId"    column="teen_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="phoneNumber"    column="phonenumber"    />
        <result property="teenName"    column="teen_name"    />
        <result property="teenAge"    column="teen_age"    />
        <result property="teenAvatar"    column="teen_avatar"    />
        <result property="teenSex"    column="teen_sex"    />
        <result property="teenBirth"    column="teen_birth"    />
        <result property="teenSleepTime"    column="teen_sleep_time"    />
        <result property="teenSleepDurationTime"    column="teen_sleep_duration_time"    />
        <result property="teenExerciseHabit"    column="teen_exercise_habit"    />
        <result property="teenEatHabit"    column="teen_eat_habit"    />
        <result property="teenNutrient"    column="teen_nutrient"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="teenNameCh"    column="teen_name_ch"    />
    </resultMap>

    <sql id="selectRlctTeenVo">
        select teen_id, parent_id, teen_name, teen_age, teen_avatar, teen_sex, teen_birth, teen_sleep_time
             , teen_sleep_duration_time, teen_exercise_habit, teen_eat_habit, teen_nutrient
             , del_flag, create_by, create_time, update_by, update_time, remark,init_height,init_weight,init_time,teen_name_ch from rlct_teen
    </sql>

    <select id="selectRlctTeenList" parameterType="RlctTeen" resultMap="RlctTeenResult">
        <include refid="selectRlctTeenVo"/>
        <where>
            <if test="teenName != null  and teenName != ''"> and teen_name like concat('%', #{teenName}, '%')</if>
            <if test="teenAge != null "> and teen_age = #{teenAge}</if>
            <if test="teenSex != null "> and teen_sex = #{teenSex}</if>
            <if test="teenBirth != null "> and teen_birth = #{teenBirth}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="selectRlctTeenByTeenId" parameterType="Long" resultMap="RlctTeenResult">
        <include refid="selectRlctTeenVo"/>
        where teen_id = #{teenId} and del_flag = 0
    </select>

    <select id="selectTeenByUnTeenNameCh" resultMap="RlctTeenResult">
        <include refid="selectRlctTeenVo"/>
        where teen_name is not null and teen_name_ch is null
    </select>

    <insert id="insertRlctTeen" parameterType="RlctTeen" useGeneratedKeys="true" keyProperty="teenId">
        insert into rlct_teen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="teenName != null">teen_name,</if>
            <if test="teenAge != null">teen_age,</if>
            <if test="teenAvatar != null">teen_avatar,</if>
            <if test="teenSex != null">teen_sex,</if>
            <if test="teenBirth != null">teen_birth,</if>
            <if test="teenSleepTime != null">teen_sleep_time,</if>
            <if test="teenSleepDurationTime != null">teen_sleep_duration_time,</if>
            <if test="teenExerciseHabit != null">teen_exercise_habit,</if>
            <if test="teenEatHabit != null">teen_eat_habit,</if>
            <if test="teenNutrient != null">teen_nutrient,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="teenNameCh != null">teen_name_ch,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="teenName != null">#{teenName},</if>
            <if test="teenAge != null">#{teenAge},</if>
            <if test="teenAvatar != null">#{teenAvatar},</if>
            <if test="teenSex != null">#{teenSex},</if>
            <if test="teenBirth != null">#{teenBirth},</if>
            <if test="teenSleepTime != null">#{teenSleepTime},</if>
            <if test="teenSleepDurationTime != null">#{teenSleepDurationTime},</if>
            <if test="teenExerciseHabit != null">#{teenExerciseHabit},</if>
            <if test="teenEatHabit != null">#{teenEatHabit},</if>
            <if test="teenNutrient != null">#{teenNutrient},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="teenNameCh != null">#{teenNameCh},</if>
         </trim>
    </insert>

    <update id="updateRlctTeen" parameterType="com.ruoyi.teen.domain.RlctTeen">
        update rlct_teen
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="teenName != null">teen_name = #{teenName},</if>
            <if test="teenAge != null">teen_age = #{teenAge},</if>
            <if test="teenAvatar != null">teen_avatar = #{teenAvatar},</if>
            <if test="teenSex != null">teen_sex = #{teenSex},</if>
            <if test="teenBirth != null">teen_birth = #{teenBirth},</if>
            <if test="teenSleepTime != null">teen_sleep_time = #{teenSleepTime},</if>
            <if test="teenSleepDurationTime != null">teen_sleep_duration_time = #{teenSleepDurationTime},</if>
            <if test="teenExerciseHabit != null">teen_exercise_habit = #{teenExerciseHabit},</if>
            <if test="teenEatHabit != null">teen_eat_habit = #{teenEatHabit},</if>
            <if test="teenNutrient != null">teen_nutrient = #{teenNutrient},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="initHeight != null">init_height = #{initHeight},</if>
            <if test="initWeight != null">init_weight = #{initWeight},</if>
            <if test="initTime != null">init_time = #{initTime},</if>
            <if test="teenNameCh != null">teen_name_ch = #{teenNameCh},</if>
        </trim>
        where teen_id = #{teenId}
    </update>

    <delete id="deleteRlctTeenByTeenId" parameterType="Long">
        delete from rlct_teen where teen_id = #{teenId}
    </delete>

    <delete id="deleteRlctTeenByTeenIds" parameterType="String">
        delete from rlct_teen where teen_id in
        <foreach item="teenId" collection="array" open="(" separator="," close=")">
            #{teenId}
        </foreach>
    </delete>

    <select id="getListByTel" resultMap="RlctTeenResult">
        select rt.*
        from sys_user su
                 join rlct_parent_teen rpt on su.user_id = rpt.parent_id
                 join rlct_teen rt on rpt.teen_id = rt.teen_id
        where su.phonenumber = #{tel}
    </select>

    <select id="getTeenListByTel" resultMap="TeenVoResult">
        select rpt.id,rt.*
        from sys_user su
                 join rlct_parent_teen rpt on su.user_id = rpt.parent_id
                 join rlct_teen rt on rpt.teen_id = rt.teen_id
        where su.phonenumber = #{tel}
    </select>

    <select id="patientList" resultType="com.ruoyi.teen.vo.ipad.PadPatientVo">
        select t.user_id patientId,t.nick_name nickName,t.phonenumber phonenumber from sys_user t
        where t.user_type = '22'
          and t.del_flag = 0
          and t.status = 0
          and t.phonenumber like concat(#{tel}, '%')
          order by t.phonenumber asc
          limit 10
    </select>

    <select id="patientTeenList" resultType="com.ruoyi.teen.vo.ipad.PadPatientTeenVo">
        select su.user_id patientId,su.nick_name nickName,su.phonenumber phonenumber
             ,rt.teen_id teenId,rt.teen_name teenName,rt.teen_name_ch teenNameCh
             ,rt.teen_age teenAge,rt.teen_sex teenSex,rt.teen_birth teenBirth
             ,rpt.id parentTeenId
        from rlct_teen rt
                 inner join rlct_parent_teen rpt on rpt.teen_id = rt.teen_id
                 inner join sys_user su on su.user_id = rpt.parent_id
        where su.user_type = '22'
          and su.del_flag = 0
          and su.status = 0
          and rt.del_flag = 0
          and rt.teen_name_ch like concat(#{teenNameCh}, '%')
         order by rt.teen_name_ch asc
          limit 10
    </select>

    <select id="patientByTel" resultType="com.ruoyi.teen.vo.ipad.PadPatientDetailVo">
        select t.user_id patientId,t.nick_name nickName,t.phonenumber phonenumber
             ,t.create_time createTime,t.sex
        from sys_user t
        where t.user_type = '22'
          and t.del_flag = 0
          and t.status = 0
          and t.phonenumber = #{tel}
    </select>

    <select id="selectTeen" resultType="com.ruoyi.teen.vo.physician.RlctTeenVo" resultMap="RlctTeenVoResult">
        select rt.*,su.phonenumber
        from rlct_teen rt
                 left join sys_user su on su.user_id = rt.parent_id
        where rt.teen_id = #{teenId}
    </select>
    <select id="getTeenList" resultMap="TeenVoResult">
        select rpt.id,rt.*
        from sys_user su
                 join rlct_parent_teen rpt on su.user_id = rpt.parent_id
                 join rlct_teen rt on rpt.teen_id = rt.teen_id
        where su.user_id = #{userId}
    </select>
</mapper>
