<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctTreatmentsMapper">
    
    <resultMap type="RlctTreatments" id="RlctTreatmentsResult">
        <result property="treatmentId"    column="treatment_id"    />
        <result property="treatmentName"    column="treatment_name"    />
        <result property="treatmentDescription"    column="treatment_description"    />
        <result property="treatmentPrice"    column="treatment_price"    />
        <result property="treatmentImageUrl"    column="treatment_image_url"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRlctTreatmentsVo">
        select treatment_id, treatment_name, treatment_description, treatment_price, treatment_image_url, del_flag, create_by, create_time, update_by, update_time, remark from rlct_treatments
    </sql>

    <select id="selectRlctTreatmentsList" parameterType="RlctTreatments" resultMap="RlctTreatmentsResult">
        <include refid="selectRlctTreatmentsVo"/>
        <where>  
            <if test="treatmentName != null  and treatmentName != ''"> and treatment_name like concat('%', #{treatmentName}, '%')</if>
            <if test="treatmentDescription != null  and treatmentDescription != ''"> and treatment_description like concat('%', #{treatmentDescription}, '%')</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>
    
    <select id="selectRlctTreatmentsByTreatmentId" parameterType="Long" resultMap="RlctTreatmentsResult">
        <include refid="selectRlctTreatmentsVo"/>
        where treatment_id = #{treatmentId}
    </select>
        
    <insert id="insertRlctTreatments" parameterType="RlctTreatments" useGeneratedKeys="true" keyProperty="treatmentId">
        insert into rlct_treatments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="treatmentName != null">treatment_name,</if>
            <if test="treatmentDescription != null">treatment_description,</if>
            <if test="treatmentPrice != null">treatment_price,</if>
            <if test="treatmentImageUrl != null">treatment_image_url,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="treatmentName != null">#{treatmentName},</if>
            <if test="treatmentDescription != null">#{treatmentDescription},</if>
            <if test="treatmentPrice != null">#{treatmentPrice},</if>
            <if test="treatmentImageUrl != null">#{treatmentImageUrl},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRlctTreatments" parameterType="RlctTreatments">
        update rlct_treatments
        <trim prefix="SET" suffixOverrides=",">
            <if test="treatmentName != null">treatment_name = #{treatmentName},</if>
            <if test="treatmentDescription != null">treatment_description = #{treatmentDescription},</if>
            <if test="treatmentPrice != null">treatment_price = #{treatmentPrice},</if>
            <if test="treatmentImageUrl != null">treatment_image_url = #{treatmentImageUrl},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where treatment_id = #{treatmentId}
    </update>

    <delete id="deleteRlctTreatmentsByTreatmentId" parameterType="Long">
        delete from rlct_treatments where treatment_id = #{treatmentId}
    </delete>

    <delete id="deleteRlctTreatmentsByTreatmentIds" parameterType="String">
        delete from rlct_treatments where treatment_id in 
        <foreach item="treatmentId" collection="array" open="(" separator="," close=")">
            #{treatmentId}
        </foreach>
    </delete>
</mapper>