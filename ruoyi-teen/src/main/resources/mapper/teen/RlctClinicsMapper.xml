<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctClinicsMapper">
    
    <resultMap type="RlctClinics" id="RlctClinicsResult">
        <result property="clinicId"    column="clinic_id"    />
        <result property="clinicName"    column="clinic_name"    />
        <result property="clinicAddress"    column="clinic_address"    />
        <result property="clinicPhoneNumber"    column="clinic_phone_number"    />
        <result property="clinicImageUrl"    column="clinic_image_url"    />
        <result property="clinicDescription"    column="clinic_description"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRlctClinicsVo">
        select clinic_id, clinic_name, clinic_address, clinic_phone_number, clinic_image_url, clinic_description, del_flag, create_by, create_time, update_by, update_time, remark from rlct_clinics
    </sql>

    <select id="selectRlctClinicsList" parameterType="RlctClinics" resultMap="RlctClinicsResult">
        <include refid="selectRlctClinicsVo"/>
        <where>  
            <if test="clinicName != null  and clinicName != ''"> and clinic_name like concat('%', #{clinicName}, '%')</if>
            <if test="clinicAddress != null  and clinicAddress != ''"> and clinic_address = #{clinicAddress}</if>
            <if test="clinicPhoneNumber != null  and clinicPhoneNumber != ''"> and clinic_phone_number = #{clinicPhoneNumber}</if>
            <if test="clinicDescription != null  and clinicDescription != ''"> and clinic_description like concat('%', #{clinicDescription}, '%')</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>
    
    <select id="selectRlctClinicsByClinicId" parameterType="Long" resultMap="RlctClinicsResult">
        <include refid="selectRlctClinicsVo"/>
        where clinic_id = #{clinicId}
    </select>
        
    <insert id="insertRlctClinics" parameterType="RlctClinics" useGeneratedKeys="true" keyProperty="clinicId">
        insert into rlct_clinics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clinicName != null">clinic_name,</if>
            <if test="clinicAddress != null">clinic_address,</if>
            <if test="clinicPhoneNumber != null">clinic_phone_number,</if>
            <if test="clinicImageUrl != null">clinic_image_url,</if>
            <if test="clinicDescription != null">clinic_description,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clinicName != null">#{clinicName},</if>
            <if test="clinicAddress != null">#{clinicAddress},</if>
            <if test="clinicPhoneNumber != null">#{clinicPhoneNumber},</if>
            <if test="clinicImageUrl != null">#{clinicImageUrl},</if>
            <if test="clinicDescription != null">#{clinicDescription},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRlctClinics" parameterType="RlctClinics">
        update rlct_clinics
        <trim prefix="SET" suffixOverrides=",">
            <if test="clinicName != null">clinic_name = #{clinicName},</if>
            <if test="clinicAddress != null">clinic_address = #{clinicAddress},</if>
            <if test="clinicPhoneNumber != null">clinic_phone_number = #{clinicPhoneNumber},</if>
            <if test="clinicImageUrl != null">clinic_image_url = #{clinicImageUrl},</if>
            <if test="clinicDescription != null">clinic_description = #{clinicDescription},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where clinic_id = #{clinicId}
    </update>

    <delete id="deleteRlctClinicsByClinicId" parameterType="Long">
        delete from rlct_clinics where clinic_id = #{clinicId}
    </delete>

    <delete id="deleteRlctClinicsByClinicIds" parameterType="String">
        delete from rlct_clinics where clinic_id in 
        <foreach item="clinicId" collection="array" open="(" separator="," close=")">
            #{clinicId}
        </foreach>
    </delete>
</mapper>