<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctProductMapper">
    
    <resultMap type="RlctProduct" id="RlctProductResult">
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productImageUrl"    column="product_image_url"    />
        <result property="productDetails"    column="product_details"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRlctProductVo">
        select product_id, product_name, product_image_url, product_details, del_flag, create_by, create_time, update_by, update_time, remark from rlct_product
    </sql>

    <select id="selectRlctProductList" parameterType="RlctProduct" resultMap="RlctProductResult">
        <include refid="selectRlctProductVo"/>
        <where>  
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="productDetails != null  and productDetails != ''"> and product_details = #{productDetails}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>
    
    <select id="selectRlctProductByProductId" parameterType="Long" resultMap="RlctProductResult">
        <include refid="selectRlctProductVo"/>
        where product_id = #{productId}
    </select>
        
    <insert id="insertRlctProduct" parameterType="RlctProduct">
        insert into rlct_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="productImageUrl != null">product_image_url,</if>
            <if test="productDetails != null">product_details,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productImageUrl != null">#{productImageUrl},</if>
            <if test="productDetails != null">#{productDetails},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRlctProduct" parameterType="RlctProduct">
        update rlct_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productImageUrl != null">product_image_url = #{productImageUrl},</if>
            <if test="productDetails != null">product_details = #{productDetails},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where product_id = #{productId}
    </update>

    <delete id="deleteRlctProductByProductId" parameterType="Long">
        delete from rlct_product where product_id = #{productId}
    </delete>

    <delete id="deleteRlctProductByProductIds" parameterType="String">
        delete from rlct_product where product_id in 
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>
</mapper>