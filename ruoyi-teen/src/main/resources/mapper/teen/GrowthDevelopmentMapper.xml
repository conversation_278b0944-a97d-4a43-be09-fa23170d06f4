<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.GrowthDevelopmentMapper">

    <resultMap type="GrowthDevelopment" id="GrowthDevelopmentResult">
        <result property="growth"    column="growth"    />
        <result property="heightTwoYear"    column="height_two_year"    />
        <result property="heightOneYear"    column="height_one_year"    />
        <result property="expectedHeightSixMonth"    column="expected_height_six_month"    />
        <result property="bodyHair"    column="body_hair"    />
        <result property="adamApple"    column="adam_apple"    />
        <result property="voiceChange"    column="voice_change"    />
        <result property="menstruation"    column="menstruation"    />
        <result property="breastDevelopment"    column="breast_development"    />
        <result property="underarmHair"    column="underarm_hair"    />
        <result property="parentTeenId"    column="parent_teen_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>


    <insert id="insertGrowthDevelopment" parameterType="GrowthDevelopment" useGeneratedKeys="true" keyProperty="growth">
        insert into rlct_growth_development
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="heightTwoYear != null">height_two_year,</if>
            <if test="heightOneYear != null">height_one_year,</if>
            <if test="expectedHeightSixMonth != null">expected_height_six_month,</if>
            <if test="bodyHair != null">body_hair,</if>
            <if test="adamApple != null">adam_apple,</if>
            <if test="voiceChange != null">voice_change,</if>
            <if test="menstruation != null">menstruation,</if>
            <if test="breastDevelopment != null">breast_development,</if>
            <if test="underarmHair != null">underarm_hair,</if>
            <if test="parentTeenId != null">parent_teen_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="heightTwoYear != null">#{heightTwoYear},</if>
            <if test="heightOneYear != null">#{heightOneYear},</if>
            <if test="expectedHeightSixMonth != null">#{expectedHeightSixMonth},</if>
            <if test="bodyHair != null">#{bodyHair},</if>
            <if test="adamApple != null">#{adamApple},</if>
            <if test="voiceChange != null">#{voiceChange},</if>
            <if test="menstruation != null">#{menstruation},</if>
            <if test="breastDevelopment != null">#{breastDevelopment},</if>
            <if test="underarmHair != null">#{underarmHair},</if>
            <if test="parentTeenId != null">#{parentTeenId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

</mapper>
