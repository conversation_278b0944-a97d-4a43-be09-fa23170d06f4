<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.teen.mapper.RlctUserPackageMapper">

    <resultMap type="com.ruoyi.teen.domain.RlctUserPackage" id="RlctUserPackageResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="packageTotalTimes"    column="package_total_times"    />
        <result property="packageBuyTimes"    column="package_buy_times"    />
        <result property="packageGiftTimes"    column="package_gift_times"    />
        <result property="packageNumberTimes"    column="package_number_times"    />
        <result property="packageThenPrice"    column="package_then_price"    />
        <result property="packagePrice"    column="package_price"    />
        <result property="packageReturnPrice"    column="package_return_price"    />
        <result property="returnTime"    column="return_time"    />
        <result property="packageReturnStatus"    column="package_return_status"    />
        <result property="packageAuthorizationer"    column="package_authorizationer"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="packageDuringConsumption"    column="package_during_consumption"    />
    </resultMap>

    <resultMap type="com.ruoyi.teen.vo.patient.RlctUserPackageVo" id="RlctUserPackageVoMap">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="packageTotalTimes"    column="package_total_times"    />
        <result property="packageBuyTimes"    column="package_buy_times"    />
        <result property="packageGiftTimes"    column="package_gift_times"    />
        <result property="packageNumberTimes"    column="package_number_times"    />
        <result property="packageThenPrice"    column="package_then_price"    />
        <result property="packagePrice"    column="package_price"    />
        <result property="packageReturnPrice"    column="package_return_price"    />
        <result property="returnTime"    column="return_time"    />
        <result property="packageReturnStatus"    column="package_return_status"    />
        <result property="packageAuthorizationer"    column="package_authorizationer"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="packageImageUrl"    column="package_image_url"    />
        <result property="packageName"    column="package_name"    />
        <result property="packageDuringConsumption"    column="package_during_consumption"    />
    </resultMap>


    <resultMap type="com.ruoyi.teen.vo.pc.RlctReturnRecordVo" id="RlctReturnRecordVoResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="packageTotalTimes"    column="package_total_times"    />
        <result property="packageBuyTimes"    column="package_buy_times"    />
        <result property="packageGiftTimes"    column="package_gift_times"    />
        <result property="packageNumberTimes"    column="package_number_times"    />
        <result property="packageThenPrice"    column="package_then_price"    />
        <result property="packagePrice"    column="package_price"    />
        <result property="packageReturnPrice"    column="package_return_price"    />
        <result property="returnTime"    column="return_time"    />
        <result property="packageReturnStatus"    column="package_return_status"    />
        <result property="packageAuthorizationer"    column="package_authorizationer"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="nickName" column="nick_name"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="packageName" column="package_name"/>
        <result property="packageDuringConsumption"    column="package_during_consumption"    />
    </resultMap>

    <resultMap type="com.ruoyi.teen.vo.pc.RlctUserPackagePCVo" id="RlctUserPackagePCVoMap">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="packageId"    column="package_id"    />
        <result property="packageTotalTimes"    column="package_total_times"    />
        <result property="packageBuyTimes"    column="package_buy_times"    />
        <result property="packageGiftTimes"    column="package_gift_times"    />
        <result property="packageNumberTimes"    column="package_number_times"    />
        <result property="packageThenPrice"    column="package_then_price"    />
        <result property="packagePrice"    column="package_price"    />
        <result property="packageReturnPrice"    column="package_return_price"    />
        <result property="returnTime"    column="return_time"    />
        <result property="packageReturnStatus"    column="package_return_status"    />
        <result property="packageAuthorizationer"    column="package_authorizationer"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="packageName"    column="package_name"    />
        <result property="packageAuthorizationerNickName"    column="package_authorizationer_nick_name"    />
        <result property="packageDuringConsumption"    column="package_during_consumption"    />
    </resultMap>

    <resultMap id="UserPackageVoMap" type="com.ruoyi.teen.vo.ipad.UserPackageVo">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="packageName"    column="package_name"    />
        <result property="packageTotalTimes"    column="package_total_times"    />
        <result property="packageBuyTimes"    column="package_buy_times"    />
        <result property="packageGiftTimes"    column="package_gift_times"    />
        <result property="packageNumberTimes"    column="package_number_times"    />
        <result property="packageThenPrice"    column="package_then_price"    />
        <result property="packagePrice"    column="package_price"    />
        <result property="packageReturnPrice"    column="package_return_price"    />
        <result property="returnTime"    column="return_time"    />
        <result property="packageReturnStatus"    column="package_return_status"    />
        <result property="packageAuthorizationer"    column="package_authorizationer"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="packageDuringConsumption"    column="package_during_consumption"    />
    </resultMap>

    <!--套餐带条件查询列表-->
    <resultMap id="RecordPackagesListVoMap" type="com.ruoyi.teen.vo.ipad.RecordPackagesListVo">
        <result property="parentTeenId"    column="id"    />
        <result property="teenName"    column="teen_name"    />
        <result property="teenAge" column="teen_age"/>
        <result property="teenSex" column="teen_sex"/>
        <result property="teenBirth"    column="teen_birth"    />
        <result property="packageCount"    column="package_count"    />
        <result property="remainingTimes"    column="remaining_times"    />
    </resultMap>

    <sql id="selectRlctUserPackageVo">
        select id, user_id, package_id, package_total_times, package_buy_times, package_gift_times,
               package_number_times, package_then_price, package_price, package_return_price, return_time,
               package_return_status, package_authorizationer, create_by, create_time, update_by, update_time,
               remark,package_during_consumption
        from rlct_user_package
    </sql>

    <select id="selectRlctUserPackageList" parameterType="RlctUserPackage" resultMap="RlctUserPackageResult">
        <include refid="selectRlctUserPackageVo"/>
        <where>
            <if test="packageTotalTimes != null "> and package_total_times = #{packageTotalTimes}</if>
            <if test="returnTime != null "> and return_time = #{returnTime}</if>
            <if test="packageReturnStatus != null "> and package_return_status = #{packageReturnStatus}</if>
            <if test="packageAuthorizationer != null "> and package_authorizationer = #{packageAuthorizationer}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>



    <select id="selectRlctUserPackagePCList" parameterType="com.ruoyi.teen.vo.pc.RlctUserPackagePCVo" resultMap="RlctUserPackagePCVoMap">
        select
            rup.id, su.user_name,rup.user_id, rup.package_id, rup.package_total_times,
            rup.package_buy_times, rup.package_gift_times,
            rup.package_number_times, rup.package_then_price,
            rup.package_price, rup.package_return_price,
            rup.return_time, rup.package_return_status,
            rup.package_authorizationer, rup.create_by,
            rup.create_time, rup.update_by, rup.update_time,
            rup.remark,rp.package_name,
            authSu.nick_name as package_authorizationer_nick_name,
            rup.package_during_consumption
        from rlct_user_package rup
        left join sys_user su on rup.user_id = su.user_id
        left join rlct_packages rp on rp.package_id = rup.package_id
        left join sys_user authSu on rup.package_authorizationer = authSu.user_id
        <where>
            <if test="packageReturnStatus != null "> and rup.package_return_status = #{packageReturnStatus}</if>
            <if test="userId != null "> and rup.user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectRlctUserPackageById" parameterType="Long" resultMap="RlctUserPackageResult">
        <include refid="selectRlctUserPackageVo"/>
        where id = #{id}
    </select>

    <insert id="insertRlctUserPackage" parameterType="RlctUserPackage" useGeneratedKeys="true" keyProperty="id">
        insert into rlct_user_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="packageTotalTimes != null">package_total_times,</if>
            <if test="packageBuyTimes != null">package_buy_times,</if>
            <if test="packageGiftTimes != null">package_gift_times,</if>
            <if test="packageNumberTimes != null">package_number_times,</if>
            <if test="packageThenPrice != null">package_then_price,</if>
            <if test="packagePrice != null">package_price,</if>
            <if test="packageReturnPrice != null">package_return_price,</if>
            <if test="returnTime != null">return_time,</if>
            <if test="packageReturnStatus != null">package_return_status,</if>
            <if test="packageAuthorizationer != null">package_authorizationer,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="packageDuringConsumption != null">package_during_consumption,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="packageTotalTimes != null">#{packageTotalTimes},</if>
            <if test="packageBuyTimes != null">#{packageBuyTimes},</if>
            <if test="packageGiftTimes != null">#{packageGiftTimes},</if>
            <if test="packageNumberTimes != null">#{packageNumberTimes},</if>
            <if test="packageThenPrice != null">#{packageThenPrice},</if>
            <if test="packagePrice != null">#{packagePrice},</if>
            <if test="packageReturnPrice != null">#{packageReturnPrice},</if>
            <if test="returnTime != null">#{returnTime},</if>
            <if test="packageReturnStatus != null">#{packageReturnStatus},</if>
            <if test="packageAuthorizationer != null">#{packageAuthorizationer},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="packageDuringConsumption != null">#{packageDuringConsumption},</if>
         </trim>
    </insert>
    <insert id="insertRlctUserPackageList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO rlct_user_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rlctUserPackageVTwoList[0].userId != null">user_id,</if>
            <if test="rlctUserPackageVTwoList[0].packageId != null">package_id,</if>
            <if test="rlctUserPackageVTwoList[0].packageThenPrice != null">package_then_price,</if>
            <if test="rlctUserPackageVTwoList[0].packagePrice != null">package_price,</if>
            <if test="rlctUserPackageVTwoList[0].packageTotalTimes != null">package_total_times,</if>
            <if test="rlctUserPackageVTwoList[0].packageBuyTimes != null">package_buy_times,</if>
            <if test="rlctUserPackageVTwoList[0].packageReturnStatus != null">package_return_status,</if>
            <if test="rlctUserPackageVTwoList[0].packageGiftTimes != null">package_gift_times,</if>
            <if test="rlctUserPackageVTwoList[0].packageNumberTimes != null">package_number_times,</if>
            <if test="rlctUserPackageVTwoList[0].packageDuringConsumption != null">package_during_consumption,</if>
            <if test="rlctUserPackageVTwoList[0].createBy != null">create_by,</if>
            <if test="rlctUserPackageVTwoList[0].createTime != null">create_time,</if>
            <if test="rlctUserPackageVTwoList[0].updateBy != null">update_by,</if>
            <if test="rlctUserPackageVTwoList[0].updateTime != null">update_time,</if>
            <if test="rlctUserPackageVTwoList[0].salesId != null">sales_id,</if>
        </trim>
        VALUES
        <foreach collection="rlctUserPackageVTwoList" item="item" separator=",">
            (
            <if test="item.userId != null">#{item.userId},</if>
            <if test="item.packageId != null">#{item.packageId},</if>
            <if test="item.packageThenPrice != null">#{item.packageThenPrice},</if>
            <if test="item.packagePrice != null">#{item.packagePrice},</if>
            <if test="item.packageTotalTimes != null">#{item.packageTotalTimes},</if>
            <if test="item.packageBuyTimes != null">#{item.packageBuyTimes},</if>
            <if test="item.packageReturnStatus != null">#{item.packageReturnStatus},</if>
            <if test="item.packageGiftTimes != null">#{item.packageGiftTimes},</if>
            <if test="item.packageNumberTimes != null">#{item.packageNumberTimes},</if>
            <if test="item.packageDuringConsumption != null">#{item.packageDuringConsumption},</if>
            <if test="item.createBy != null">#{item.createBy},</if>
            <if test="item.createTime != null">#{item.createTime},</if>
            <if test="item.updateBy != null">#{item.updateBy},</if>
            <if test="item.updateTime != null">#{item.updateTime}</if>
            <if test="item.salesId != null">#{item.salesId}</if>
            )
        </foreach>
    </insert>

    <update id="updateRlctUserPackage" parameterType="RlctUserPackage">
        update rlct_user_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="packageTotalTimes != null">package_total_times = #{packageTotalTimes},</if>
            <if test="packageBuyTimes != null">package_buy_times = #{packageBuyTimes},</if>
            <if test="packageGiftTimes != null">package_gift_times = #{packageGiftTimes},</if>
            <if test="packageNumberTimes != null">package_number_times = #{packageNumberTimes},</if>
            <if test="packageThenPrice != null">package_then_price = #{packageThenPrice},</if>
            <if test="packagePrice != null">package_price = #{packagePrice},</if>
            <if test="packageReturnPrice != null">package_return_price = #{packageReturnPrice},</if>
            <if test="returnTime != null">return_time = #{returnTime},</if>
            <if test="packageReturnStatus != null">package_return_status = #{packageReturnStatus},</if>
            <if test="packageAuthorizationer != null">package_authorizationer = #{packageAuthorizationer},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="packageDuringConsumption != null">package_during_consumption = #{packageDuringConsumption},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="userPackageReturn" parameterType="RlctUserPackage">
        update rlct_user_package
            set package_return_price = #{packageReturnPrice},
                package_return_status = #{packageReturnStatus},
                return_time = #{returnTime},
                package_return_user = #{packageReturnUser},
                update_by = #{updateBy},
                update_time = #{updateTime}
        where id  = #{id}
    </update>
    <update id="upIpadUserPackage" parameterType="Long">
        update rlct_user_package set package_during_consumption = package_during_consumption +1
        where id = #{id}
    </update>

    <update id="finishUserPackage" parameterType="Long">
        update rlct_user_package set package_during_consumption = package_during_consumption -1,
        package_number_times = package_number_times +1
        where id = #{id}
    </update>

    <update id="upIpadRollbackUserPackage">
        update rlct_user_package set package_during_consumption = package_during_consumption -1
        where id = #{id}
    </update>

    <delete id="deleteRlctUserPackageById" parameterType="Long">
        delete from rlct_user_package where id = #{id}
    </delete>

    <delete id="deleteRlctUserPackageByIds" parameterType="String">
        delete from rlct_user_package where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectUsePackage" resultMap="RlctUserPackageVoMap">
        select rup.*,rp.package_name,rp.package_image_url
        from rlct_user_package rup
                 inner join sys_user su on su.user_id = rup.user_id
                 inner join rlct_packages rp on rup.package_id = rp.package_id
        where rup.package_return_status = 0 and su.phonenumber = #{tel}
        order by rup.id desc
    </select>

    <select id="selectReturnRecordList" resultMap="RlctReturnRecordVoResult" parameterType="com.ruoyi.teen.vo.pc.RlctReturnRecordVo">
        SELECT su.nick_name,su.phonenumber,rp.package_name,rup.*
        FROM rlct_user_package rup
        LEFT JOIN sys_user su ON rup.user_id = su.user_id
        LEFT JOIN rlct_packages rp ON rup.package_id = rp.package_id
        <where>
            <if test="phonenumber != null and phonenumber != ''">phonenumber like concat('%', #{phonenumber}, '%')</if>
            and rup.package_return_status = 1
        </where>

    </select>

    <select id="selectUserPackageList" resultType="com.ruoyi.teen.vo.ipad.PadUserPackageVo" parameterType="Long">
        select rup.id value,rp.package_name text
        from rlct_user_package as rup
                 left join rlct_packages as rp on rup.package_id = rp.package_id
        where rp.del_flag = '0'
          and rup.package_total_times-rup.package_during_consumption-rup.package_number_times>0
          and rup.package_return_status = 0
          and rup.user_id = #{patientId}
        Limit 1
    </select>


    <select id="selectByTel" resultMap="UserPackageVoMap" parameterType="java.lang.String">
        select rp.package_name,rup.*
        from rlct_user_package as rup
                 left join rlct_packages as rp on rup.package_id = rp.package_id
                 left join sys_user  as su on rup.user_id = su.user_id
        where rp.del_flag = '0' and su.user_type = '22' and su.`status` = '0' and su.del_flag = '0'
          and rup.package_return_status = 0
          and su.phonenumber = #{tel}
    </select>

    <select id="selectUserPackage" resultMap="UserPackageVoMap" parameterType="Long">
        select rp.package_name,rup.*
        from rlct_user_package as rup
        left join rlct_packages as rp on rup.package_id = rp.package_id
        left join sys_user  as su on rup.user_id = su.user_id
        where rp.del_flag = '0' and su.user_type = '22' and su.`status` = '0' and su.del_flag = '0'
        and rup.package_total_times-rup.package_during_consumption-rup.package_number_times>0
        and rup.package_return_status = 0 and rup.user_id = #{deductParentId} and rup.id = #{id}
    </select>

    <select id="countNumberTimes" resultType="java.lang.Integer">
        select IFNULL(sum(t.package_number_times),0) package_number_times
            from rlct_user_package t
            inner join sys_user su on su.user_id = t.user_id
            where su.phonenumber = #{tel}
    </select>

    <select id="countLeaveTimes" resultType="java.lang.Integer">
        select IFNULL(sum((t.package_total_times - t.package_number_times)),0) leaveTimes
        from rlct_user_package t
        inner join sys_user su on su.user_id = t.user_id
        where t.package_return_status = 0
        and su.phonenumber = #{tel}
    </select>
    <select id="selectByPhonenumberNameCh" resultMap="RecordPackagesListVoMap">
        SELECT
            pt.id,
            t.teen_name,
            t.teen_age,
            t.teen_sex,
            t.teen_birth,
            COUNT(up.package_id) AS package_count, -- 套餐总数
            SUM(up.package_total_times - up.package_number_times) AS remaining_times -- 剩余总次数
        FROM
            rlct_parent_teen pt
            LEFT JOIN
            rlct_teen t ON pt.teen_id = t.teen_id
            LEFT JOIN
            sys_user u ON pt.parent_id = u.user_id
            LEFT JOIN
            rlct_user_package up ON pt.parent_id = up.user_id AND up.package_return_status = 0
        WHERE
            t.del_flag = '0'
            AND u.del_flag = '0'
            AND u.status = '0'
            <if test="(phonenumber != null and phonenumber != '') or (nameCh != null and nameCh != '')">
                AND (
                <if test="phonenumber != null and phonenumber != ''">
                    u.phonenumber LIKE CONCAT('%', #{phonenumber}, '%')
                </if>
                <if test="phonenumber != null and phonenumber != '' and nameCh != null and nameCh != ''">
                    OR
                </if>
                <if test="nameCh != null and nameCh != ''">
                    t.teen_name_ch LIKE CONCAT('%', #{nameCh}, '%')
                </if>
                )
            </if>
        <!-- 新增统一搜索条件 -->
        <if test="keyword != null and keyword != ''">
            AND (u.phonenumber LIKE CONCAT('%', #{keyword}, '%')
            OR t.teen_name_ch LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        GROUP BY pt.id, t.teen_name, t.teen_age, t.teen_sex, t.teen_birth -- 按用户分组
    </select>
    <select id="selectTreatmentTypeSelection" resultType="com.ruoyi.teen.vo.ipad.PackageBuyTimesVo">
        SELECT
            rp.package_id,
            rp.package_name
        FROM
            rlct_parent_teen rpt
                JOIN
            rlct_user_package rup ON rpt.parent_id = rup.user_id
                JOIN
            rlct_packages rp ON rup.package_id = rp.package_id
        WHERE
            rpt.id = 505
          AND rup.package_return_status = 0
    </select>
    <select id="selectDoctorUserList" resultType="com.ruoyi.teen.vo.ipad.DoctorUserVo">
        SELECT
            user_id,
            nick_name AS nickName
        FROM
            sys_user
        WHERE
            user_type = '11'
          AND del_flag = '0'
          AND status = '0';
    </select>

    <!--通过套餐关系id查询套餐剩余次数-->
    <select id="selectCheckRemainingTimes" resultType="RlctUserPackageVTwo" >
        SELECT
            package_total_times - package_number_times - package_during_consumption AS remainingTimes,
            user_id AS userId
        FROM
            rlct_user_package
        WHERE
            id = #{userPackageId}
          AND package_return_status = 0;
    </select>


</mapper>
