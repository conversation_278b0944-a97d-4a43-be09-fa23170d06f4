package com.test.transferringData;

import org.junit.jupiter.api.Test;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;

/**
 * 简单的文件上传测试类
 * 使用原生 Java HTTP 连接，不依赖 Spring 框架
 */
public class SimpleFileUploadTest {

    @Test
    public void testSimpleHttpUpload() {
        // 测试文件路径
        String filePath = "G:\\School\\javaProjectTest\\qingjianTest\\00004-X朱珂-男-13岁\\2025-06-14-首诊\\2025-06-14-首诊-图片002.jpg";
        String uploadUrl = "http://localhost:38812/teenapi/common/upload";
        
        try {
            File file = new File(filePath);
            
            // 检查文件是否存在
            if (!file.exists()) {
                System.err.println("错误：文件不存在 - " + filePath);
                System.out.println("请确保文件存在，或修改为正确的文件路径");
                return;
            }
            
            System.out.println("准备上传文件: " + file.getName() + " (大小: " + file.length() + " 字节)");
            System.out.println("上传地址: " + uploadUrl);
            
            // 调用上传方法
            uploadFile(file, uploadUrl);
            
        } catch (Exception e) {
            System.err.println("上传失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 使用原生 Java HTTP 连接上传文件
     */
    private void uploadFile(File file, String uploadUrl) throws IOException {
        String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();
        String LINE_FEED = "\r\n";
        
        URL url = new URL(uploadUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        // 设置请求属性
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
        connection.setRequestProperty("User-Agent", "Java File Upload Test");
        
        try (OutputStream outputStream = connection.getOutputStream();
             PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true)) {
            
            // 添加文件部分
            writer.append("--").append(boundary).append(LINE_FEED);
            writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"").append(file.getName()).append("\"").append(LINE_FEED);
            writer.append("Content-Type: ").append(Files.probeContentType(file.toPath())).append(LINE_FEED);
            writer.append(LINE_FEED);
            writer.flush();
            
            // 写入文件内容
            try (FileInputStream fileInputStream = new FileInputStream(file)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
            
            writer.append(LINE_FEED);
            writer.append("--").append(boundary).append("--").append(LINE_FEED);
            writer.flush();
        }
        
        // 获取响应
        int responseCode = connection.getResponseCode();
        System.out.println("HTTP 响应码: " + responseCode);
        
        // 读取响应内容
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                responseCode >= 200 && responseCode < 300 ? connection.getInputStream() : connection.getErrorStream()))) {
            
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
            
            if (responseCode >= 200 && responseCode < 300) {
                System.out.println("上传成功！");
                System.out.println("响应内容: " + response.toString());
            } else {
                System.err.println("上传失败！");
                System.err.println("错误响应: " + response.toString());
            }
        }
        
        connection.disconnect();
    }
    
    /**
     * 测试上传指定文件的方法
     */
    public void testUploadSpecificFile(String filePath) {
        String uploadUrl = "http://localhost:38812/teenapi/common/upload";
        
        try {
            File file = new File(filePath);
            
            if (!file.exists()) {
                System.err.println("错误：文件不存在 - " + filePath);
                return;
            }
            
            System.out.println("准备上传文件: " + file.getName() + " (大小: " + file.length() + " 字节)");
            uploadFile(file, uploadUrl);
            
        } catch (Exception e) {
            System.err.println("上传失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
