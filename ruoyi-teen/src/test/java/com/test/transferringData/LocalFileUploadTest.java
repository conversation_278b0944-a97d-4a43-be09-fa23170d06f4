package com.test.transferringData;


import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;

public class LocalFileUploadTest {

    @Autowired
    private ISysFileService fileService; // 若依文件上传服务

    @Test
    public void testUploadLocalFile() throws Exception {
        // 1. 本地文件路径（测试用）
        String localFilePath = "C:/temp/test.jpg";
        File file = new File(localFilePath);

        // 2. 转换为 MultipartFile（若依上传需要）
        MultipartFile multipartFile = new MockMultipartFile(
                "file",             // 参数名（必须和接口一致）
                file.getName(),     // 文件名
                "image/jpeg",       // 文件类型
                new FileInputStream(file)
        );

        // 3. 调用若依上传服务
        AjaxResult result = fileService.uploadFile(multipartFile);

        // 4. 打印结果
        System.out.println("上传结果: " + result);
    }
}
