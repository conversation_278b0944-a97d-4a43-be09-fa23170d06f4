package com.test.transferringData;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;

@SpringBootTest
public class LocalFileUploadTest {

    private RestTemplate restTemplate = new RestTemplate();

    @Test
    public void testHttpUpload() {
        try {
            // 1. 本地文件路径 - 请确保此文件存在
            String localFilePath = "C:/temp/test.jpg";
            File file = new File(localFilePath);

            // 检查文件是否存在
            if (!file.exists()) {
                System.err.println("错误：文件不存在 - " + localFilePath);
                System.out.println("请确保文件存在，或修改为正确的文件路径");
                return;
            }

            System.out.println("准备上传文件: " + file.getName() + " (大小: " + file.length() + " 字节)");

            // 2. 构造请求体（模拟 multipart/form-data）
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new FileSystemResource(file));

            // 3. 构造请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 4. 构造 HTTP 请求
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 5. 调用青健系统上传接口
            String uploadUrl = "http://localhost:38812/teenapi/common/upload";
            System.out.println("正在调用上传接口: " + uploadUrl);

            ResponseEntity<String> response = restTemplate.postForEntity(uploadUrl, requestEntity, String.class);

            // 6. 打印结果
            System.out.println("上传成功！");
            System.out.println("HTTP状态码: " + response.getStatusCode());
            System.out.println("响应内容: " + response.getBody());

        } catch (Exception e) {
            System.err.println("上传失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试上传指定文件
     * @param filePath 文件路径
     */
    public void testUploadSpecificFile(String filePath) {
        try {
            File file = new File(filePath);

            if (!file.exists()) {
                System.err.println("错误：文件不存在 - " + filePath);
                return;
            }

            System.out.println("准备上传文件: " + file.getName() + " (大小: " + file.length() + " 字节)");

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new FileSystemResource(file));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            String uploadUrl = "http://localhost:38812/teenapi/common/upload";
            System.out.println("正在调用上传接口: " + uploadUrl);

            ResponseEntity<String> response = restTemplate.postForEntity(uploadUrl, requestEntity, String.class);

            System.out.println("上传成功！");
            System.out.println("HTTP状态码: " + response.getStatusCode());
            System.out.println("响应内容: " + response.getBody());

        } catch (Exception e) {
            System.err.println("上传失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
