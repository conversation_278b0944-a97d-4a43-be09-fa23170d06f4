package com.test;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class VisTest {
    public static void main(String[] args) throws IOException {
        List<String> list = FileUtils.readLines(new File("D:/test/123.csv"));
        int id = 103;
        for (String s : list) {
            String [] ss = s.split(",");
            String sql = "INSERT INTO sys_dept (`dept_id`,`parent_id`,`ancestors`,`dept_name`,`order_num`," +
                    "`leader`,`phone`,`email`,`status`,`del_flag`," +
                    "`create_by`,`create_time`,`update_by`,`update_time`,`dept_address`,`dept_description`) " +
                    "VALUES ("+ id +",101,'0,100,101','" + ss[0] + "',"+ id +",'管理员','" + ss[2] + "','<EMAIL>','0','0','admin',now(),'',null,'"+ ss[1] +"','"+ ss[3] +"');";
            System.out.println(sql);

            id++;
        }
    }
}
