package com.test.updateTeenNameCh;

import com.ruoyi.teen.utils.PinyinUtils;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class UpdateTeenNameCh {
    public static void main(String[] args) throws IOException {

        List<String> nameList = FileUtils.readLines(new File("D:\\resourceyg2023\\瑞来春堂青少年理疗客户服务管理系统\\teen-custom-api\\ruoyi-teen\\src\\test\\java\\com\\test\\updateTeenNameCh/没有英文名的数据.txt"), "UTF-8");

        int i=0;
        for (String name : nameList) {
            if(i == 0){
                i++;
                continue;
            }
            i++;
//            System.out.println(name);
            String[] nameArr = name.split(",");
            String teenId = nameArr[0];
            String nameZw = nameArr[1];
            String teenNameCh = PinyinUtils.getFirstLetter(nameZw);
            String sql = "update rlct_teen set teen_name_ch = '" + teenNameCh + "' where teen_id = " + teenId + ";";
            System.out.println(sql);

        }


    }
}
