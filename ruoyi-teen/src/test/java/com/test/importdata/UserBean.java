package com.test.importdata;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class UserBean {
    private String phone;
    private String name;
    private String sex;
    private Date birday;
    private String pack;

    private Long packId;

    private int total;

    private int shengyu;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        if(StringUtils.isBlank(sex)){
            this.sex = "2";
        }else{
            if(sex.equals("男")){
                this.sex = "0";
            }else{
                this.sex = "1";
            }
        }
    }

    public Date getBirday() {
        return birday;
    }

    public void setBirday(String birday) {
        if (StringUtils.isBlank(birday)){
            return;
        }
        this.birday = formatDate(birday);
    }

    public Date formatDate(String dateStr) {
        try{
            if (dateStr.contains("号")){
                dateStr = dateStr.replace("号", "日");
            }

            if(dateStr.contains("年") && !dateStr.contains("日")){
                dateStr = dateStr + "日";
            }

            if(dateStr.contains(".")){
                int res = StringUtils.countMatches(dateStr, ".");
                if(res == 1){
                    dateStr = dateStr + ".1";
                }
            }



            if(dateStr.contains(".")){
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd");
                Date date = formatter.parse(dateStr);
                return date;
            }else if (dateStr.contains("-")){
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date date = formatter.parse(dateStr);
                return date;
            }else if (dateStr.contains("/")){
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
                Date date = formatter.parse(dateStr);
                return date;
            }else if (dateStr.contains("年")){
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日");
                Date date = formatter.parse(dateStr);
                return date;
            }else{
                System.out.println("有其他日期！"+dateStr);
                System.exit(0);
            }
        }catch (Exception e){
            System.out.println("出错了！"+dateStr);
            e.printStackTrace();
            System.exit(0);
        }

        return null;
    }

    public String getPack() {
        return pack;
    }

    public void setPack(String pack) {
        pack = pack.trim();
        this.pack = pack;
        if("综合助长调理".equals(pack)){
            this.packId = 6L;
        }else if("体态调理".equals(pack)){
            this.packId = 9L;
        }else if("鼻炎调理".equals(pack)){
            this.packId = 8L;
        }else if("鼻炎＋脾胃调理".equals(pack)){
            this.packId = 7L;
        }else if("体质调理".equals(pack)){
            this.packId = 9L;
        }else if("鼻炎".equals(pack)){
            this.packId = 8L;
        }else if("脾胃".equals(pack)){
            this.packId = 8L;
        }else if("脾胃/鼻炎/体质".equals(pack)){
            this.packId = 8L;
        }else if("鼻炎体态".equals(pack)){
            this.packId = 8L;
        }else if("身高综合".equals(pack)){
            this.packId = 6L;
        }else if("身高".equals(pack)){
            this.packId = 6L;
        }else if("综合调理".equals(pack)){
            this.packId = 6L;
        }else if("身高减重".equals(pack)){
            this.packId = 6L;
        }else if("身高脾胃".equals(pack)){
            this.packId = 6L;
        }else if("身高鼻炎".equals(pack)){
            this.packId = 6L;
        }else if("身高体态".equals(pack)){
            this.packId = 6L;
        }else if("身高羊屎便".equals(pack)){
            this.packId = 6L;
        }else if("身高性早熟".equals(pack)){
            this.packId = 6L;
        }else if("综合".equals(pack)){
            this.packId = 6L;
        }else if("体质/鼻炎/脾胃".equals(pack)){
            this.packId = 7L;
        }else if("鼻炎+脾胃".equals(pack)){
            this.packId = 7L;
        }else if("鼻炎脾胃".equals(pack)){
            this.packId = 7L;
        }else if("脾胃+鼻炎".equals(pack)){
            this.packId = 7L;
        }else if("体态".equals(pack)){
            this.packId = 9L;
        }else if("体态体重".equals(pack)){
            this.packId = 9L;
        }else if("脾胃调理".equals(pack)){
            this.packId = 7L;
        }else if("身高调理".equals(pack)){
            this.packId = 6L;
        }else if("小儿推拿".equals(pack)){
            this.packId = 3L;
        }
    }

    public void setBirday(Date birday) {
        this.birday = birday;
    }

    public Long getPackId() {
        return packId;
    }

    public void setPackId(Long packId) {
        this.packId = packId;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getShengyu() {
        return shengyu;
    }

    public void setShengyu(int shengyu) {
        this.shengyu = shengyu;
    }
}
