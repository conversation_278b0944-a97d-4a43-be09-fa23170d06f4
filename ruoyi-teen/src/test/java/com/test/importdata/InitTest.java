package com.test.importdata;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class InitTest {
    public static void main(String[] args) throws IOException {

        List<String> list = FileUtils.readLines(new File("D:\\temp/小程序培训签到表20240520.csv"), "UTF-8");

        for (String s : list) {
            if(s.contains("序号,姓名")){
                continue;
            }
//            System.out.println(s);
            String[] arr = s.split(",",-1);

            String arr5 = arr[5];
            String userType = "11";
            if("主管".equals(arr5)){
                userType = "00";
            }

            String sql = "INSERT INTO `teen`.`sys_user` (`dept_id`, `user_name`, `nick_name`, `user_type`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `openid`) " +
                    " VALUES ("+ arr[4] +", '" + arr[2] + "', '" + arr[1] + "', '" + userType + "', '', '" + arr[2] + "', '0', '', '$2a$10$9jdO48LX2MG5XAwKw510R.IG15uo4oF4s2Udxl.dgsWSgJ7QZ5d4i', '0', '0', null, null, 'admin', now(), '', now(), NULL, NULL);";
            System.out.println(sql);

        }


    }
}
