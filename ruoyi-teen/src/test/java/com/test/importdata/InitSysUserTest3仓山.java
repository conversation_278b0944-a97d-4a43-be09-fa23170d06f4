package com.test.importdata;

import com.ruoyi.teen.utils.ValidateUtils;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InitSysUserTest3仓山 {
    public static void main(String[] args) throws IOException {

//        List<String> list = FileUtils.readLines(new File("D:\\resourcecommon\\componentlib\\utils\\src\\test\\java\\com\\rlct/家长数据（导入）.csv"), "UTF-8");

        //数据库里的所有用户数据
        //SELECT CONCAT(t.user_id,',',t.user_name) str from sys_user t
        List<SysUserBean> resPhones = new ArrayList<>();
        List<String> sysUserList = FileUtils.readLines(new File("D:\\resourceyg2023\\瑞来春堂青少年理疗客户服务管理系统\\teen-custom-api\\ruoyi-teen\\src\\test\\java\\com\\test\\importdata/系统用户表数据.txt"), "UTF-8");
        for (String s : sysUserList) {
            String[] split = s.split(",",-1);
            String userId = split[0];
            String phone = split[1];
//            System.out.println(phone);
            resPhones.add(new SysUserBean(Long.parseLong(userId),phone));
        }






        String path = "D:\\resourceyg2023\\瑞来春堂青少年理疗客户服务管理系统\\teen-custom-api\\ruoyi-teen\\src\\test\\java\\com\\test\\importdata/仓山馆-生长发育套餐客户数据24.12.29.csv";
        List<String> list = FileUtils.readLines(new File(path), "UTF-8");

        int sysUserId = 4000; //用户ID的开始序号
        int teenId = 2700; //teenID的开始序号

        String userIdsPrint = "";
        Map<String,List<UserBean>> map = new HashMap<>();
        for (String s : list) {
//            System.out.println(s);
            try {
                if(s.contains("*手机号")){
                    continue;
                }
                String[] attr = s.split(",",-1);
//            System.out.println(attr.length);
                if(attr[0].equals("1")){
                    continue;
                }
//                System.out.println("------------------------------------------");
                String phone = attr[1];
                String name = attr[2];
                String sex = attr[4];
                String birday = attr[3];
                String pack = attr[5];
                String total = attr[6];
                String shengyu = attr[7];

                if (shengyu.contains("/")){
                    String[] shengyuArr = shengyu.split("/");
                    shengyu = shengyuArr[0];
                    total = shengyuArr[1];
                }

                ValidateUtils.checkString(phone,"phone");
                ValidateUtils.checkString(name,"name");
                ValidateUtils.checkString(pack,"pack");
                ValidateUtils.checkString(total,"total");
                ValidateUtils.checkString(shengyu,"shengyu");



//                System.out.println(s);
//                System.out.println(phone);
//                System.out.println(name);
//                System.out.println(shengyu);
//                System.out.println(pack);

                UserBean bean = new UserBean();
                bean.setBirday(birday);
                bean.setPhone(phone);
                bean.setName(name);
                bean.setSex(sex);
                bean.setPack(pack);
                bean.setTotal(Integer.parseInt(total));
                bean.setShengyu(Integer.parseInt(shengyu));
                ValidateUtils.checkLong(bean.getPackId(),"packId");

                if(bean.getBirday() != null){
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//                    System.out.println(formatter.format(bean.getBirday()));
                }

                putMap(map,bean);
            }catch (Exception e){
                e.printStackTrace();
                System.out.println("--->"+s);
                System.exit(0);
            }

        }

        String phones = "";
        for(Map.Entry<String,List<UserBean>> entry:map.entrySet()){
            String key = entry.getKey();
            List<UserBean> list1 = entry.getValue();
//            System.out.println("key="+key);


            SysUserBean userBean = findResPhones(resPhones,key);
            if(userBean != null){
//                System.out.println("phone="+key+",userId="+userBean.getUserId());
                phones += "'"+key + "',";

                //系统中已有的客户数据

                int consume2 = list1.get(0).getTotal() - list1.get(0).getShengyu();
                String sql1 = "UPDATE rlct_user_package SET package_total_times = " + list1.get(0).getTotal()
                        +", package_buy_times=" +  list1.get(0).getTotal()
                        +", package_number_times=" +  consume2
                        +", package_gift_times=0"
                        +", package_during_consumption=0" +
                        " where " + " user_id =" + userBean.getUserId() + ";";
//                System.out.println(sql1);

                userIdsPrint += userBean.getUserId() + ",";

                continue;
            }

            String sql = "INSERT INTO `sys_user` (`user_id`,`dept_id`, `user_name`, `nick_name`, `user_type`, `email`, `phonenumber`, `sex`, `avatar`, `password`, `status`, `del_flag`, `login_ip`, `login_date`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `openid`) " +
                    " VALUES ('" + sysUserId + "',null, '" + key + "', '微信用户', '22', '', '" + key + "', '0', '', '$2a$10$9jdO48LX2MG5XAwKw510R.IG15uo4oF4s2Udxl.dgsWSgJ7QZ5d4i', '0', '0', null, null, 'admin', now(), '', now(), NULL, NULL);";
            System.out.println(sql);



            UserBean lastBean = null;
            for (UserBean bean : list1) {
                lastBean = bean;
                String birda = "NULL";
                if(bean.getBirday() != null){
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                    birda = "'" + formatter.format(bean.getBirday()) + "'";
//                    System.out.println(formatter.format(bean.getBirday()));
                }
                String sql2 = "INSERT INTO `rlct_teen` " +
                        "(`teen_id`, `parent_id`, `teen_name`, `teen_age`" +
                        ", `teen_avatar`, `teen_sex`, `teen_birth`" +
                        ", `teen_sleep_time`, `teen_sleep_duration_time`" +
                        ", `teen_exercise_habit`, `teen_eat_habit`" +
                        ", `teen_nutrient`, `del_flag`, `create_by`" +
                        ", `create_time`, `update_by`, `update_time`" +
                        ", `remark`, `init_height`, `init_weight`" +
                        ", `init_time`, `teen_name_ch`) VALUES " +
                        "("+ teenId +", "+ sysUserId +", '" + bean.getName() + "', 0, NULL, '" + bean.getSex()
                        + "', "+ birda +", 0.00, 0.00, " +
                        "0.00, 0.00, 0.00, '0', '', now(), ''" +
                        ", NULL, NULL, NULL, NULL, NULL, null);";
                System.out.println(sql2);

                String sql3 = "INSERT INTO `rlct_parent_teen` " +
                        "(`parent_id`, `teen_id`, `create_time`) VALUES ( "+ sysUserId +", " + teenId + ", now());";
                System.out.println(sql3);
                teenId++;
            }

            //消费次数
            int consume = lastBean.getTotal() - lastBean.getShengyu();
            String sql4 = "INSERT INTO `rlct_user_package` (`user_id`, `package_id`, " +
                    "`package_total_times`, `package_buy_times`, `package_gift_times`, `package_number_times`, " +
                    "`package_during_consumption`, `package_then_price`, `package_price`, `package_return_price`, " +
                    "`return_time`, `package_return_status`, `package_authorizationer`, `create_by`, `create_time`, " +
                    "`update_by`, `update_time`, `remark`, `package_return_user`) VALUES " +
                    "("+ sysUserId +", " + lastBean.getPackId() + ", " + lastBean.getTotal()
                    + ", " + lastBean.getTotal() + ", 0, "+ consume +", 0, 0, 0, 0, " +
                    "null,0, NULL, '', now(), ''," +
                    " now(), NULL, NULL);";
            System.out.println(sql4);



            sysUserId++;
        }

//        System.out.println(phones);
//        System.out.println(resPhones.size());
//        System.out.println(userIdsPrint);

    }


    private static SysUserBean findResPhones(List<SysUserBean> list,String phone) throws IOException {
        for (SysUserBean bean : list) {
            if(bean.getUserName().equals(phone)){
                return bean;
            }
        }
        return null;
    }

    private static Map<String,List<UserBean>> putMap(Map<String,List<UserBean>> map,UserBean bean){
        String key = bean.getPhone();
        List<UserBean> list = map.get(key);
        if(list == null){
            list = new ArrayList<>();
            list.add(bean);
            map.put(key,list);
        }else{
            list.add(bean);
        }
        return map;
    }
}
