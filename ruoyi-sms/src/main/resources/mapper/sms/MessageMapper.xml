<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.sms.mapper.MessageMapper">

    <update id="smsEdit">
        update sms_message set cur_send_times = #{curSendTimes},state=#{state},send_time=#{sendTime} where id=#{id}
    </update>

    <update id="editSms">
        update sms_message set cur_send_times = #{curSendTimes},state=#{state},send_time=#{sendTime}
        ,next_send_time=#{nextSendTime},error_msg=#{errorMsg} where id=#{id}
    </update>

    <update id="editState">
        update sms_message set state=#{state} where id=#{id}
    </update>

    <select id="mesList" resultType="com.ruoyi.sms.domain.Message">
        select * from sms_message
        where (state = 3 and max_send_times>cur_send_times and (NOW()>=next_send_time or next_send_time is null))
        or state = 0
        or (state = 1 and max_send_times>cur_send_times and NOW()+INTERVAL 2 HOUR>=next_send_time)
    </select>
</mapper>
