package com.ruoyi.sms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sms.domain.Message;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
public interface MessageMapper extends BaseMapper<Message> {

    /**
     * 获取失败消息记录
     * @return
     */
    List<Message> mesList();
    /**
     * 更新成功的状态
     * @return
     */
    int smsEdit(@Param("state")Integer state,@Param("id")Integer id,@Param("curSendTimes") Integer curSendTimes,@Param("sendTime") Date sendTime);
    /**
     * 更新失败的状态和下一次发送时间
     * @return
     */
    int editSms(@Param("state") Integer state,@Param("id") Integer id,@Param("sendTime") Date sendTime,@Param("nextSendTime") Date nextSendTime,@Param("curSendTimes") Integer curSendTimes,@Param("errorMsg") String errorMsg);
    /**
     * 更新正在发送的状态
     * @return
     */
    int editState(@Param("state") Integer state, @Param("id") Integer id);

}
