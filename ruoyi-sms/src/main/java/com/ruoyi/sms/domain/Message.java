package com.ruoyi.sms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sms_message")
public class Message implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 短信
     */
    private String message;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 最大发送次数 默认3
     */
    private Integer maxSendTimes;

    /**
     * 当前发送次数
     */
    private Integer curSendTimes;

    /**
     * 下一次发送时间
     */
    private Date nextSendTime;

    /**
     * 间隔周期(单位s)
     */
    private Integer intervalCycle;

    /**
     * 失败信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态,0待发送，1发送中，2发送成功，3发送失败
     */
    private Integer state;


}
