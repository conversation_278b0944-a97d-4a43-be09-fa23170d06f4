package com.ruoyi.sms.service.impl;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.sms.domain.Message;
import com.ruoyi.sms.mapper.MessageMapper;
import com.ruoyi.sms.platform.ISmSPlatform;
import com.ruoyi.sms.platform.SmsPlatformFactory;
import com.ruoyi.sms.service.MessageService;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Service
@Transactional //开启事务
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);
    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private SmsPlatformFactory smsPlatformFactory;






    @Override
    public int sendSms(String phone, String ... message) {
        ISmSPlatform smSPlatform = smsPlatformFactory.createSmsPlatform();
        Message mes = smSPlatform.builderMessage(phone,message);
        String str = sendSms(mes);
        //发送成功这条数据state=2 失败state=3 否则待发送0 0发送的时候要处理
        int result;
        if (str == null){
            mes.setState(2);
            result = messageMapper.insert(mes);
        }else{
            mes.setState(3);
            //下次发送时间更新
            Date nextSendTime = DateUtils.addMinutes(mes.getSendTime(),mes.getIntervalCycle());
            mes.setNextSendTime(nextSendTime);
            mes.setErrorMsg(getError_msg(str));
            result = messageMapper.insert(mes);
            logger.info("错误信息",str);
        }
        return result;
    }


    /**
     * 设置定时任务捞取发送失败的消息
     */

    @Override
    public void sendAll(){
        String str;
        int result;
        List<Message> list = mesList();
        //循环发送 发送次数加1 发送成功下次发送时间不更新，发送失败下次发送时间更新
        List<Message> messageList=new ArrayList<>();
        for(int i=0;i<list.size();i++) {
            result = editState(1, list.get(i).getId());
            if (result > 0) {
                messageList.add(list.get(i));
            }
        }
        for (Message message : messageList) {
            str = sendSms(message);
            message.setCurSendTimes(message.getCurSendTimes()+1);
            if (str == null) {
                logger.info("发送成功",message.getPhone(),message.getMessage());
                smsEdit(2, message.getId(),message.getCurSendTimes(),message.getSendTime());
            } else {
                logger.info("发送失败",message.getPhone(),"错误信息",message.getErrorMsg());
                Date nextSendTime = DateUtils.addMinutes(message.getSendTime(),message.getIntervalCycle());
                editSms(3, message.getId(),message.getSendTime(),nextSendTime,message.getCurSendTimes(),str);
            }
        }

    }

//    @Override
    public String sendSms(Message message){
        logger.info("message="+ JSONObject.toJSONString(message));
        return smsPlatformFactory.createSmsPlatform().sendSms(message);
    }


    /**
     * 对错误信息处理
     * @param error_msg
     * @return
     */
    private String getError_msg(String error_msg) {
        if (error_msg.length()>1024){
            error_msg = error_msg.substring(0,1024);
        }
        return error_msg;
    }

    /**
     * 获取失败消息列表
     * @return
     */
    public List<Message> mesList() {
        return messageMapper.mesList();
    }


    /**
     * 消息发送成功处理
     * @param state
     * @param id
     * @param cur_send_times
     * @param send_time
     * @return
     */
    public int smsEdit(Integer state,Integer id,Integer cur_send_times,Date send_time){
        return messageMapper.smsEdit(state,id,cur_send_times,send_time);
    }

    /**
     * 消息发送失败处理
     * @param state 状态
     * @param id
     * @param send_time 发送时间
     * @param next_send_time 下一次发送时间
     * @param cur_send_times 当前发送次数
     * @param Error_msg 错误信息
     * @return
     */
    public int editSms(Integer state,Integer id,Date send_time,Date next_send_time,Integer cur_send_times,String Error_msg) {
        return messageMapper.editSms(state,id,send_time,next_send_time,cur_send_times,Error_msg);
    }


    /**
     * 获取到消息后状态更新为发送中 state=1
     * @param state
     * @param id
     * @return
     */
    public int editState(Integer state, Integer id) {
        return messageMapper.editState(state,id);
    }
}
