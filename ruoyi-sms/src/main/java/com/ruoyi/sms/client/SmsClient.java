package com.ruoyi.sms.client;

import com.ruoyi.sms.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SmsClient {

    public static MessageService messageService;


    /**
     * 发送腾讯短信验证码
     * @param phoneNumber
     * @param code
     * @return
     */
    public static void sendSmsCode(String phoneNumber, String code) {
        messageService.sendSms(phoneNumber,code);
    }



    @Autowired
    public void setMessageService(MessageService messageService) {
        SmsClient.messageService = messageService;
    }
}
