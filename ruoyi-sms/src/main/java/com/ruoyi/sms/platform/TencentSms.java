package com.ruoyi.sms.platform;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.sms.config.SmsConfig;
import com.ruoyi.sms.domain.Message;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
@Primary   //默认使用本地存储
@Component
public class TencentSms implements ISmSPlatform {
    private static final Logger logger = LoggerFactory.getLogger(TencentSms.class);

    @Autowired
    private SmsConfig smsConfig;

    @Override
    public Message builderMessage(String phone, String... message) {
        Message msg = new Message();
        msg.setPlatform(1);//1表示腾讯云
        msg.setPhone(phone);
        msg.setSendTime(new Date());
        msg.setCurSendTimes(1);
        msg.setMaxSendTimes(smsConfig.getMaxSendTimes());
        msg.setIntervalCycle(smsConfig.getIntervalCycle());
        msg.setCreateTime(new Date());

        JSONObject jo = new JSONObject();
        jo.put("secretId", smsConfig.getSecretId());
        jo.put("secretKey", smsConfig.getSecretKey());
        jo.put("smsSdkAppId",smsConfig.getSmsSdkAppId());
        jo.put("signName",smsConfig.getSignName());
        jo.put("templateId",smsConfig.getTemplateId());

        List<String> templateParamSet = new ArrayList<>();
        for (String s : message) {
            templateParamSet.add(s);
        }
        templateParamSet.add("5");
        jo.put("templateParamSet", templateParamSet.toArray(new String[0]));
        msg.setMessage(jo.toJSONString());
        return msg;
    }

    @Override
    public String sendSms(Message message) {
        try{
            String msg = message.getMessage();
            JSONObject msgJo = JSONObject.parseObject(msg,JSONObject.class);
            String secretId = msgJo.getString("secretId");
            String secretKey = msgJo.getString("secretKey");
            String smsSdkAppId = msgJo.getString("smsSdkAppId");
            String signName = msgJo.getString("signName");
            String templateId = msgJo.getString("templateId");
            List<String> templateParamSet = msgJo.getList("templateParamSet",String.class);

            return sendSMS(message.getPhone(),secretId,secretKey,smsSdkAppId,templateId,signName,templateParamSet.toArray(new String[0]));
        }catch (Exception e){
            return "error!" + e.getMessage();
        }
    }

    private String sendSMS(String phone,String secretId,String secretKey,String smsSdkAppId,String templateId,String signName,String[] templateParamSet1){
        String msg=null;
        try{
            logger.info("TencentSms info,phone="+phone+",templateId="+templateId+",signName="+signName+",templateParamSet1="+ JSON.toJSONString(templateParamSet1));
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(secretId, secretKey);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("sms.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            SmsClient client = new SmsClient(cred, "ap-guangzhou", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            SendSmsRequest req = new SendSmsRequest();
            //短信 SdkAppId
            req.setSmsSdkAppId(smsSdkAppId);
            //模板 ID
            req.setTemplateId(templateId);
            //短信签名内容
            req.setSignName(signName);
            String[] phoneNumber = {phone};
            req.setPhoneNumberSet(phoneNumber);
            req.setTemplateParamSet(templateParamSet1);
            //用户的 session 内容，可以携带用户侧 ID 等上下文信息，server 会原样返回。
            req.setSessionContext("1");
            // 返回的resp是一个SendSmsResponse的实例，与请求对象对应
            SendSmsResponse resp = client.SendSms(req);
            String resMessage = SendSmsResponse.toJsonString(resp);
            logger.info("输出json格式的字符串回包:phone="+phone + "," + resMessage);
            if(!resp.getSendStatusSet()[0].getCode().equalsIgnoreCase("Ok")){
                 return resMessage;
            }

        } catch (TencentCloudSDKException e) {
            logger.info("Message:",e.toString());
            logger.error("TencentSms error,phone="+phone+",templateId="+templateId+",signName="+signName+",templateParamSet1="+ JSON.toJSONString(templateParamSet1),e);
            msg= "error!"+e.getMessage();
        }
        return msg;
    }
}
