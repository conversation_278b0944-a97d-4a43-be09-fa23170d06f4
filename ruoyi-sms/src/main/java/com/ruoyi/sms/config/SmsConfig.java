package com.ruoyi.sms.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * 读取代码生成相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "sms")
@PropertySource(value = { "classpath:sms.yml" }, encoding="UTF-8")
public class SmsConfig {

    @Value("${maxSendTimes}")
    public  int maxSendTimes;
    @Value("${intervalCycle}")
    public  int intervalCycle;
    @Value("${SecretId}")
    public String SecretId;
    @Value("${SecretKey}")
    public String SecretKey;
    @Value("${SmsSdkAppId}")
    public String SmsSdkAppId;
    @Value("${SignName}")
    public String SignName;
    @Value("${TemplateId}")
    public String TemplateId;

    public String getSecretId() {
        return SecretId;
    }

    public void setSecretId(String secretId) {
        SecretId = secretId;
    }

    public String getSecretKey() {
        return SecretKey;
    }

    public void setSecretKey(String secretKey) {
        SecretKey = secretKey;
    }

    public String getSmsSdkAppId() {
        return SmsSdkAppId;
    }

    public void setSmsSdkAppId(String smsSdkAppId) {
        SmsSdkAppId = smsSdkAppId;
    }

    public String getSignName() {
        return SignName;
    }

    public void setSignName(String signName) {
        SignName = signName;
    }

    public String getTemplateId() {
        return TemplateId;
    }

    public void setTemplateId(String templateId) {
        TemplateId = templateId;
    }

    public int getMaxSendTimes() {
        return maxSendTimes;
    }

    public void setMaxSendTimes(int maxSendTimes) {
        this.maxSendTimes = maxSendTimes;
    }

    public int getIntervalCycle() {
        return intervalCycle;
    }

    public void setIntervalCycle(int intervalCycle) {
        this.intervalCycle = intervalCycle;
    }
}
