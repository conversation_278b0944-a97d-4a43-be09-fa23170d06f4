package com.ruoyi.sms.until;
import com.ruoyi.sms.service.MessageService;
import com.ruoyi.sms.service.impl.MessageServiceImpl;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Component
public class QuarzUntil {

    private static final Logger logger = LoggerFactory.getLogger(QuarzUntil.class);

    private MessageService messageService;

    @Autowired
    public QuarzUntil(MessageService messageService) {
        this.messageService = messageService;
    }
    /**
     * 每五秒执行一次
     */
    @SneakyThrows
//    @Scheduled(cron = "*/30 * * * * ?")
    private void printNowDate(){
        logger.info("定时器启动");
        messageService.sendAll();
    }
}
