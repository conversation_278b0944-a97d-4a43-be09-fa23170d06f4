/*
 Navicat Premium Data Transfer

 Source Server         : ***************(开发测试)
 Source Server Type    : MySQL
 Source Server Version : 80031 (8.0.31)
 Source Host           : ***************:33306
 Source Schema         : ruoyi

 Target Server Type    : MySQL
 Target Server Version : 80031 (8.0.31)
 File Encoding         : 65001

 Date: 24/10/2023 16:59:32
*/



-- ----------------------------
-- Table structure for sms_message
-- ----------------------------
DROP TABLE IF EXISTS `sms_message`;
CREATE TABLE `sms_message`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短信',
  `send_time` datetime NULL DEFAULT NULL COMMENT '发送时间',
  `max_send_times` int NULL DEFAULT NULL COMMENT '最大发送次数',
  `cur_send_times` int NULL DEFAULT NULL COMMENT '当前发送次数',
  `next_send_time` datetime NULL DEFAULT NULL COMMENT '下次发送时间',
  `interval_cycle` int NULL DEFAULT NULL COMMENT '间隔发送周期(单位s)',
  `error_msg` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送失败原因',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `state` int NULL DEFAULT NULL COMMENT '状态：0待发送，1发送中，2发送成功，3发送失败',
  PRIMARY KEY (`id`) USING BTREE
) AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='短信发送表';
