package com.ruoyi.system.service.impl;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.mapper.SysConfigMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysLoginService;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;

/**
 * 参数配置 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysLoginServiceImpl implements ISysLoginService
{
    private static final Logger logger = LoggerFactory.getLogger(SysLoginServiceImpl.class);
    @Autowired
    private ISysUserService userService;


    /**
     * 获取用户，如果不存在则创建（获取家长和医师）
     * @param phoneNumber
     * @param openId
     * @return
     */
    @Override
    public SysUser getLoginUser(String phoneNumber,String openId) {
        // 家长
        SysUser sysUser = userService.selectOne(phoneNumber,null);

        if(sysUser == null){
            sysUser = createUserIfNotExist(phoneNumber,"patient");
        }

        if(!sysUser.getStatus().equals("0")){
            throw new RuntimeException("该账号已停用");
        }

        if(StringUtils.isNotBlank(openId)){
            if(StringUtils.isNotBlank(sysUser.getOpenid())){
                if(!openId.equalsIgnoreCase(sysUser.getOpenid())){
                    throw new RuntimeException("该手机号非当前绑定微信号");
                }
            }else{
                //当前用户没有opnId,更新openId
                userService.updateOpenId(sysUser.getUserId(),openId);
            }
        }

        return sysUser;
    }


    private SysUser createUserIfNotExist(String phoneNumber,String type) {
        // 如果用户不存在，创建新用户
        SysUser user = new SysUser();
        user.setUserName(phoneNumber);
//        user.setNickName("phoneid_" + RandomStringUtils.randomAlphanumeric(14));
        user.setNickName("微信用户");
        user.setPhonenumber(phoneNumber);
        // 如果是家长
        if ("patient".equals(type)){
            // 设置家长类型
            user.setUserType("22");
            user.setStatus("0");
        }
        // 如果是医师
        if ("physician".equals(type)){
            // 设置医师类型、未申请状态
            user.setUserType("11");
            user.setStatus("3");
        }
        user.setPassword(SecurityUtils.encryptPassword("Try@888888"));//默认密码
        userService.insertUser(user);
        // 记录日志，说明成功创建新用户
        logger.info("成功创建新用户: {}", phoneNumber);
        return user;
    }
}
