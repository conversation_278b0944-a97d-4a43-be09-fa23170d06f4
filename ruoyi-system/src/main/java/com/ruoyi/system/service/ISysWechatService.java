package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;

public interface ISysWechatService {
    /**
     * 微信登录接口
     *
     * @param code 用户授权码
     * @param type 用户类型 patient家长   physician医师
     * @return 操作结果
     * @throws IOException 发送请求或解析响应发生异常时抛出
     */
    SysUser handleWechatLogin(String code) throws Exception;


    public String getOpenIdByCodeForPhysician(String code);
    public String getPhoneByCodeForPhysician(String code);
    public String getPhoneByOpenIdPhysician(String openId);


    public String getOpenIdByCodeForPatient(String code);
    public String getPhoneByCodeForPatient(String code);
    public String getPhoneByOpenIdPatient(String openId);
}
