package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.service.ISysLoginService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysWechatService;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;

@Service
public class SysWechatServiceImpl implements ISysWechatService {
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysLoginService loginService;
    private static final Logger logger = LoggerFactory.getLogger(SysWechatServiceImpl.class);
    /**
     * 微信小程序AppID
     */
    @Value("${wx.app-id}")
    private String appid;
    /**
     * 微信小程序AppSecret
     */
    @Value("${wx.app-secret}")
    private String secret;


    @Value("${wx.app-id-physician}")
    private String appidPhysician;
    /**
     * 微信小程序AppSecret
     */
    @Value("${wx.app-secret-physician}")
    private String secretPhysician;

    /**
     * 微信登录接口
     *
     * @param code 用户授权码
     * @param type 用户类型 patient家长   physician医师
     * @return 操作结果
     * @throws IOException 发送请求或解析响应发生异常时抛出
     */
    @Override
    public SysUser handleWechatLogin(String code) throws Exception {
        try {
            // 获取微信小程序的 Access Token
            String token = getAccessToken(null);
            // 构建获取用户手机号的 URL
            String url = String.format("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s", token);

            // 构建请求体数据
            String requestBody = "{\"code\":\"" + code + "\"}";

            // 发送 POST 请求获取用户手机号
            String response = sendPostRequest(url, requestBody);

            // 解析 JSON 响应
            JSONObject phoneNumberJsonObject = JSONObject.parseObject(response);

            // 获取 errcode 的值
            int errcode = phoneNumberJsonObject.getIntValue("errcode");

            if (errcode != 0) {
                // 处理错误码的逻辑
                logger.error("获取手机号码失败，errcode: {}", errcode);
                throw  new RuntimeException("获取手机号码失败");
            }

            // 提取手机号码信息
            JSONObject phone = JSONObject.parseObject(phoneNumberJsonObject.getString("phone_info"));
            String phoneNumber = phone.getString("purePhoneNumber");

            // 记录日志，说明获取手机号码成功
            logger.info("成功获取用户手机号码: {}", phoneNumber);

            // 检查用户是否已存在，如果不存在，则创建新用户
            SysUser sysUser = loginService.getLoginUser(phoneNumber,null);

            // 返回操作成功
            return sysUser;

        } catch (IOException e) {
            // 记录日志，便于调试和追踪错误
            logger.error("微信登录接口发生异常", e);
            throw e; // 将异常重新抛出，使得调用者能够处理
        }
    }

    /**
     * 获取微信小程序的 Access Token
     *
     * @return Access Token
     */
    public String getAccessToken(String type) {
        try {
            // 构建获取 Access Token 的URL
            String accessTokenUrl = null;
            if (StringUtils.isBlank(type) || "patient".equals(type)) {
                accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + secret;
            }else if ("physician".equals(type)) {
                accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appidPhysician + "&secret=" + secretPhysician;
            }

            // 发起 GET 请求获取 Access Token
            String accessTokenJson = HttpUtils.sendGet(accessTokenUrl);

            // 解析 JSON 响应
            JSONObject accessTokenJsonObject = JSONObject.parseObject(accessTokenJson);

            // 从 JSON 响应中提取 Access Token
            String accessToken = (String) accessTokenJsonObject.get("access_token");

            // 记录日志，以便跟踪 Access Token 获取情况
            logger.info("成功获取微信小程序的 Access Token: {}", accessToken);

            return accessToken;
        } catch (Exception e) {
            // 记录日志，以便跟踪异常情况
            logger.error("获取微信小程序的 Access Token 失败", e);
            return null;
        }
    }


    /**
     * 发送 HTTP POST 请求 获取登录手机号码
     *
     * @param url         请求的 URL
     * @param requestBody 请求体数据
     * @return 响应数据
     * @throws IOException 发送请求发生异常时抛出
     */
    private static String sendPostRequest(String url, String requestBody) throws IOException {
        HttpURLConnection connection = null;
        try {
            // 创建URL对象
            URL apiUrl = new URL(url);

            // 打开连接
            connection = (HttpURLConnection) apiUrl.openConnection();

            // 设置请求方法为POST
            connection.setRequestMethod("POST");

            // 设置请求头部信息
            connection.setRequestProperty("Content-Type", "application/json");

            // 允许输出数据
            connection.setDoOutput(true);

            // 将请求体数据写入输出流
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes();
                os.write(input, 0, input.length);
            }

            // 读取响应数据
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            }
        } catch (IOException e) {
            // 记录日志，便于调试和追踪错误
            logger.error("发送POST请求发生异常", e);
            throw e; // 将异常重新抛出，使得调用者能够处理
        } finally {
            if (connection != null) {
                // 关闭连接
                connection.disconnect();
            }
        }
    }

    /**
     * 如果用户不存在，则创建新用户
     *
     * @param phoneNumber 用户手机号码
     * @param type        用户类型 patient家长   physician医师
     * @return
     */
    private SysUser createUserIfNotExist(String phoneNumber, String type) {
        // 如果用户不存在，创建新用户
        SysUser user = new SysUser();
        user.setUserName(phoneNumber);
        user.setNickName("wxid_" + RandomStringUtils.randomAlphanumeric(14));
        user.setPhonenumber(phoneNumber);

        // 如果是家长
        if ("patient".equals(type)) {
            // 设置家长类型
            user.setUserType("22");
        }
        // 如果是医师
        if ("physician".equals(type)) {
            // 设置医师类型、未申请状态
            user.setUserType("11");
            user.setStatus("3");
        }
        user.setPassword(SecurityUtils.encryptPassword("123456"));//默认密码
        userService.insertUser(user);
        // 记录日志，说明成功创建新用户
        logger.info("成功创建新用户: {}", phoneNumber);
        return user;
    }


    @Override
    public String getOpenIdByCodeForPhysician(String code){
        String accessTokenUrl = "https://api.weixin.qq.com/sns/jscode2session?grant_type=authorization_code&appid=" + appidPhysician + "&secret=" + secretPhysician+"&js_code="+code;

        logger.info("getOpenIdByCode.url="+accessTokenUrl);
        // 发起 GET 请求获取 Access Token
        String accessTokenJson = HttpUtils.sendGet(accessTokenUrl);
        // 解析 JSON 响应
        JSONObject accessTokenJsonObject = JSONObject.parseObject(accessTokenJson);

        // 从 JSON 响应中提取 Access Token
        String openid = (String) accessTokenJsonObject.get("openid");
        return openid;
    }

    @Override
    public String getPhoneByCodeForPhysician(String code){
        String openId = getOpenIdByCodeForPhysician(code);
        if(StringUtils.isBlank(openId)){
            throw new RuntimeException("获取openId失败");
        }

        List<SysUser> userList = userService.selectUserByOpenId(openId,"11");
        if(userList == null || userList.size() == 0 || userList.size() > 1){
            return null;
        }
        return userList.get(0).getUserName();
    }

    @Override
    public String getPhoneByOpenIdPhysician(String openId){
        List<SysUser> userList = userService.selectUserByOpenId(openId,"11");
        if(userList == null || userList.size() == 0 || userList.size() > 1){
            return null;
        }
        return userList.get(0).getUserName();
    }


    @Override
    public String getOpenIdByCodeForPatient(String code){
        String accessTokenUrl = "https://api.weixin.qq.com/sns/jscode2session?grant_type=authorization_code&appid=" + appid + "&secret=" + secret+"&js_code="+code;

        logger.info("getOpenIdByCode.url="+accessTokenUrl);
        // 发起 GET 请求获取 Access Token
        String accessTokenJson = HttpUtils.sendGet(accessTokenUrl);
        // 解析 JSON 响应
        JSONObject accessTokenJsonObject = JSONObject.parseObject(accessTokenJson);

        // 从 JSON 响应中提取 Access Token
        String openid = (String) accessTokenJsonObject.get("openid");
        return openid;
    }

    @Override
    public String getPhoneByCodeForPatient(String code){
        String openId = getOpenIdByCodeForPatient(code);
        if(StringUtils.isBlank(openId)){
            throw new RuntimeException("获取openId失败");
        }

        List<SysUser> userList = userService.selectUserByOpenId(openId,"22");
        if(userList == null || userList.size() == 0 || userList.size() > 1){
            return null;
        }
        return userList.get(0).getUserName();
    }

    @Override
    public String getPhoneByOpenIdPatient(String openId){
        List<SysUser> userList = userService.selectUserByOpenId(openId,"22");
        if(userList == null || userList.size() == 0 || userList.size() > 1){
            return null;
        }
        return userList.get(0).getUserName();
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getAppidPhysician() {
        return appidPhysician;
    }

    public void setAppidPhysician(String appidPhysician) {
        this.appidPhysician = appidPhysician;
    }

    public String getSecretPhysician() {
        return secretPhysician;
    }

    public void setSecretPhysician(String secretPhysician) {
        this.secretPhysician = secretPhysician;
    }
}
